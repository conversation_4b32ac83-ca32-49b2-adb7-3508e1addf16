#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 异步模块导入修复已合并到店铺后台登录模块中，无需单独导入

import sys
import requests
import json
import os
import pickle
import re
import time
import threading
import random
import datetime
import traceback
from datetime import datetime, timedelta
from PyQt5.QtCore import Qt, QSize, QTimer, QDate, QDateTime, QTime, pyqtSignal, QThread, QRect, QPoint, QMimeData, QModelIndex, QEvent
from PyQt5.QtGui import QIcon, QFont, QColor, QPalette, QCursor, QBrush, QPainter, QPainterPath, QPen, QDrag, QRegion
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QTableWidget, QTableWidgetItem, QHeaderView, QLabel, QComboBox,
                            QPush<PERSON>utton, Q<PERSON><PERSON><PERSON>, <PERSON><PERSON>p<PERSON><PERSON>, QLineEdit, QCheckBox, QScrollArea,
                            QDialog, QFileDialog, QMessageBox, QMenu, QAction, QToolButton,
                            QTabWidget, QListWidget, QListWidgetItem, QAbstractItemView,
                            QStyledItemDelegate, QStyle, QStyleOptionViewItem, QProgressBar,
                            QGroupBox, QFormLayout, QGridLayout, QSpacerItem, QSizePolicy,
                            QTextEdit, QDateEdit, QStackedWidget, QToolBar, QStatusBar,
                            QGraphicsDropShadowEffect, QTableView, QProxyStyle, QStyleFactory,
                            QDialogButtonBox)
from 商品管理 import ProductManager  # 导入商品管理模块
from tool.分类树 import CategoryTree  # 导入分类树模块
from 登录窗口 import LoginWindow  # 导入登录窗口
from tool.店铺编辑器 import ShopEditor  # 导入店铺编辑器
from 一键下单 import ModernDateEdit, ModernCalendar # 导入自定义日期组件
from tool.状态管理器 import StatusManager, StatusEvent, handle_status_event  # 导入状态管理器
from 计划管理 import PlanManager  # 导入计划管理模块
import csv

# 全局变量：存储当前的排序方向，供NumericTableWidgetItem使用
CURRENT_SORT_ORDER = Qt.AscendingOrder  # 默认为升序

class CustomConfirmDialog(QDialog):
    """简洁的自定义确认对话框 - 优化圆角背景显示"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('确认退出')

        # 设置无边框窗口并禁用系统阴影
        self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint | Qt.NoDropShadowWindowHint)

        # 设置窗口图标
        icon_path = get_resource_path(os.path.join("config", "imges", "logo.ico"))
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # 初始化拖拽位置
        self.drag_position = None

        # 设置对话框大小
        self.setFixedSize(300, 160)

        # 关键：设置透明背景属性，解决圆角背景问题
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.setAttribute(Qt.WA_StyledBackground, True)

        # 设置主对话框样式为完全透明
        self.setStyleSheet("""
            CustomConfirmDialog {
                background-color: transparent;
                border: none;
            }
        """)

        # 创建内容容器 - 这是显示圆角背景的关键容器
        self.content_container = QWidget(self)
        self.content_container.setFixedSize(300, 160)
        self.content_container.move(0, 0)  # 确保容器位置正确

        # 关键设置：启用样式背景渲染
        self.content_container.setAttribute(Qt.WA_StyledBackground, True)

        # 优化的圆角样式设置 - 更浅的边框颜色
        self.content_container.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 255);
                border-radius: 15px;
                border: 1px solid #f1f3f4;
            }
            QWidget:hover {
                border-color: #e8eaed;
            }
        """)

        # 添加现代化阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(25)
        shadow.setColor(QColor(0, 0, 0, 40))
        shadow.setOffset(0, 10)
        self.content_container.setGraphicsEffect(shadow)

    def showEvent(self, event):
        """窗口显示事件 - 优化显示逻辑"""
        super().showEvent(event)

        # 初始化UI（只在第一次显示时）
        if not hasattr(self, '_ui_initialized'):
            self.init_ui()
            self._ui_initialized = True

        # 延迟设置标题栏颜色，确保窗口完全创建
        QTimer.singleShot(50, self.set_white_title_bar)

        # 确保内容容器正确定位
        self.content_container.move(0, 0)
        self.content_container.raise_()

        # 确保窗口在父窗口中央显示
        if self.parent():
            parent_rect = self.parent().geometry()
            x = parent_rect.center().x() - self.width() // 2
            y = parent_rect.center().y() - self.height() // 2
            self.move(x, y)

        # 强制刷新界面确保圆角正确显示
        self.update()
        self.content_container.update()

    def init_ui(self):
        """初始化用户界面 - 在内容容器上创建布局"""
        # 在内容容器上创建主布局
        main_layout = QVBoxLayout(self.content_container)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # 消息文本区域
        message_label = QLabel("确定要退出快手小店管理系统吗？")
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 14px;
                color: #333333;
                background-color: transparent;
                border: none;
            }
        """)
        main_layout.addWidget(message_label)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(12)
        button_layout.addStretch()

        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setObjectName("cancelButton")
        self.cancel_button.setDefault(True)
        self.cancel_button.setFocus()
        self.cancel_button.setStyleSheet("""
            QPushButton {
                font-family: 'Microsoft YaHei';
                font-size: 14px;
                font-weight: 600;
                padding: 8px 20px;
                border-radius: 8px;
                border: 1px solid #dee2e6;
                background-color: #f8f9fa;
                color: #6c757d;
                min-width: 65px;
                min-height: 32px;
                letter-spacing: 0px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                color: #495057;
                border-color: #adb5bd;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
                color: #343a40;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)

        # 确认按钮
        self.confirm_button = QPushButton("确认")
        self.confirm_button.setObjectName("confirmButton")
        self.confirm_button.setStyleSheet("""
            QPushButton {
                font-family: 'Microsoft YaHei';
                font-size: 14px;
                font-weight: 700;
                padding: 8px 20px;
                border-radius: 8px;
                border: none;
                background-color: #0d6efd;
                color: white;
                min-width: 65px;
                min-height: 32px;
                letter-spacing: 0px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #0a58ca;
                transform: translateY(0px);
            }
        """)
        self.confirm_button.clicked.connect(self.accept)

        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.confirm_button)
        button_layout.addStretch()

        main_layout.addLayout(button_layout)

    def set_white_title_bar(self):
        """设置白色标题栏 - 针对圆角窗口优化"""
        if sys.platform == 'win32':
            try:
                import ctypes
                from ctypes import wintypes

                # 获取窗口句柄
                hwnd = int(self.winId())
                if hwnd == 0:
                    return

                # Windows API 常量
                DWMWA_USE_IMMERSIVE_DARK_MODE = 20
                DWMWA_CAPTION_COLOR = 35
                DWMWA_WINDOW_CORNER_PREFERENCE = 33
                DWMWCP_ROUND = 2

                # 设置标题栏为浅色模式
                value = ctypes.c_int(0)
                try:
                    ctypes.windll.dwmapi.DwmSetWindowAttribute(
                        hwnd,
                        DWMWA_USE_IMMERSIVE_DARK_MODE,
                        ctypes.byref(value),
                        ctypes.sizeof(value)
                    )
                except:
                    pass

                # 设置标题栏颜色为白色
                color_value = ctypes.c_int(0x00FFFFFF)
                try:
                    ctypes.windll.dwmapi.DwmSetWindowAttribute(
                        hwnd,
                        DWMWA_CAPTION_COLOR,
                        ctypes.byref(color_value),
                        ctypes.sizeof(color_value)
                    )
                except:
                    pass

                # 设置窗口圆角（Windows 11）
                corner_value = ctypes.c_int(DWMWCP_ROUND)
                try:
                    ctypes.windll.dwmapi.DwmSetWindowAttribute(
                        hwnd,
                        DWMWA_WINDOW_CORNER_PREFERENCE,
                        ctypes.byref(corner_value),
                        ctypes.sizeof(corner_value)
                    )
                except:
                    pass

            except Exception as e:
                print(f"设置对话框标题栏颜色时出错: {str(e)}")

    def paintEvent(self, event):
        """重写paintEvent确保圆角背景正确渲染"""
        # 让父类处理默认绘制
        super().paintEvent(event)

        # 创建画笔进行自定义绘制
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing, True)

        # 设置透明背景
        painter.fillRect(self.rect(), QColor(0, 0, 0, 0))

        painter.end()



    def mousePressEvent(self, event):
        """鼠标按下事件 - 记录拖拽起始位置"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 实现窗口拖拽"""
        if event.buttons() == Qt.LeftButton and self.drag_position is not None:
            self.move(event.globalPos() - self.drag_position)
            event.accept()

    def mouseReleaseEvent(self, event):
        """鼠标释放事件 - 清除拖拽状态"""
        if event.button() == Qt.LeftButton:
            self.drag_position = None
            event.accept()

    def resizeEvent(self, event):
        """窗口大小改变事件 - 确保内容容器始终正确定位"""
        super().resizeEvent(event)
        if hasattr(self, 'content_container'):
            # 确保内容容器大小和位置与对话框一致
            self.content_container.resize(self.size())
            self.content_container.move(0, 0)

def get_application_directory():
    """
    获取应用程序所在目录（exe文件所在目录）

    返回:
        str: 应用程序所在目录的绝对路径
    """
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe文件
        app_dir = os.path.dirname(sys.executable)
        print(f"打包环境 - exe所在目录: {app_dir}")
    else:
        # 开发环境
        app_dir = os.path.dirname(os.path.abspath(__file__))
        print(f"开发环境 - 脚本所在目录: {app_dir}")

    return app_dir

def get_config_path(relative_path):
    """
    获取配置文件的绝对路径，确保使用exe同目录的config
    如果是打包环境且配置文件不存在，尝试从嵌入资源中提取

    参数:
        relative_path (str): 相对于config目录的文件路径

    返回:
        str: 配置文件的绝对路径
    """
    app_dir = get_application_directory()
    config_path = os.path.join(app_dir, 'config', relative_path)

    # 如果是打包环境且配置文件不存在，尝试从嵌入资源中提取
    if getattr(sys, 'frozen', False) and not os.path.exists(config_path):
        extract_embedded_config_file(relative_path, config_path)

    return config_path

def extract_embedded_config_file(relative_path, target_path):
    """
    从打包的exe中提取嵌入的配置文件到运行目录

    参数:
        relative_path (str): 相对于config目录的文件路径
        target_path (str): 目标文件的完整路径
    """
    try:
        import tempfile
        import shutil

        # 确保目标目录存在
        target_dir = os.path.dirname(target_path)
        if not os.path.exists(target_dir):
            os.makedirs(target_dir)
            print(f"创建配置目录: {target_dir}")

        # 尝试从PyInstaller的临时目录中找到配置文件
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller的临时目录
            embedded_path = os.path.join(sys._MEIPASS, 'config', relative_path)
            if os.path.exists(embedded_path):
                shutil.copy2(embedded_path, target_path)
                print(f"从嵌入资源提取配置文件: {relative_path} -> {target_path}")
                return True

        # 如果找不到嵌入的配置文件，创建默认配置
        if relative_path == 'config.json':
            create_default_config(target_path)
            return True
        elif relative_path == '账号管理.json':
            create_default_account_config(target_path)
            return True

        print(f"警告: 无法找到嵌入的配置文件: {relative_path}")
        return False

    except Exception as e:
        print(f"提取配置文件时出错: {str(e)}")
        return False

def create_default_config(config_path):
    """
    创建默认的配置文件

    参数:
        config_path (str): 配置文件的完整路径
    """
    try:
        default_config = {
            "database_server_url": "http://150.158.14.242:8000",
            "saved_credentials": {}
        }

        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)

        print(f"创建默认配置文件: {config_path}")

    except Exception as e:
        print(f"创建默认配置文件时出错: {str(e)}")

def create_default_account_config(config_path):
    """
    创建默认的账号管理配置文件

    参数:
        config_path (str): 配置文件的完整路径
    """
    try:
        default_account_config = []

        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(default_account_config, f, ensure_ascii=False, indent=2)

        print(f"创建默认账号管理配置文件: {config_path}")

    except Exception as e:
        print(f"创建默认账号管理配置文件时出错: {str(e)}")

def get_resource_path(relative_path):
    """
    获取资源文件的绝对路径，确保使用exe同目录的资源

    参数:
        relative_path (str): 相对于应用程序目录的资源路径

    返回:
        str: 资源文件的绝对路径
    """
    app_dir = get_application_directory()
    resource_path = os.path.join(app_dir, relative_path)
    print(f"资源文件完整路径: {resource_path}")
    return resource_path

def get_configured_server_url(default_url="http://150.158.14.242:8000"):
    """
    获取配置的服务器URL，统一使用应用程序根目录下的config

    参数:
        default_url (str): 默认服务器URL

    返回:
        str: 配置的服务器URL
    """
    config_path = get_config_path('config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
            url = config_data.get("database_server_url", default_url)
            if url == default_url and "database_server_url" not in config_data:
                print(f"警告: 在 {config_path} 中未找到 'database_server_url'，使用默认地址: {url}")
            return url
    except FileNotFoundError:
        print(f"错误: 配置文件 {config_path} 未找到，使用默认地址: {default_url}")
        return default_url
    except json.JSONDecodeError:
        print(f"错误: 解析配置文件 {config_path} 失败，使用默认地址: {default_url}")
        return default_url
    except Exception as e:
        print(f"读取配置文件 {config_path} 时发生未知错误: {e}，使用默认地址: {default_url}")
        return default_url


# 移除Windows API常量和函数
# 保留sys.platform检查可能在其他地方有用
if sys.platform == 'win32':
    pass  # 如有需要可以在此添加系统特定代码

class LoadDataThread(QThread):
    """后台线程用于加载数据"""
    # 定义信号，用于传递加载结果
    data_loaded = pyqtSignal(object, bool)  # 参数: 数据, 是否成功

    def __init__(self, server_url, table_name):
        super().__init__()
        self.server_url = server_url
        self.table_name = table_name

    def run(self):
        """线程执行的主要方法"""
        try:
            # 构建请求URL
            url = f"{self.server_url}/query"
            params = {"table": self.table_name}

            # 发送GET请求
            response = requests.get(url, params=params)

            # 检查响应状态
            if response.status_code == 200:
                # 获取返回的JSON数据
                data = response.json()

                if 'data' in data:
                    # 服务器返回的是字典列表形式
                    records = data['data']

                    if records and isinstance(records, list) and isinstance(records[0], dict):
                        # 发送成功信号，返回数据
                        self.data_loaded.emit(records, True)
                        return

            # 如果到这里，说明加载失败
            self.data_loaded.emit(None, False)
        except Exception as e:
            print(f"加载数据失败: {str(e)}")
            self.data_loaded.emit(None, False)

class LoadSupplierStatsThread(QThread):
    """后台线程用于加载和处理上家统计数据"""
    data_loaded = pyqtSignal(dict, list, str, str, bool, str)

    def __init__(self, server_url, start_date, end_date, parent=None):
        super().__init__(parent)
        self.server_url = server_url
        self.start_date = start_date
        self.end_date = end_date

    def run(self):
        try:
            all_orders = self._fetch_all_orders()
            supplier_daily_stats, sorted_keys = self._aggregate_data(all_orders)
            self.data_loaded.emit(supplier_daily_stats, sorted_keys, self.start_date, self.end_date, True, "")
        except requests.exceptions.RequestException as req_err:
            error_msg = f"网络请求失败: {str(req_err)}"
            self.data_loaded.emit({}, [], self.start_date, self.end_date, False, error_msg)
        except ValueError as val_err:
            error_msg = f"数据处理错误: {str(val_err)}"
            self.data_loaded.emit({}, [], self.start_date, self.end_date, False, error_msg)
        except Exception as e:
            error_msg = f"加载上家统计时发生未知错误: {str(e)}\n{traceback.format_exc()}"
            self.data_loaded.emit({}, [], self.start_date, self.end_date, False, error_msg)

    def _fetch_all_orders(self):
        all_orders = []
        api_url = f"{self.server_url}/query_by_time"
        params = {
            "table": "下单记录表",
            "time_field": "下单时间",
            "start_time": f"{self.start_date} 00:00:00",
            "end_time": f"{self.end_date} 23:59:59",
        }
        response = requests.get(api_url, params=params, timeout=120)
        response.raise_for_status()
        result = response.json()
        if "data" in result and isinstance(result["data"], list):
            all_orders = result["data"]
        return all_orders

    def _aggregate_data(self, all_orders):
        supplier_daily_stats = {}
        date_range_key_part = f"{self.start_date}_to_{self.end_date}"

        # 注意：此方法只负责数据聚合，不添加总计行
        # 总计信息将在UI中通过update_stats_summary_from_table方法计算并显示在底部状态栏

        for order in all_orders:
            if not isinstance(order, dict): continue

            remark_str = order.get("订单备注", "")
            supplier_name_for_key = "未知上家"
            if isinstance(remark_str, str):
                specific_match = re.search(r'采购价[^上]*上家店铺\s*(.*?)\s*利润[^元]*元', remark_str)
                if specific_match:
                    extracted_name = specific_match.group(1).strip()
                    if extracted_name: supplier_name_for_key = extracted_name
                else:
                    original_match = re.search(r'店铺\s*(.*?)\s*/', remark_str)
                    if original_match:
                        extracted_name = original_match.group(1).strip()
                        if extracted_name: supplier_name_for_key = extracted_name

            composite_key = (date_range_key_part, supplier_name_for_key)

            if composite_key not in supplier_daily_stats:
                # 生成简洁的日期范围显示（去掉时分秒）
                start_date_only = self.start_date.split(' ')[0] if ' ' in self.start_date else self.start_date[:10]
                end_date_only = self.end_date.split(' ')[0] if ' ' in self.end_date else self.end_date[:10]
                display_date_range = f"{start_date_only} ~ {end_date_only}"

                supplier_daily_stats[composite_key] = {
                    "order_count": 0, "valid_order_count": 0, "refund_count": 0, "refunded_count": 0,
                    "payment": 0.0, "prepayment": 0.0, "shipping": 0.0, "purchase_amount": 0.0,
                    "outgoing_shipping": 0.0, "profit": 0.0, "actual_profit": 0.0, "free_shipping_count": 0,
                    "display_date_range": display_date_range,
                    "display_supplier_name": supplier_name_for_key
                }

            stats_entry = supplier_daily_stats[composite_key]
            order_status = order.get("最后状态", "")

            prepayment_str = order.get("付款", "0")
            prepayment_amount = 0.0
            incoming_shipping_fee_pre = 0.0
            if isinstance(prepayment_str, str):
                try:
                    payment_part_pre = prepayment_str.split('(')[0].strip() if '(' in prepayment_str else prepayment_str
                    payment_match_pre = re.search(r'^[\d.]+', payment_part_pre)
                    if payment_match_pre: prepayment_amount = float(payment_match_pre.group(0))
                    if '运费' in prepayment_str and ('(' in prepayment_str or '（' in prepayment_str):
                        match_pre = re.search(r'运费\s*([\d.]+)\s*[)）]', prepayment_str)
                        if match_pre: incoming_shipping_fee_pre = float(match_pre.group(1))
                except (ValueError, TypeError): pass
            elif isinstance(prepayment_str, (int, float)): prepayment_amount = float(prepayment_str)
            stats_entry["prepayment"] += prepayment_amount

            stats_entry["order_count"] += 1
            if order_status == '退款成功':
                stats_entry["refund_count"] += 1
            elif order_status == '已退款':
                stats_entry["refunded_count"] += 1

            outgoing_shipping_str = order.get("运费", "0")
            outgoing_shipping_fee = 0.0
            try:
                if outgoing_shipping_str is not None:
                    if isinstance(outgoing_shipping_str, str):
                        cleaned_str = re.sub(r'[^\d.-]', '', outgoing_shipping_str)
                        if cleaned_str and cleaned_str != '-': outgoing_shipping_fee = float(cleaned_str)
                    elif isinstance(outgoing_shipping_str, (int, float)):
                        outgoing_shipping_fee = float(outgoing_shipping_str)
                if outgoing_shipping_fee == 0 and (order_status != '退款成功' and order_status != '已退款'):
                    stats_entry["free_shipping_count"] += 1
            except (ValueError, TypeError): pass
            stats_entry["outgoing_shipping"] += outgoing_shipping_fee

            actual_profit_str = order.get("实际利润", "0")
            actual_profit_value = 0.0
            try:
                if actual_profit_str is not None:
                    if isinstance(actual_profit_str, (int, float)): actual_profit_value = float(actual_profit_str)
                    elif isinstance(actual_profit_str, str):
                        cleaned_str = re.sub(r'[^\d.-]', '', actual_profit_str)
                        if cleaned_str and cleaned_str != '-': actual_profit_value = float(cleaned_str)
            except (ValueError, TypeError): pass
            stats_entry["actual_profit"] += actual_profit_value

            if order_status != '退款成功' and order_status != '已退款':
                stats_entry["valid_order_count"] += 1
                stats_entry["payment"] += prepayment_amount
                stats_entry["shipping"] += incoming_shipping_fee_pre

                purchase_amount_value = 0.0
                remark_str_pc = order.get("订单备注", "")
                if isinstance(remark_str_pc, str):
                    match_pc = re.search(r'采购价\s*([\d.]+)\s*上家', remark_str_pc)
                    if match_pc:
                        try: purchase_amount_value = float(match_pc.group(1))
                        except ValueError: pass
                stats_entry["purchase_amount"] += purchase_amount_value

                profit_value_from_remark = 0.0
                remark_str_pr = order.get("订单备注", "")
                if isinstance(remark_str_pr, str):
                    profit_match = re.search(r'利润\s*([\d.]+)\s*元', remark_str_pr)
                    if profit_match:
                        try: profit_value_from_remark = float(profit_match.group(1))
                        except ValueError: pass
                stats_entry["profit"] += profit_value_from_remark

        sorted_keys = sorted(supplier_daily_stats.keys(), key=lambda k: (k[0], k[1]))
        return supplier_daily_stats, sorted_keys

class LoadShopStatsThread(QThread):
    """后台线程用于加载和处理店铺统计数据"""
    data_loaded = pyqtSignal(bool, str)  # 成功/失败, 错误信息

    def __init__(self, server_url, start_date, end_date, parent=None, data_stats_widget=None):
        super().__init__(parent)
        self.server_url = server_url
        self.start_date = start_date
        self.end_date = end_date
        self.parent_window = parent
        self.data_stats_widget = data_stats_widget

    def run(self):
        try:
            print(f"LoadShopStatsThread: 开始加载店铺统计数据 {self.start_date} 到 {self.end_date}")

            # 优先调用数据统计模块的方法
            if self.data_stats_widget and hasattr(self.data_stats_widget, '_load_shop_statistics_sync'):
                success = self.data_stats_widget._load_shop_statistics_sync(self.start_date, self.end_date)
                if success:
                    self.data_loaded.emit(True, "")
                else:
                    self.data_loaded.emit(False, "加载店铺统计数据失败")
            # 如果数据统计模块不可用，回退到父窗口方法
            elif self.parent_window and hasattr(self.parent_window, '_load_shop_statistics_sync'):
                success = self.parent_window._load_shop_statistics_sync(self.start_date, self.end_date)
                if success:
                    self.data_loaded.emit(True, "")
                else:
                    self.data_loaded.emit(False, "加载店铺统计数据失败")
            else:
                self.data_loaded.emit(False, "无法找到数据加载方法")

        except requests.exceptions.RequestException as req_err:
            error_msg = f"网络请求失败: {str(req_err)}"
            print(f"LoadShopStatsThread 网络错误: {error_msg}")
            self.data_loaded.emit(False, error_msg)
        except ValueError as val_err:
            error_msg = f"数据处理错误: {str(val_err)}"
            print(f"LoadShopStatsThread 数据错误: {error_msg}")
            self.data_loaded.emit(False, error_msg)
        except Exception as e:
            error_msg = f"加载店铺统计时发生未知错误: {str(e)}"
            print(f"LoadShopStatsThread 未知错误: {error_msg}")
            import traceback
            traceback.print_exc()
            self.data_loaded.emit(False, error_msg)

class LoadDailyStatsThread(QThread):
    """后台线程用于加载和处理每日报表数据"""
    data_loaded = pyqtSignal(bool, str)  # 成功/失败, 错误信息

    def __init__(self, server_url, start_date, end_date, parent=None, data_stats_widget=None):
        super().__init__(parent)
        self.server_url = server_url
        self.start_date = start_date
        self.end_date = end_date
        self.parent_window = parent
        self.data_stats_widget = data_stats_widget

    def run(self):
        try:
            print(f"LoadDailyStatsThread: 开始加载每日报表数据 {self.start_date} 到 {self.end_date}")

            # 优先调用数据统计模块的方法
            if self.data_stats_widget and hasattr(self.data_stats_widget, '_load_daily_statistics_sync'):
                success = self.data_stats_widget._load_daily_statistics_sync(self.start_date, self.end_date)
                if success:
                    self.data_loaded.emit(True, "")
                else:
                    self.data_loaded.emit(False, "加载每日报表数据失败")
            # 如果数据统计模块不可用，回退到父窗口方法
            elif self.parent_window and hasattr(self.parent_window, '_load_daily_statistics_sync'):
                success = self.parent_window._load_daily_statistics_sync(self.start_date, self.end_date)
                if success:
                    self.data_loaded.emit(True, "")
                else:
                    self.data_loaded.emit(False, "加载每日报表数据失败")
            else:
                self.data_loaded.emit(False, "无法找到数据加载方法")

        except requests.exceptions.RequestException as req_err:
            error_msg = f"网络请求失败: {str(req_err)}"
            print(f"LoadDailyStatsThread 网络错误: {error_msg}")
            self.data_loaded.emit(False, error_msg)
        except ValueError as val_err:
            error_msg = f"数据处理错误: {str(val_err)}"
            print(f"LoadDailyStatsThread 数据错误: {error_msg}")
            self.data_loaded.emit(False, error_msg)
        except Exception as e:
            error_msg = f"加载每日报表时发生未知错误: {str(e)}"
            print(f"LoadDailyStatsThread 未知错误: {error_msg}")
            import traceback
            traceback.print_exc()
            self.data_loaded.emit(False, error_msg)


class LoadMonthlyStatsThread(QThread):
    """后台线程用于加载和处理每月报表数据"""
    data_loaded = pyqtSignal(bool, str)  # 成功/失败, 错误信息

    def __init__(self, server_url, start_date, end_date, parent=None, data_stats_widget=None):
        super().__init__(parent)
        self.server_url = server_url
        self.start_date = start_date
        self.end_date = end_date
        self.parent_window = parent
        self.data_stats_widget = data_stats_widget

    def run(self):
        try:
            print(f"LoadMonthlyStatsThread: 开始加载每月报表数据 {self.start_date} 到 {self.end_date}")

            # 优先调用数据统计模块的方法
            if self.data_stats_widget and hasattr(self.data_stats_widget, '_load_monthly_statistics_sync'):
                success = self.data_stats_widget._load_monthly_statistics_sync(self.start_date, self.end_date)
                if success:
                    self.data_loaded.emit(True, "")
                else:
                    self.data_loaded.emit(False, "加载每月报表数据失败")
            # 如果数据统计模块不可用，回退到父窗口方法
            elif self.parent_window and hasattr(self.parent_window, 'load_monthly_statistics'):
                success = self.parent_window.load_monthly_statistics(self.start_date, self.end_date)
                if success:
                    self.data_loaded.emit(True, "")
                else:
                    self.data_loaded.emit(False, "加载每月报表数据失败")
            else:
                self.data_loaded.emit(False, "无法找到数据加载方法")

        except requests.exceptions.RequestException as req_err:
            error_msg = f"网络请求失败: {str(req_err)}"
            print(f"LoadMonthlyStatsThread 网络错误: {error_msg}")
            self.data_loaded.emit(False, error_msg)
        except ValueError as val_err:
            error_msg = f"数据处理错误: {str(val_err)}"
            print(f"LoadMonthlyStatsThread 数据错误: {error_msg}")
            self.data_loaded.emit(False, error_msg)
        except Exception as e:
            error_msg = f"加载每月报表时发生未知错误: {str(e)}"
            print(f"LoadMonthlyStatsThread 未知错误: {error_msg}")
            import traceback
            traceback.print_exc()
            self.data_loaded.emit(False, error_msg)


class LoadYearlyStatsThread(QThread):
    """后台线程用于加载和处理年度报表数据"""
    data_loaded = pyqtSignal(bool, str)  # 成功/失败, 错误信息

    def __init__(self, server_url, start_date, end_date, parent=None, data_stats_widget=None):
        super().__init__(parent)
        self.server_url = server_url
        self.start_date = start_date
        self.end_date = end_date
        self.parent_window = parent
        self.data_stats_widget = data_stats_widget

    def run(self):
        try:
            print(f"LoadYearlyStatsThread: 开始加载年度报表数据 {self.start_date} 到 {self.end_date}")

            # 优先调用数据统计模块的方法
            if self.data_stats_widget and hasattr(self.data_stats_widget, '_load_yearly_statistics_sync'):
                success = self.data_stats_widget._load_yearly_statistics_sync(self.start_date, self.end_date)
                if success:
                    self.data_loaded.emit(True, "")
                else:
                    self.data_loaded.emit(False, "加载年度报表数据失败")
            # 如果数据统计模块不可用，回退到父窗口方法
            elif self.parent_window and hasattr(self.parent_window, 'load_yearly_statistics'):
                success = self.parent_window.load_yearly_statistics(self.start_date, self.end_date)
                if success:
                    self.data_loaded.emit(True, "")
                else:
                    self.data_loaded.emit(False, "加载年度报表数据失败")
            else:
                self.data_loaded.emit(False, "无法找到数据加载方法")

        except requests.exceptions.RequestException as req_err:
            error_msg = f"网络请求失败: {str(req_err)}"
            print(f"LoadYearlyStatsThread 网络错误: {error_msg}")
            self.data_loaded.emit(False, error_msg)
        except ValueError as val_err:
            error_msg = f"数据处理错误: {str(val_err)}"
            print(f"LoadYearlyStatsThread 数据错误: {error_msg}")
            self.data_loaded.emit(False, error_msg)
        except Exception as e:
            error_msg = f"加载年度报表时发生未知错误: {str(e)}"
            print(f"LoadYearlyStatsThread 未知错误: {error_msg}")
            import traceback
            traceback.print_exc()
            self.data_loaded.emit(False, error_msg)


class LoadMonthlyStatsThread(QThread):
    """后台线程用于加载和处理每月报表数据"""
    data_loaded = pyqtSignal(bool, str)  # 成功/失败, 错误信息

    def __init__(self, server_url, start_date, end_date, parent=None, data_stats_widget=None):
        super().__init__(parent)
        self.server_url = server_url
        self.start_date = start_date
        self.end_date = end_date
        self.parent_window = parent
        self.data_stats_widget = data_stats_widget

    def run(self):
        try:
            print(f"LoadMonthlyStatsThread: 开始加载每月报表数据 {self.start_date} 到 {self.end_date}")

            # 优先调用数据统计模块的方法
            if self.data_stats_widget and hasattr(self.data_stats_widget, '_load_monthly_statistics_sync'):
                success = self.data_stats_widget._load_monthly_statistics_sync(self.start_date, self.end_date)
                if success:
                    self.data_loaded.emit(True, "")
                else:
                    self.data_loaded.emit(False, "加载每月报表数据失败")
            # 如果数据统计模块不可用，回退到父窗口方法
            elif self.parent_window and hasattr(self.parent_window, 'load_monthly_statistics'):
                success = self.parent_window.load_monthly_statistics(self.start_date, self.end_date)
                if success:
                    self.data_loaded.emit(True, "")
                else:
                    self.data_loaded.emit(False, "加载每月报表数据失败")
            else:
                self.data_loaded.emit(False, "无法找到数据加载方法")

        except requests.exceptions.RequestException as req_err:
            error_msg = f"网络请求失败: {str(req_err)}"
            print(f"LoadMonthlyStatsThread 网络错误: {error_msg}")
            self.data_loaded.emit(False, error_msg)
        except ValueError as val_err:
            error_msg = f"数据处理错误: {str(val_err)}"
            print(f"LoadMonthlyStatsThread 数据错误: {error_msg}")
            self.data_loaded.emit(False, error_msg)
        except Exception as e:
            error_msg = f"加载每月报表时发生未知错误: {str(e)}"
            print(f"LoadMonthlyStatsThread 未知错误: {error_msg}")
            import traceback
            traceback.print_exc()
            self.data_loaded.emit(False, error_msg)


class LoadYearlyStatsThread(QThread):
    """后台线程用于加载和处理年度报表数据"""
    data_loaded = pyqtSignal(bool, str)  # 成功/失败, 错误信息

    def __init__(self, server_url, start_date, end_date, parent=None, data_stats_widget=None):
        super().__init__(parent)
        self.server_url = server_url
        self.start_date = start_date
        self.end_date = end_date
        self.parent_window = parent
        self.data_stats_widget = data_stats_widget

    def run(self):
        try:
            print(f"LoadYearlyStatsThread: 开始加载年度报表数据 {self.start_date} 到 {self.end_date}")

            # 优先调用数据统计模块的方法
            if self.data_stats_widget and hasattr(self.data_stats_widget, '_load_yearly_statistics_sync'):
                success = self.data_stats_widget._load_yearly_statistics_sync(self.start_date, self.end_date)
                if success:
                    self.data_loaded.emit(True, "")
                else:
                    self.data_loaded.emit(False, "加载年度报表数据失败")
            # 如果数据统计模块不可用，回退到父窗口方法
            elif self.parent_window and hasattr(self.parent_window, 'load_yearly_statistics'):
                success = self.parent_window.load_yearly_statistics(self.start_date, self.end_date)
                if success:
                    self.data_loaded.emit(True, "")
                else:
                    self.data_loaded.emit(False, "加载年度报表数据失败")
            else:
                self.data_loaded.emit(False, "无法找到数据加载方法")

        except requests.exceptions.RequestException as req_err:
            error_msg = f"网络请求失败: {str(req_err)}"
            print(f"LoadYearlyStatsThread 网络错误: {error_msg}")
            self.data_loaded.emit(False, error_msg)
        except ValueError as val_err:
            error_msg = f"数据处理错误: {str(val_err)}"
            print(f"LoadYearlyStatsThread 数据错误: {error_msg}")
            self.data_loaded.emit(False, error_msg)
        except Exception as e:
            error_msg = f"加载年度报表时发生未知错误: {str(e)}"
            print(f"LoadYearlyStatsThread 未知错误: {error_msg}")
            import traceback
            traceback.print_exc()
            self.data_loaded.emit(False, error_msg)

class BrowserConfigSyncThread(QThread):
    """后台线程用于同步浏览器配置"""
    progress_updated = pyqtSignal(str)  # 进度更新信号
    sync_completed = pyqtSignal(bool, int, int, list)  # 同步完成信号：成功/失败, 成功数量, 总数量, 错误列表

    def __init__(self, source_shop_data, target_shops, parent=None):
        """
        初始化同步线程

        参数:
            source_shop_data (dict): 源店铺数据，包含店铺名称和店铺ID
            target_shops (list): 目标店铺列表，格式为[(shop_name, target_dir), ...]
            parent: 父窗口对象
        """
        super().__init__(parent)
        self.source_shop_data = source_shop_data
        self.target_shops = target_shops
        self.parent_window = parent

    def run(self):
        try:
            import os
            import shutil
            import json

            success_count = 0
            error_shops = []
            total_shops = len(self.target_shops)

            # 获取源店铺信息
            source_shop_name = self.source_shop_data.get('店铺名称', '未知店铺')
            source_shop_id = self.source_shop_data.get('店铺ID', '')

            if not source_shop_id:
                error_msg = f"无法获取源店铺 '{source_shop_name}' 的店铺ID"
                print(f"❌ {error_msg}")
                self.sync_completed.emit(False, 0, total_shops, [error_msg])
                return

            # 构建源店铺目录路径
            config_base_dir = os.path.join(os.getcwd(), "config", "kslogoin")
            source_dir = os.path.join(config_base_dir, f"shop_{source_shop_id}")

            # 检查源目录是否存在
            if not os.path.exists(source_dir):
                error_msg = f"源店铺 '{source_shop_name}' (ID: {source_shop_id}) 的配置目录不存在: {source_dir}"
                print(f"❌ {error_msg}")
                self.sync_completed.emit(False, 0, total_shops, [error_msg])
                return

            print(f"🔍 开始同步源店铺 '{source_shop_name}' (ID: {source_shop_id}) 的配置")
            print(f"📁 源目录: {source_dir}")

            # 获取可同步的项目（只同步指定的四个配置项）
            available_items = []

            # 定义要同步的配置项（只包含指定的四个选项）
            sync_items = {
                "Extensions": {"type": "directory", "path": "Default/Extensions", "name": "插件配置", "required": True},
                "Bookmarks": {"type": "file", "path": "Default/Bookmarks", "name": "收藏夹配置", "required": True},
                "Preferences": {"type": "file", "path": "Default/Preferences", "name": "浏览器首选项配置", "required": True},
                "Secure Preferences": {"type": "file", "path": "Default/Secure Preferences", "name": "安全首选项配置", "required": True}
            }

            # 注意：以下项目不同步，每个店铺应保持独立
            # - Cookies: Cookie数据（店铺登录状态独立）
            # - History: 浏览历史（每个店铺的浏览记录独立）
            # - Login Data: 登录数据（每个店铺的密码独立）
            # - Local Storage: 本地存储（可能包含店铺特定数据）
            # - Session Storage: 会话存储（会话相关数据）
            # - IndexedDB: 数据库存储（可能包含店铺特定数据）
            # - Cache: 缓存文件（可能包含店铺特定缓存）
            # - Web Data: 表单数据（可能包含店铺特定数据）
            # - User StyleSheets: 自定义样式（非必需）
            # - Local State: 全局状态（可能包含店铺特定数据）

            print(f"🔍 扫描可同步的浏览器配置项...")

            # 检查每个配置项是否存在
            for item_key, item_info in sync_items.items():
                item_path = os.path.join(source_dir, item_info["path"])
                print(f"🔍 检查配置项 {item_info['name']}: {item_path}")
                if os.path.exists(item_path):
                    available_items.append(item_key)
                    print(f"✅ 找到{item_info['name']}: {item_info['path']}")
                    # 输出详细信息
                    if item_info["type"] == "directory":
                        try:
                            items_count = len(os.listdir(item_path))
                            print(f"   📁 目录内容: {items_count} 个项目")
                        except:
                            print(f"   📁 目录（无法访问内容）")
                    else:
                        try:
                            file_size = os.path.getsize(item_path)
                            print(f"   📄 文件大小: {file_size} 字节")
                        except:
                            print(f"   📄 文件（无法访问信息）")
                else:
                    if item_info.get("required", True):
                        print(f"❌ 缺失必需配置项 {item_info['name']}: {item_info['path']}")
                        print(f"   🔍 完整路径: {item_path}")
                    else:
                        print(f"⚪ 跳过可选配置项 {item_info['name']}: {item_info['path']}（不存在）")

            if not available_items:
                error_msg = f"源店铺 '{source_shop_name}' 没有可同步的配置项"
                print(f"❌ {error_msg}")
                self.sync_completed.emit(False, 0, total_shops, [error_msg])
                return

            # 开始同步到目标店铺
            for i, target_shop_data in enumerate(self.target_shops):
                target_shop_name = target_shop_data.get('店铺名称', '未知店铺')
                target_shop_id = target_shop_data.get('店铺ID', '')

                # 发送进度更新信号
                progress_percent = int((i / total_shops) * 100)
                status_message = f"正在同步浏览器配置到 {target_shop_name}... ({i+1}/{total_shops}) {progress_percent}%"
                self.progress_updated.emit(status_message)

                try:
                    if not target_shop_id:
                        error_msg = f"无法获取目标店铺 '{target_shop_name}' 的店铺ID"
                        print(f"❌ {error_msg}")
                        error_shops.append(error_msg)
                        continue

                    # 构建目标店铺目录路径
                    target_dir = os.path.join(config_base_dir, f"shop_{target_shop_id}")
                    target_default_dir = os.path.join(target_dir, "Default")

                    # 确保目标目录存在
                    if not os.path.exists(target_default_dir):
                        os.makedirs(target_default_dir)
                        print(f"📁 创建目标目录: {target_default_dir}")

                    print(f"🔄 同步到店铺 '{target_shop_name}' (ID: {target_shop_id})")

                    # 同步配置项
                    sync_success_count = 0
                    sync_error_count = 0

                    for item_name in available_items:
                        item_info = sync_items[item_name]
                        source_path = os.path.join(source_dir, item_info["path"])

                        # 计算目标路径
                        if item_info["path"].startswith("Default/"):
                            target_path = os.path.join(target_default_dir, item_info["path"][8:])  # 去掉"Default/"前缀
                        else:
                            target_path = os.path.join(target_dir, item_info["path"])

                        print(f"🔍 检查源路径是否存在: {source_path}")
                        if os.path.exists(source_path):
                            print(f"✅ 源路径存在，开始同步 {item_info['name']}")
                            try:
                                if item_info["type"] == "directory":
                                    # 同步目录 - 使用智能同步方法
                                    print(f"📁 开始同步 {item_info['name']} 目录")
                                    print(f"   源: {source_path}")
                                    print(f"   目标: {target_path}")
                                    self._sync_browser_item_safely(source_path, target_path, item_name, item_info)
                                    print(f"✅ 成功同步 {item_info['name']} 目录")
                                else:
                                    # 同步文件 - 使用安全复制
                                    print(f"📄 开始同步 {item_info['name']} 文件")
                                    print(f"   源: {source_path}")
                                    print(f"   目标: {target_path}")
                                    self._sync_browser_file_safely(source_path, target_path, item_name, item_info)
                                    print(f"✅ 成功同步 {item_info['name']} 文件")
                                sync_success_count += 1
                            except Exception as item_error:
                                sync_error_count += 1
                                error_msg = f"同步 {item_info['name']} 到 {target_shop_name} 时出错: {str(item_error)}"
                                print(f"❌ {error_msg}")
                                print(f"   详细错误: {type(item_error).__name__}: {str(item_error)}")
                                # 继续处理其他配置项，不中断整个同步过程
                                continue
                        else:
                            print(f"⚠️ 源路径不存在，跳过 {item_info['name']}: {source_path}")

                    print(f"📊 店铺 '{target_shop_name}' 同步统计: 成功 {sync_success_count}, 失败 {sync_error_count}")

                    # 只有当至少有一个配置项同步成功时，才算这个店铺同步成功
                    if sync_success_count > 0:
                        success_count += 1
                        print(f"✅ 成功同步到店铺 '{target_shop_name}' (成功 {sync_success_count}/{sync_success_count + sync_error_count} 项)")
                    else:
                        error_msg = f"店铺 '{target_shop_name}' 没有任何配置项同步成功"
                        print(f"❌ {error_msg}")
                        error_shops.append(error_msg)

                except Exception as e:
                    error_msg = f"同步到 {target_shop_name} 时出错: {str(e)}"
                    print(f"❌ {error_msg}")
                    error_shops.append(error_msg)

            # 发送完成信号
            print(f"🎉 同步完成！成功: {success_count}/{total_shops}")
            self.sync_completed.emit(success_count > 0, success_count, total_shops, error_shops)

        except Exception as e:
            error_msg = f"同步线程执行时出错: {str(e)}"
            print(f"❌ {error_msg}")
            self.sync_completed.emit(False, 0, len(self.target_shops), [error_msg])

    def _sync_directory_efficiently(self, source_dir, target_dir):
        """
        高效的目录同步方法 - 直接覆盖而不删除

        功能：
        - 使用现代化的目录同步方法
        - 自动覆盖已存在的文件，无需先删除
        - 保持目录结构和文件权限
        - 特别处理Chrome Extensions目录的权限问题
        - 比先删除再复制的方式更快更安全

        参数:
            source_dir (str): 源目录路径
            target_dir (str): 目标目录路径
        """
        try:
            import shutil
            import os
            import sys
            import errno

            # 确保目标目录的父目录存在
            os.makedirs(os.path.dirname(target_dir), exist_ok=True)

            # 检查是否为Extensions目录，需要特殊处理
            is_extensions_dir = os.path.basename(source_dir).lower() == 'extensions'
            if is_extensions_dir:
                print(f"🔧 检测到Extensions目录，使用增强同步模式")

            # 优先使用shutil.copytree的现代方法（Python 3.8+）
            if sys.version_info >= (3, 8):
                try:
                    if is_extensions_dir:
                        # 对Extensions目录使用更宽松的权限处理
                        shutil.copytree(source_dir, target_dir, dirs_exist_ok=True,
                                      ignore_dangling_symlinks=True,
                                      copy_function=self._safe_copy_function)
                    else:
                        shutil.copytree(source_dir, target_dir, dirs_exist_ok=True)
                    return
                except (PermissionError, OSError) as e:
                    if e.errno in (errno.EPERM, errno.EACCES):
                        print(f"⚠️ 权限错误，尝试增强模式: {str(e)}")
                        if is_extensions_dir:
                            self._sync_extensions_safely(source_dir, target_dir)
                            return
                    print(f"⚠️ shutil.copytree失败: {str(e)}")
                except Exception as e:
                    print(f"⚠️ shutil.copytree失败: {str(e)}")

            # 尝试使用distutils（如果可用，主要用于Python 3.7及以下）
            try:
                # 动态导入避免静态分析警告
                import importlib
                distutils_module = importlib.import_module('distutils.dir_util')
                copy_tree = getattr(distutils_module, 'copy_tree')
                copy_tree(source_dir, target_dir, update=1, verbose=0)
                return
            except (ImportError, AttributeError):
                pass
            except (PermissionError, OSError) as e:
                if is_extensions_dir and e.errno in (errno.EPERM, errno.EACCES):
                    print(f"⚠️ distutils权限错误，使用安全模式: {str(e)}")
                    self._sync_extensions_safely(source_dir, target_dir)
                    return

            # 降级到备用方案
            print("⚠️ 使用备用同步方案")
            if is_extensions_dir:
                self._sync_extensions_safely(source_dir, target_dir)
            else:
                self._sync_directory_fallback(source_dir, target_dir)

        except Exception as e:
            print(f"⚠️ 高效同步失败，使用备用方案: {str(e)}")
            # 检查是否为Extensions目录
            is_extensions_dir = os.path.basename(source_dir).lower() == 'extensions'
            if is_extensions_dir:
                self._sync_extensions_safely(source_dir, target_dir)
            else:
                self._sync_directory_fallback(source_dir, target_dir)

    def _sync_directory_fallback(self, source_dir, target_dir):
        """
        备用的目录同步方法

        功能：
        - 当高效方法不可用时的备用方案
        - 递归复制文件和子目录
        - 自动创建不存在的目录
        - 覆盖已存在的文件
        - 增强的错误处理

        参数:
            source_dir (str): 源目录路径
            target_dir (str): 目标目录路径
        """
        import os
        import shutil
        import errno

        # 确保目标目录存在
        os.makedirs(target_dir, exist_ok=True)

        # 遍历源目录中的所有文件和子目录
        for item in os.listdir(source_dir):
            source_item = os.path.join(source_dir, item)
            target_item = os.path.join(target_dir, item)

            try:
                if os.path.isdir(source_item):
                    # 递归同步子目录
                    self._sync_directory_fallback(source_item, target_item)
                else:
                    # 复制文件（自动覆盖）
                    self._safe_copy_file(source_item, target_item)
            except Exception as e:
                print(f"⚠️ 跳过文件/目录 {item}: {str(e)}")
                # 继续处理其他文件，不中断整个过程
                continue

    def _safe_copy_function(self, src, dst, *, follow_symlinks=True):
        """
        安全的文件复制函数，处理权限错误
        用于shutil.copytree的copy_function参数
        """
        import shutil
        import errno
        import os

        try:
            return shutil.copy2(src, dst, follow_symlinks=follow_symlinks)
        except (PermissionError, OSError) as e:
            if e.errno in (errno.EPERM, errno.EACCES):
                # 权限不足时，尝试只复制文件内容
                try:
                    shutil.copyfile(src, dst)
                    print(f"⚠️ 已复制文件内容（忽略权限）: {os.path.basename(src)}")
                    return dst
                except Exception:
                    print(f"❌ 无法复制文件: {os.path.basename(src)}")
                    # 不抛出异常，让copytree继续处理其他文件
                    return dst
            else:
                raise

    def _sync_extensions_safely(self, source_dir, target_dir):
        """
        安全地同步Chrome Extensions目录

        功能：
        - 专门处理Extensions目录的特殊文件结构
        - 跳过临时文件和锁文件
        - 处理权限问题
        - 提供详细的同步反馈
        """
        import os
        import shutil
        import errno

        try:
            # 确保目标目录存在
            os.makedirs(target_dir, exist_ok=True)

            # Extensions目录中应该跳过的文件/目录
            skip_patterns = {
                'temp', 'tmp', '.tmp', 'lock', '.lock',
                '.crdownload', '.crswap', 'crash reports'
            }

            success_count = 0
            skip_count = 0
            error_count = 0

            print(f"🔧 开始安全同步Extensions目录")

            # 遍历Extensions目录
            for item in os.listdir(source_dir):
                # 检查是否应该跳过
                if any(pattern in item.lower() for pattern in skip_patterns):
                    print(f"⏭️ 跳过临时文件: {item}")
                    skip_count += 1
                    continue

                source_item = os.path.join(source_dir, item)
                target_item = os.path.join(target_dir, item)

                try:
                    if os.path.isdir(source_item):
                        # 同步插件目录
                        self._sync_plugin_directory(source_item, target_item)
                        success_count += 1
                    else:
                        # 复制单个文件
                        self._safe_copy_file(source_item, target_item)
                        success_count += 1

                except Exception as e:
                    error_count += 1
                    print(f"❌ 同步失败 {item}: {str(e)}")
                    continue

            print(f"📊 Extensions同步完成: 成功 {success_count}, 跳过 {skip_count}, 失败 {error_count}")

        except Exception as e:
            print(f"❌ Extensions安全同步失败: {str(e)}")
            # 最后的备用方案
            self._sync_directory_fallback(source_dir, target_dir)

    def _sync_plugin_directory(self, source_plugin_dir, target_plugin_dir):
        """
        同步单个Chrome插件目录
        """
        import os
        import shutil

        # 确保目标插件目录存在
        os.makedirs(target_plugin_dir, exist_ok=True)

        # 递归复制插件目录内容
        for root, dirs, files in os.walk(source_plugin_dir):
            # 计算相对路径
            rel_path = os.path.relpath(root, source_plugin_dir)
            target_root = os.path.join(target_plugin_dir, rel_path) if rel_path != '.' else target_plugin_dir

            # 确保目标子目录存在
            os.makedirs(target_root, exist_ok=True)

            # 复制文件
            for file in files:
                source_file = os.path.join(root, file)
                target_file = os.path.join(target_root, file)
                self._safe_copy_file(source_file, target_file)

    def _safe_copy_file(self, source_file, target_file):
        """
        安全地复制单个文件，处理各种异常情况
        """
        import shutil
        import errno
        import os
        import stat

        try:
            # 如果目标文件存在且为只读，先修改权限
            if os.path.exists(target_file):
                try:
                    # 获取当前权限
                    current_mode = os.stat(target_file).st_mode
                    # 添加写权限
                    os.chmod(target_file, current_mode | stat.S_IWUSR | stat.S_IWGRP)
                except (PermissionError, OSError):
                    pass  # 忽略权限修改失败

            # 复制文件
            shutil.copy2(source_file, target_file)

        except (PermissionError, OSError) as e:
            if e.errno in (errno.EPERM, errno.EACCES):
                # 权限不足时，尝试只复制内容
                try:
                    shutil.copyfile(source_file, target_file)
                except Exception:
                    # 完全无法复制时，记录但不抛出异常
                    print(f"⚠️ 无法复制文件: {os.path.basename(source_file)}")
            else:
                # 其他错误继续抛出
                raise

    def _sync_browser_item_safely(self, source_path, target_path, item_name, item_info):
        """
        安全地同步浏览器配置目录，根据不同类型采用不同策略

        参数:
            source_path (str): 源路径
            target_path (str): 目标路径
            item_name (str): 配置项名称
            item_info (dict): 配置项信息
        """
        import os

        print(f"🔧 选择同步策略for {item_name}")

        if item_name == "Extensions":
            print(f"   使用Extensions专用同步方法")
            # Extensions目录使用专用的安全同步
            self._sync_extensions_safely(source_path, target_path)
        else:
            print(f"   使用高效目录同步方法")
            # 其他目录使用标准的高效同步
            self._sync_directory_efficiently(source_path, target_path)

    def _sync_browser_file_safely(self, source_path, target_path, item_name, item_info):
        """
        安全地同步浏览器配置文件，根据不同类型采用不同策略

        参数:
            source_path (str): 源文件路径
            target_path (str): 目标文件路径
            item_name (str): 配置项名称
            item_info (dict): 配置项信息
        """
        import os
        import shutil
        import time

        try:
            print(f"📁 创建目标目录: {os.path.dirname(target_path)}")
            # 确保目标目录存在
            os.makedirs(os.path.dirname(target_path), exist_ok=True)

            print(f"🔧 选择文件同步策略for {item_name}")

            if item_name == "Web Data":
                print(f"   使用数据库文件专用同步方法")
                # Web Data数据库文件可能被锁定，需要特殊处理
                self._sync_database_file_safely(source_path, target_path, item_name)
            elif item_name == "Preferences":
                print(f"   使用Preferences文件专用同步方法")
                # Preferences文件包含JSON配置，可以进行验证
                self._sync_preferences_file_safely(source_path, target_path)
            elif item_name == "Local State":
                print(f"   使用Local State文件专用同步方法")
                # Local State文件包含全局设置
                self._sync_local_state_file_safely(source_path, target_path)
            else:
                print(f"   使用标准文件复制方法")
                # 其他文件使用标准安全复制
                self._safe_copy_file(source_path, target_path)

        except Exception as e:
            print(f"❌ 同步{item_info['name']}文件失败: {str(e)}")
            print(f"   错误类型: {type(e).__name__}")
            raise

    def _sync_cache_directory_safely(self, source_dir, target_dir):
        """
        安全地同步Cache目录，跳过临时文件和过大的文件
        """
        import os
        import shutil

        try:
            # 确保目标目录存在
            os.makedirs(target_dir, exist_ok=True)

            # Cache目录中应该跳过的文件模式
            skip_patterns = {
                'temp', 'tmp', '.tmp', 'lock', '.lock',
                '.crdownload', '.crswap', 'journal', '.journal'
            }

            max_file_size = 50 * 1024 * 1024  # 50MB限制
            success_count = 0
            skip_count = 0

            print(f"🗂️ 开始同步Cache目录（跳过大文件和临时文件）")

            for root, dirs, files in os.walk(source_dir):
                # 计算相对路径
                rel_path = os.path.relpath(root, source_dir)
                target_root = os.path.join(target_dir, rel_path) if rel_path != '.' else target_dir

                # 确保目标子目录存在
                os.makedirs(target_root, exist_ok=True)

                # 复制文件
                for file in files:
                    # 检查是否应该跳过
                    if any(pattern in file.lower() for pattern in skip_patterns):
                        skip_count += 1
                        continue

                    source_file = os.path.join(root, file)
                    target_file = os.path.join(target_root, file)

                    try:
                        # 检查文件大小
                        if os.path.getsize(source_file) > max_file_size:
                            print(f"⏭️ 跳过大文件: {file} ({os.path.getsize(source_file) // (1024*1024)}MB)")
                            skip_count += 1
                            continue

                        self._safe_copy_file(source_file, target_file)
                        success_count += 1

                    except Exception as e:
                        print(f"⚠️ 跳过文件 {file}: {str(e)}")
                        skip_count += 1
                        continue

            print(f"📊 Cache同步完成: 成功 {success_count}, 跳过 {skip_count}")

        except Exception as e:
            print(f"❌ Cache目录同步失败: {str(e)}")
            # 降级到标准同步
            self._sync_directory_fallback(source_dir, target_dir)

    def _sync_storage_directory_safely(self, source_dir, target_dir, storage_type):
        """
        安全地同步存储目录（Local Storage, Session Storage, IndexedDB）
        """
        import os

        try:
            # 确保目标目录存在
            os.makedirs(target_dir, exist_ok=True)

            # 存储目录中应该跳过的文件模式
            skip_patterns = {
                'lock', '.lock', 'journal', '.journal',
                'temp', 'tmp', '.tmp'
            }

            success_count = 0
            skip_count = 0

            print(f"💾 开始同步{storage_type}目录")

            for root, dirs, files in os.walk(source_dir):
                # 计算相对路径
                rel_path = os.path.relpath(root, source_dir)
                target_root = os.path.join(target_dir, rel_path) if rel_path != '.' else target_dir

                # 确保目标子目录存在
                os.makedirs(target_root, exist_ok=True)

                # 复制文件
                for file in files:
                    # 检查是否应该跳过锁文件
                    if any(pattern in file.lower() for pattern in skip_patterns):
                        print(f"⏭️ 跳过锁文件: {file}")
                        skip_count += 1
                        continue

                    source_file = os.path.join(root, file)
                    target_file = os.path.join(target_root, file)

                    try:
                        self._safe_copy_file(source_file, target_file)
                        success_count += 1

                    except Exception as e:
                        print(f"⚠️ 跳过文件 {file}: {str(e)}")
                        skip_count += 1
                        continue

            print(f"📊 {storage_type}同步完成: 成功 {success_count}, 跳过 {skip_count}")

        except Exception as e:
            print(f"❌ {storage_type}目录同步失败: {str(e)}")
            # 降级到标准同步
            self._sync_directory_fallback(source_dir, target_dir)

    def _sync_database_file_safely(self, source_path, target_path, db_type):
        """
        安全地同步数据库文件（主要用于Web Data等表单数据）
        """
        import os
        import shutil
        import time

        try:
            print(f"🗄️ 同步{db_type}数据库文件")

            # 检查是否存在相关的锁文件或临时文件
            source_dir = os.path.dirname(source_path)
            base_name = os.path.basename(source_path)

            # 常见的数据库相关文件后缀
            related_files = []
            for suffix in ['', '-journal', '-wal', '-shm', '.bak']:
                related_file = os.path.join(source_dir, base_name + suffix)
                if os.path.exists(related_file):
                    related_files.append((related_file, os.path.join(os.path.dirname(target_path), base_name + suffix)))

            # 同步主文件和相关文件
            for source_file, target_file in related_files:
                try:
                    self._safe_copy_file(source_file, target_file)
                    print(f"✅ 已同步: {os.path.basename(source_file)}")
                except Exception as e:
                    print(f"⚠️ 跳过相关文件 {os.path.basename(source_file)}: {str(e)}")

        except Exception as e:
            print(f"❌ 数据库文件同步失败: {str(e)}")
            # 降级到标准文件复制
            self._safe_copy_file(source_path, target_path)

    def _sync_preferences_file_safely(self, source_path, target_path):
        """
        安全地同步Preferences文件，并验证JSON格式
        """
        import json
        import os

        try:
            print(f"⚙️ 同步浏览器设置文件")

            # 读取并验证源文件的JSON格式
            with open(source_path, 'r', encoding='utf-8') as f:
                preferences_data = json.load(f)

            # 确保目标目录存在
            os.makedirs(os.path.dirname(target_path), exist_ok=True)

            # 写入目标文件
            with open(target_path, 'w', encoding='utf-8') as f:
                json.dump(preferences_data, f, indent=2, ensure_ascii=False)

            print(f"✅ Preferences文件同步成功，包含 {len(preferences_data)} 个配置项")

        except json.JSONDecodeError as e:
            print(f"⚠️ Preferences文件JSON格式错误，使用直接复制: {str(e)}")
            self._safe_copy_file(source_path, target_path)
        except Exception as e:
            print(f"❌ Preferences文件同步失败: {str(e)}")
            # 降级到标准文件复制
            self._safe_copy_file(source_path, target_path)

    def _sync_local_state_file_safely(self, source_path, target_path):
        """
        安全地同步Local State文件
        """
        import json
        import os

        try:
            print(f"🌐 同步全局状态文件")

            # 读取并验证源文件的JSON格式
            with open(source_path, 'r', encoding='utf-8') as f:
                local_state_data = json.load(f)

            # 确保目标目录存在
            os.makedirs(os.path.dirname(target_path), exist_ok=True)

            # 写入目标文件
            with open(target_path, 'w', encoding='utf-8') as f:
                json.dump(local_state_data, f, indent=2, ensure_ascii=False)

            print(f"✅ Local State文件同步成功")

        except json.JSONDecodeError as e:
            print(f"⚠️ Local State文件JSON格式错误，使用直接复制: {str(e)}")
            self._safe_copy_file(source_path, target_path)
        except Exception as e:
            print(f"❌ Local State文件同步失败: {str(e)}")
            # 降级到标准文件复制
            self._safe_copy_file(source_path, target_path)

class NumericTableWidgetItem(QTableWidgetItem):
    """
    用于数字排序的表格项类
    
    功能说明：
    1. 支持普通数字排序
    2. 支持分数格式排序（如 "899/1133"，按/前面的数字排序）
    3. 支持"在售"列格式排序（如 "[3]25"，按]后面的数字排序）
    4. 支持"在售"列带分数格式排序（如 "[3]25/30"，按]/之间的数字排序）
    
    排序规则：
    - 有数值的项总是排在无数值项的前面
    - 无数值的项按文本排序
    - 数值项按数值大小排序
    """
    def __init__(self, value, display_text=None):
        """
        初始化数字排序表格项
        
        参数:
            value: 原始数据值，用于排序计算
            display_text: 显示文本（可选），如果不提供则使用value
        
        排序值提取逻辑:
        1. [数字]数字格式（在售列）：提取]后面的数字
        2. [数字]数字/数字格式：提取]/之间的数字  
        3. 数字/数字格式：提取/前面的数字
        4. 普通数字：直接使用
        5. 其他格式：尝试提取数字字符
        """
        # 如果提供了display_text，则使用它；否则使用value转换的字符串；如果value也为None，则为空字符串
        display_str = str(display_text) if display_text is not None else (str(value) if value is not None else "")
        super().__init__(display_str)

        self._numeric_value = None
        if value is not None:
            try:
                # 检查是否是"在售"列的格式：[上家数量]在售数量
                if isinstance(value, str) and ']' in value:
                    # 提取]之后的数字进行排序（用于"在售"列）
                    parts = value.split(']')
                    if len(parts) >= 2:
                        after_bracket = parts[1].strip()
                        # 如果]后面还有/，则取/前面的数字
                        if '/' in after_bracket:
                            slash_parts = after_bracket.split('/')
                            if len(slash_parts) >= 1 and slash_parts[0].strip():
                                try:
                                    self._numeric_value = float(slash_parts[0].strip())
                                except ValueError:
                                    pass
                        else:
                            # 如果]后面没有/，直接取]后面的数字
                            try:
                                self._numeric_value = float(after_bracket)
                            except ValueError:
                                pass
                # 检查是否是分数格式（如"899/1133"）
                elif isinstance(value, str) and '/' in value:
                    # 提取斜杠前面的数字进行排序
                    parts = value.split('/')
                    if len(parts) >= 2 and parts[0].strip():
                        try:
                            self._numeric_value = float(parts[0].strip())
                        except ValueError:
                            pass
                else:
                    # 尝试直接转换为数字
                    self._numeric_value = float(value)
            except (ValueError, TypeError):
                # 如果直接转换失败，并且原始值是字符串，尝试移除常见非数字字符再转换
                if isinstance(value, str):
                        cleaned_value_str = ''.join(filter(lambda x: x.isdigit() or x == '.' or x == '-', value))
                        if cleaned_value_str:
                            try:
                                self._numeric_value = float(cleaned_value_str)
                            except ValueError:
                                pass # 再次失败则保持 None

    def __lt__(self, other):
        """
        定义排序规则：按数值大小排序，非数值始终排在最后面（无论升序还是降序）
        
        排序逻辑:
        1. 两个都有数值：按数值大小比较
        2. 自己有数值，对方没有：自己排在前面（返回True）
        3. 自己没有数值，对方有：自己排在后面（返回False）
        4. 两个都没有数值：按文本比较
        
        注意：这个方法是PyQt表格排序的核心，修改时需要谨慎
        """
        if not isinstance(other, NumericTableWidgetItem):
            # 如果对方不是NumericTableWidgetItem，则按文本比较
            return self.text() < other.text()

        self_has_numeric = self._numeric_value is not None
        other_has_numeric = other._numeric_value is not None

        # 根据排序方向确定非数字内容应该怎样处理
        global CURRENT_SORT_ORDER
        # 确保全局变量CURRENT_SORT_ORDER被赋值，防止未定义的情况
        if 'CURRENT_SORT_ORDER' not in globals():
            CURRENT_SORT_ORDER = Qt.AscendingOrder

        if self_has_numeric and other_has_numeric:
            # 两个都有数值，直接比较数值
            return self._numeric_value < other._numeric_value
        elif self_has_numeric and not other_has_numeric:
            # 自己有数值，对方没有 - 无论升序降序，对方都应排在后面
            return True
        elif not self_has_numeric and other_has_numeric:
            # 自己没有数值，对方有 - 无论升序降序，自己都应排在后面
            return False
        else:
            # 两者都没有数值，按原始文本比较
            return self.text() < other.text()


class CheckBoxNumericTableWidgetItem(QTableWidgetItem):
    """带复选框的序号列表格项类"""
    def __init__(self, row_number):
        super().__init__("")
        self.row_number = row_number
        self.setTextAlignment(Qt.AlignCenter)
        self.setFlags(self.flags() & ~Qt.ItemIsEditable)  # 设置为不可编辑

    def __lt__(self, other):
        """定义排序规则：按行号排序"""
        if isinstance(other, CheckBoxNumericTableWidgetItem):
            return self.row_number < other.row_number
        return super().__lt__(other)


class CheckBoxDelegate(QStyledItemDelegate):
    """自定义代理，用于在序号列中显示复选框和序号"""

    # 定义信号，当复选框状态改变时发出
    checkStateChanged = pyqtSignal(int, bool)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.checked_rows = set()  # 存储被选中的行
        self.row_to_display_number = {}  # 存储行号到显示序号的映射

    def paint(self, painter, option, index):
        """绘制复选框和序号"""
        if index.column() == 0:  # 只处理第一列（序号列）
            # 获取行号和显示序号
            row = index.row()
            display_number = self.row_to_display_number.get(row, row + 1)

            # 创建一个新的样式选项，用于绘制
            opt = QStyleOptionViewItem(option)

            # 设置复选框的矩形区域
            check_rect = QRect(opt.rect.left() + 5, opt.rect.top() + (opt.rect.height() - 15) // 2, 15, 15)

            # 设置文本的矩形区域
            text_rect = QRect(opt.rect.left() + 25, opt.rect.top(), opt.rect.width() - 25, opt.rect.height())

            # 绘制背景
            opt.widget.style().drawPrimitive(QStyle.PE_PanelItemViewItem, opt, painter, opt.widget)

            # 绘制复选框
            check_state = Qt.Checked if row in self.checked_rows else Qt.Unchecked
            check_opt = QStyleOptionViewItem()
            check_opt.rect = check_rect
            check_opt.state = opt.state | QStyle.State_Enabled
            if check_state == Qt.Checked:
                check_opt.state |= QStyle.State_On
            else:
                check_opt.state |= QStyle.State_Off

            opt.widget.style().drawPrimitive(QStyle.PE_IndicatorCheckBox, check_opt, painter, opt.widget)

            # 绘制序号文本
            painter.drawText(text_rect, Qt.AlignLeft | Qt.AlignVCenter, str(display_number))
        else:
            # 对于其他列，使用默认绘制方法
            super().paint(painter, option, index)

    def editorEvent(self, event, model, option, index):
        """处理鼠标点击事件"""
        if index.column() == 0 and event.type() == event.MouseButtonRelease:
            # 计算复选框的矩形区域
            check_rect = QRect(option.rect.left() + 5, option.rect.top() + (option.rect.height() - 15) // 2, 15, 15)

            # 检查点击是否在复选框区域内
            if check_rect.contains(event.pos()):
                row = index.row()
                if row in self.checked_rows:
                    self.checked_rows.remove(row)
                    new_state = False
                else:
                    self.checked_rows.add(row)
                    new_state = True

                # 发出信号
                self.checkStateChanged.emit(row, new_state)

                # 请求重绘
                model.dataChanged.emit(index, index)
                return True

        return super().editorEvent(event, model, option, index)

    def get_checked_rows(self):
        """获取所有被选中的行"""
        return self.checked_rows

    def set_checked_rows(self, rows):
        """设置选中的行"""
        self.checked_rows = set(rows)

    def clear_checked_rows(self):
        """清除所有选中的行"""
        self.checked_rows.clear()

    def toggle_all_rows(self, table_widget, checked):
        """切换所有行的选中状态"""
        if checked:
            # 只选择可见行
            visible_rows = []
            for row in range(table_widget.rowCount()):
                if not table_widget.isRowHidden(row):
                    visible_rows.append(row)
            self.checked_rows = set(visible_rows)
        else:
            self.checked_rows.clear()

        # 请求重绘表格
        table_widget.update()

    def update_display_numbers(self, table_widget):
        """更新显示序号映射"""
        self.row_to_display_number.clear()
        visible_row_index = 1

        for row in range(table_widget.rowCount()):
            if not table_widget.isRowHidden(row):
                self.row_to_display_number[row] = visible_row_index
                visible_row_index += 1
class KuaishouShopManager(QMainWindow):
    # 实例计数，用于跟踪多开的实例数量
    instance_count = 0

    # 定义解密状态信号
    decrypt_status_signal = pyqtSignal(str)

    def __init__(self, instance_id=None):
        super().__init__()

        self.base_title_prefix = '快手小店 V99' # 新增
        self.base_window_title = self.base_title_prefix # 新增，初始值

        # 实例ID，用于多开时区分不同实例
        self.instance_id = instance_id if instance_id is not None else KuaishouShopManager.instance_count
        KuaishouShopManager.instance_count += 1

        # 服务器URL直接从辅助函数获取
        self.server_url = get_configured_server_url()
        print(f"KuaishouShopManager __init__: Server URL set to {self.server_url}") # 调试信息

        # 初始化状态管理器
        self.status_manager = StatusManager(self)

        self.cache_dir = get_resource_path("cache")
        # 账号管理缓存文件路径修改为存放在config目录下，以JSON格式存储
        self.shop_accounts_cache = get_config_path("账号管理.json")
        # 字段设置缓存文件路径修改为存放在config目录下，以JSON格式存储
        self.field_settings_cache = get_config_path("账号管理字段设置.json")
        # 旧的字段设置缓存文件路径（用于迁移）
        self.old_field_settings_cache = os.path.join(self.cache_dir, "field_settings.pkl")

        # 用户信息
        self.user_info = {}

        # 字段显示设置
        self.field_settings = []
        self.all_fields = []
        #账号管理表头排序设置
        self.numeric_sort_columns = [
            "在售", "保证金", "付款30天", "销量", "全部订单", "商品", "订单数",
            "店铺分", "PV", "UV", "待发货", "待改", "已上架",
            "货盘商品数", "运费险", "钱包", "运费"
        ] # 需要进行数值排序的列名

        # 确保缓存目录存在
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)

        # -------- 新增：确保 config 目录存在 --------
        self.stats_cache_config_dir = get_resource_path("config")
        if not os.path.exists(self.stats_cache_config_dir):
            os.makedirs(self.stats_cache_config_dir)
        # -------- 新增结束 --------

        # 加载字段设置
        self.load_field_settings()

        # 设置应用程序图标 - 使用软件运行目录的相对路径
        icon_path = get_resource_path(os.path.join("config", "imges", "logo.ico"))
        print(f"🎨 主程序尝试设置图标: {icon_path}")
        print(f"   文件是否存在: {os.path.exists(icon_path)}")

        if os.path.exists(icon_path):
            try:
                icon = QIcon(icon_path)
                if not icon.isNull():
                    self.setWindowIcon(icon)
                    print(f"✅ 主程序图标设置成功")
                else:
                    print(f"❌ 图标对象创建失败")
            except Exception as e:
                print(f"❌ 设置图标异常: {e}")
        else:
            print(f"❌ 图标文件不存在")
            print(f"   当前工作目录: {os.getcwd()}")
            print(f"   应用程序目录: {get_application_directory()}")

        self.initUI()

        # 启动时不立即显示窗口，而是先显示登录窗口
        self.hide()

        # 创建并显示登录窗口
        # LoginWindow 内部会自行调用 get_configured_server_url
        self.login_window = LoginWindow()
        self.login_window.loginSuccess.connect(self.on_login_success)

        # 尝试加载保存的凭据
        self.login_window.load_saved_credentials()

        self.login_window.show()

        # 创建状态管理器实例
        self.status_manager = StatusManager(self)

        # 连接解密状态信号
        self.decrypt_status_signal.connect(self.set_window_title_status)



    def event(self, event):
        """重写事件处理方法，处理状态事件和进度条事件"""
        # 处理状态事件
        if handle_status_event(self, event):
            return True



        return super().event(event)

    def set_window_title_status(self, status_message):
        """Updates the window title with a status message."""
        if status_message and status_message.strip():
            self.setWindowTitle(f"{self.base_window_title} - {status_message.strip()}")
        else:
            self.setWindowTitle(self.base_window_title)



    def initUI(self):
        # 设置窗口标题和大小
        _initial_base = self.base_title_prefix
        if self.instance_id == 0:
            # self.setWindowTitle('快手小店 V62') # 旧代码
            pass # base_title_prefix 已包含版本
        else:
            # self.setWindowTitle(f'快手小店 V62 - 实例 {self.instance_id + 1}') # 旧代码
            _initial_base += f' - 实例 {self.instance_id + 1}'
        self.base_window_title = _initial_base
        self.setWindowTitle(self.base_window_title)

        self.setFixedSize(1500, 750)

        # 根据实例ID调整窗口位置，避免重叠
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2 + self.instance_id * 30
        y = (screen.height() - self.height()) // 2 + self.instance_id * 30
        self.move(x, y)

        # 设置整体应用样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #F4F6FA;
            }
            QWidget {
                background-color: rgba(238, 242, 247, 0.95);
            }
            QTabWidget {
                background-color: rgba(238, 242, 247, 0.9);
            }
            QTabBar::tab {
                background-color: rgba(211, 222, 233, 0.8);
                color: #414f5c;
                padding: 8px 16px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: rgba(74, 107, 223, 0.9);
                color: white;
            }
            QScrollArea {
                background-color: rgba(238, 242, 247, 0.9);
                border: none;
            }
            QTitleBar {
                background-color: #F4F6FA;
            }
        """)

        # 主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 导航菜单 - 优化风格
        menu_frame = QFrame()
        menu_frame.setFixedHeight(46)  # 增加高度，让菜单更高一些
        menu_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                          stop:0 #3B5CA0, stop:0.5 #4A6BDF, stop:1 #3B5CA0);
                border-bottom: 1px solid rgba(42, 63, 89, 0.8);
            }
        """)

        # 添加菜单阴影
        menu_shadow = QGraphicsDropShadowEffect()
        menu_shadow.setBlurRadius(10)
        menu_shadow.setColor(QColor(0, 0, 0, 40))
        menu_shadow.setOffset(0, 2)
        menu_frame.setGraphicsEffect(menu_shadow)

        menu_layout = QHBoxLayout(menu_frame)
        menu_layout.setContentsMargins(9, 0, 9, 0)
        menu_layout.setSpacing(4)  # 增加菜单项之间的间距

        # 创建堆叠布局用于切换不同页面
        self.stacked_widget = QStackedWidget()

        # 创建账号管理页面
        self.account_page = QWidget()
        self.init_account_page()

        # 创建快手订单页面
        self.order_page = QWidget()
        self.init_order_page()

        # 创建商品管理页面
        self.product_page = QWidget()
        self.init_product_page()

        # 创建商品复制页面
        self.product_copy_page = QWidget()
        self.init_product_copy_page()

        # 创建一键下单页面
        self.one_click_order_page = QWidget()
        self.init_one_click_order_page()

        # 创建数据统计页面
        from 数据统计 import DataStatistics
        self.data_stats_widget = DataStatistics(self)
        self.data_stats_page = self.data_stats_widget

        # 创建计划管理页面
        self.plan_manager_page = QWidget()
        self.init_plan_manager_page()

        # 创建达人邀约页面
        self.influencer_invitation_page = QWidget()
        self.init_influencer_invitation_page()

        # 创建通用消息页面（用于显示开发中的功能）
        self.message_page = QWidget()
        self.init_message_page()

        # 将页面添加到堆叠布局
        self.stacked_widget.addWidget(self.account_page)
        self.stacked_widget.addWidget(self.order_page)
        self.stacked_widget.addWidget(self.product_page)
        self.stacked_widget.addWidget(self.product_copy_page)
        self.stacked_widget.addWidget(self.one_click_order_page)
        self.stacked_widget.addWidget(self.data_stats_page)
        self.stacked_widget.addWidget(self.plan_manager_page)
        self.stacked_widget.addWidget(self.influencer_invitation_page)
        self.stacked_widget.addWidget(self.message_page)

        # 按照指定顺序设置菜单项
        menu_items = [
            "商品管理",
            "一键下单",
            "快手订单",
            "样品拍单",
            "样品报价",
            "退货地址",
            "计划管理",
            "快手邀约",
            "操作记录",
            "商品复制",
            "团长合作",
            "数据统计",
            "账号管理"
        ]

        # 保存菜单按钮的引用，以便后续更改样式
        self.menu_buttons = {}

        # 定义默认样式和活动样式
        self.default_menu_style = """
            QPushButton {
                background-color: transparent;
                border: none;
                padding: 6px 6px;
                font-family: 'Microsoft YaHei';
                font-size: 16px;
                color: rgba(229, 234, 243, 0.95);
                border-radius: 3px;
                margin: 3px 1px;
            }
            QPushButton:hover {
                color: #FFFFFF;
                background-color: rgba(90, 120, 190, 0.6);
            }
        """

        self.active_menu_style = """
            QPushButton {
                background-color: rgba(255, 255, 255, 0.18);
                border: none;
                border-bottom: 2px solid rgba(255, 255, 255, 0.9);
                padding: 6px 10px;
                font-family: 'Microsoft YaHei';
                font-size: 13px;
                color: #FFFFFF;
                font-weight: bold;
                border-radius: 3px;
                margin: 3px 1px;
            }
        """

        for item in menu_items:
            menu_btn = QPushButton(item)
            menu_btn.setStyleSheet(self.default_menu_style)
            menu_layout.addWidget(menu_btn)

            # 保存按钮引用
            self.menu_buttons[item] = menu_btn

            # 添加导航按钮的点击事件
            if item == "快手订单":
                menu_btn.clicked.connect(self.show_order_page)
            elif item == "账号管理":
                menu_btn.clicked.connect(self.show_account_page)
            elif item == "商品管理":
                menu_btn.clicked.connect(self.show_product_page)
            elif item == "商品复制":
                menu_btn.clicked.connect(self.show_product_copy_page)
            elif item == "一键下单":
                menu_btn.clicked.connect(self.show_one_click_order_page)
            elif item == "样品拍单":
                menu_btn.clicked.connect(lambda: self.show_message_page("样品拍单功能开发中，敬请期待..."))
            elif item == "样品报价":
                menu_btn.clicked.connect(lambda: self.show_message_page("样品报价功能开发中，敬请期待..."))
            elif item == "退货地址":
                menu_btn.clicked.connect(lambda: self.show_message_page("退货地址功能开发中，敬请期待..."))
            elif item == "计划管理":
                menu_btn.clicked.connect(self.show_plan_manager_page)
            elif item == "快手邀约":
                menu_btn.clicked.connect(self.show_influencer_invitation_page)
            elif item == "操作记录":
                menu_btn.clicked.connect(lambda: self.show_message_page("操作记录功能开发中，敬请期待..."))
            elif item == "团长合作":
                menu_btn.clicked.connect(lambda: self.show_message_page("团长合作功能开发中，敬请期待..."))
            elif item == "数据统计":
                menu_btn.clicked.connect(self.show_data_stats_page)

        menu_layout.addStretch()

        # 添加主菜单和堆叠布局到主布局
        main_layout.addWidget(menu_frame)
        main_layout.addWidget(self.stacked_widget)

        # 默认显示账号管理页面
        self.show_account_page()

    def init_account_page(self):
        """初始化账号管理页面"""
        account_layout = QVBoxLayout(self.account_page)
        account_layout.setContentsMargins(0, 0, 0, 0)
        account_layout.setSpacing(0)

        # 店铺筛选区域
        filter_frame = QFrame()
        filter_frame.setFixedHeight(45)
        filter_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-bottom: 1px solid #edf0f7;
            }
        """)
        filter_layout = QHBoxLayout(filter_frame)
        filter_layout.setContentsMargins(8, 0, 8, 0)

        # 账号列表按钮移到顶部
        account_list_btn = QPushButton("账号列表")
        account_list_btn.setFixedSize(80, 32)  # 稍微加宽加高
        account_list_btn.setStyleSheet("""
            QPushButton {
                background-color: #f5f7fa;
                color: #333333;
                border: 1px solid #d3d7df;
                border-radius: 6px;
                padding: 5px 8px;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                font-weight: bold;
                letter-spacing: 1px;
            }
            QPushButton:hover {
                background-color: #e9ecf3;
                border: 1.5px solid #bfc6d1;
                color: #409EFF;
            }
            QPushButton:pressed {
                background-color: #e0e3ea;
                border: 1.5px solid #a6b0be;
                color: #3070c9;
            }
        """)

        # 添加按钮阴影
        btn_shadow = QGraphicsDropShadowEffect()
        btn_shadow.setBlurRadius(10)
        btn_shadow.setColor(QColor(54, 79, 107, 60))
        btn_shadow.setOffset(0, 2)
        account_list_btn.setGraphicsEffect(btn_shadow)

        account_list_btn.clicked.connect(self.on_account_list_clicked)

        filter_label = QLabel("店铺:")
        filter_label.setStyleSheet("font-family: 'Microsoft YaHei'; font-size: 14px; color: #5a6677;")

        # 创建店铺搜索输入框（可编辑的下拉框）
        self.filter_combo = QComboBox()
        self.filter_combo.setFixedWidth(150)
        self.filter_combo.setFixedHeight(30)
        self.filter_combo.setEditable(True)  # 设置为可编辑
        self.filter_combo.setInsertPolicy(QComboBox.NoInsert)  # 不允许插入新项目
        self.filter_combo.lineEdit().setPlaceholderText("输入姓名/店铺/执照/旺旺搜索...")  # 设置占位符文本

        # 连接文本变化事件实现实时筛选
        self.filter_combo.lineEdit().textChanged.connect(self.on_shop_filter_changed)

        self.filter_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #e2e8f0;
                border-radius: 4px;
                padding: 4px 10px;
                background-color: white;
                font-family: 'Microsoft YaHei';
                color: #334155;
            }
            QComboBox::drop-down {
                width: 20px;
                border: none;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #e2e8f0;
                border-radius: 4px;
                selection-background-color: #f1f5f9;
            }
            QLineEdit {
                border: none;
                padding: 0px;
                background-color: transparent;
                font-family: 'Microsoft YaHei';
                color: #334155;
            }
        """)

        # 创建搜索框清空按钮
        self.filter_clear_btn = QToolButton()
        self.filter_clear_btn.setText("✕")
        self.filter_clear_btn.setToolTip("清空搜索")
        self.filter_clear_btn.setFixedSize(30, 30)
        self.filter_clear_btn.setStyleSheet("""
            QToolButton {
                border: 1px solid #e2e8f0;
                border-radius: 4px;
                background-color: white;
                font-size: 12px;
                color: #6b7280;
            }
            QToolButton:hover {
                background-color: #f1f5f9;
                color: #374151;
            }
            QToolButton:pressed {
                background-color: #e5e7eb;
            }
        """)
        self.filter_clear_btn.clicked.connect(self.clear_search_filter)
#刷新按扭
        refresh_btn = QToolButton()
        refresh_btn.setText("🔄")
        refresh_btn.setToolTip("刷新账号管理表格")
        refresh_btn.setFixedSize(30, 30)
        refresh_btn.setStyleSheet("""
            QToolButton {
                border: 1px solid #e2e8f0;
                border-radius: 4px;
                background-color: white;
                font-size: 14px;
            }
            QToolButton:hover {
                background-color: #f1f5f9;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_account_table_from_local)

        # 添加设置按钮
        settings_btn = QToolButton()
        settings_btn.setText("⚙️")
        settings_btn.setFixedSize(30, 30)
        settings_btn.setStyleSheet("""
            QToolButton {
                border: 1px solid #e2e8f0;
                border-radius: 4px;
                background-color: white;
                font-size: 14px;
            }
            QToolButton:hover {
                background-color: #f1f5f9;
            }
        """)
        settings_btn.clicked.connect(self.show_field_settings)





        # 添加解密复选框
        self.decrypt_checkbox = QCheckBox("解密")
        self.decrypt_checkbox.setChecked(True)  # 默认勾选

        # 创建对勾图标的像素图
        import tempfile
        import os
        from PyQt5.QtGui import QPixmap

        # 创建16x16的像素图
        checkmark_pixmap = QPixmap(16, 16)
        checkmark_pixmap.fill(Qt.transparent)

        # 绘制对勾
        painter = QPainter(checkmark_pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        pen = QPen(QColor(255, 255, 255), 2)  # 白色，2像素宽
        pen.setCapStyle(Qt.RoundCap)
        pen.setJoinStyle(Qt.RoundJoin)
        painter.setPen(pen)

        # 绘制对勾路径
        painter.drawLine(3, 8, 6, 11)  # 对勾的左半部分
        painter.drawLine(6, 11, 12, 5)  # 对勾的右半部分
        painter.end()

        # 保存图标到临时文件
        temp_dir = tempfile.gettempdir()
        checkmark_path = os.path.join(temp_dir, "checkmark.png")
        checkmark_pixmap.save(checkmark_path)

        self.decrypt_checkbox.setStyleSheet(f"""
            QCheckBox {{
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                color: #5a6677;
                spacing: 5px;
            }}
            QCheckBox::indicator {{
                width: 16px;
                height: 16px;
                border: 2px solid #c0c4cc;
                border-radius: 3px;
                background-color: white;
            }}
            QCheckBox::indicator:checked {{
                background-color: #409eff;
                border-color: #409eff;
                image: url({checkmark_path.replace(os.sep, '/')});
            }}
            QCheckBox::indicator:hover {{
                border-color: #66b3ff;
            }}
            QCheckBox::indicator:unchecked {{
                background-color: white;
                border-color: #c0c4cc;
            }}
            QCheckBox::indicator:unchecked:hover {{
                border-color: #409eff;
                background-color: #f0f8ff;
            }}
        """)

        filter_layout.addWidget(account_list_btn)
        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(self.filter_combo)
        filter_layout.addWidget(self.filter_clear_btn)

        # 添加店铺分类筛选
        category_label = QLabel("分类:")
        category_label.setStyleSheet("font-family: 'Microsoft YaHei'; font-size: 14px; color: #5a6677;")

        # 创建并设置分类下拉框
        self.category_combo = QComboBox()
        self.category_combo.setFixedWidth(100)
        self.category_combo.setFixedHeight(25)

        # 设置下拉框样式，增强选中项的显示效果
        self.category_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #c0c4cc;
                border-radius: 4px;
                padding: 4px 18px 4px 8px;
                background-color: white;
                font-family: 'Microsoft YaHei';
                color: #334155;
                max-height: 25px;
            }

            QComboBox:hover {
                border: 1px solid #409eff;
            }

            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left-width: 0px;
            }

            QComboBox::down-arrow {
                image: url(config/imges/箭头30.png);
                width: 12px;
                height: 12px;
            }

            QComboBox QAbstractItemView {
                border: 1px solid #c0c4cc;
                background-color: white;
                selection-background-color: #ecf5ff;
                selection-color: #409eff;
                outline: none;
                padding: 5px;
            }

            QComboBox QAbstractItemView::item {
                height: 30px;
                padding: 5px;
            }

            QComboBox QAbstractItemView::item:hover {
                background-color: #e2e8f0;
                border-radius: 2px;
            }

            QComboBox QAbstractItemView::item:selected {
                background-color: #ecf5ff;
                color: #409eff;
                font-weight: bold;
                border-radius: 2px;
            }
        """)
        self.category_combo.currentIndexChanged.connect(self.on_category_filter_changed)

        # 创建"暂无权限"复选框
        self.no_permission_checkbox = QCheckBox("暂无权限")

        # 创建对勾图标的像素图（与解密复选框相同）
        # 创建16x16的像素图
        no_permission_checkmark_pixmap = QPixmap(16, 16)
        no_permission_checkmark_pixmap.fill(Qt.transparent)

        # 在像素图上绘制对勾
        no_permission_painter = QPainter(no_permission_checkmark_pixmap)
        no_permission_painter.setRenderHint(QPainter.Antialiasing)

        # 设置画笔
        no_permission_pen = QPen(Qt.white)
        no_permission_pen.setWidth(2)
        no_permission_painter.setPen(no_permission_pen)

        # 绘制对勾路径
        no_permission_painter.drawLine(QPoint(3, 8), QPoint(6, 11))
        no_permission_painter.drawLine(QPoint(6, 11), QPoint(12, 4))
        no_permission_painter.end()

        # 保存到临时文件
        no_permission_temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        no_permission_checkmark_path = no_permission_temp_file.name
        no_permission_temp_file.close()
        no_permission_checkmark_pixmap.save(no_permission_checkmark_path)

        self.no_permission_checkbox.setStyleSheet(f"""
            QCheckBox {{
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                color: #5a6677;
                spacing: 5px;
            }}
            QCheckBox::indicator {{
                width: 16px;
                height: 16px;
                border: 2px solid #c0c4cc;
                border-radius: 3px;
                background-color: white;
            }}
            QCheckBox::indicator:checked {{
                background-color: #409eff;
                border-color: #409eff;
                image: url({no_permission_checkmark_path.replace(os.sep, '/')});
            }}
            QCheckBox::indicator:hover {{
                border-color: #66b3ff;
            }}
            QCheckBox::indicator:unchecked {{
                background-color: white;
                border-color: #c0c4cc;
            }}
            QCheckBox::indicator:unchecked:hover {{
                border-color: #409eff;
                background-color: #f0f8ff;
            }}
        """)
        # 连接复选框状态变化信号到新的筛选方法
        self.no_permission_checkbox.stateChanged.connect(self.on_no_permission_checkbox_changed)

        # 添加同步按钮
        sync_btn = QPushButton("同步")
        sync_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                padding: 3px 8px;
                background-color: #FFFFFF;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e5e5e5;
            }
        """)
        sync_btn.clicked.connect(self.sync_browser_config)

        filter_layout.addWidget(category_label)
        filter_layout.addWidget(self.category_combo)
        filter_layout.addWidget(self.no_permission_checkbox)
        filter_layout.addWidget(refresh_btn)
        filter_layout.addWidget(settings_btn)
        filter_layout.addWidget(self.decrypt_checkbox)
        filter_layout.addWidget(sync_btn)

        # 添加统计标签
        # 余额统计标签
        self.balance_label = QLabel("余额: 0")
        self.balance_label.setAlignment(Qt.AlignCenter)
        self.balance_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                color: #333333;
                background-color: #e8f5e8;
                border: 1px solid #c3e6c3;
                border-radius: 4px;
                padding: 4px 8px;
                margin-right: 5px;
                height: 27px;
                min-height: 27px;
                max-height: 27px;
            }
        """)
        filter_layout.addWidget(self.balance_label)

        # 保证金统计标签
        self.deposit_label = QLabel("保证金: 0")
        self.deposit_label.setAlignment(Qt.AlignCenter)
        self.deposit_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                color: #333333;
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 4px;
                padding: 4px 8px;
                margin-right: 5px;
                height: 27px;
                min-height: 27px;
                max-height: 27px;
            }
        """)
        filter_layout.addWidget(self.deposit_label)

        # 运费险统计标签
        self.freight_insurance_label = QLabel("运费险: 0")
        self.freight_insurance_label.setAlignment(Qt.AlignCenter)
        self.freight_insurance_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                color: #333333;
                background-color: #e3f2fd;
                border: 1px solid #bbdefb;
                border-radius: 4px;
                padding: 4px 8px;
                margin-right: 5px;
                height: 27px;
                min-height: 27px;
                max-height: 27px;
            }
        """)
        filter_layout.addWidget(self.freight_insurance_label)

        # 邀约统计标签
        self.invitation_label = QLabel("邀约: 0")
        self.invitation_label.setAlignment(Qt.AlignCenter)
        self.invitation_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                color: #333333;
                background-color: #f3e5f5;
                border: 1px solid #ce93d8;
                border-radius: 4px;
                padding: 4px 8px;
                margin-right: 5px;
                height: 27px;
                min-height: 27px;
                max-height: 27px;
            }
        """)
        filter_layout.addWidget(self.invitation_label)

        # 运费统计标签
        self.shipping_fee_label = QLabel("运费: 0")
        self.shipping_fee_label.setAlignment(Qt.AlignCenter)
        self.shipping_fee_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                color: #333333;
                background-color: #fff8e1;
                border: 1px solid #ffcc02;
                border-radius: 4px;
                padding: 4px 8px;
                margin-right: 5px;
                height: 27px;
                min-height: 27px;
                max-height: 27px;
            }
        """)
        filter_layout.addWidget(self.shipping_fee_label)

        filter_layout.addStretch()

        # 添加总计显示标签
        self.total_shops_label = QLabel("总店铺: 0 | 已选: 0")
        self.total_shops_label.setAlignment(Qt.AlignCenter)
        self.total_shops_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                color: #333333;
                background-color: #f5f5f5;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 4px 8px;
                margin-right: 10px;
                height: 27px;
                min-height: 27px;
                max-height: 27px;
            }
        """)
        filter_layout.addWidget(self.total_shops_label)
        # 主表格区域
        main_frame = QFrame()
        main_frame.setFixedHeight(545)
        # main_frame.setStyleSheet("""
        #     QFrame {
        #         background-color: #f9fafc;
        #     }
        # """)

        main_content_layout = QVBoxLayout(main_frame)
        main_content_layout.setContentsMargins(8, 0, 8, 15)
        main_content_layout.setSpacing(10)


        # 主表格
        self.table_widget = QTableWidget()
        self.table_widget.setAlternatingRowColors(True)
        self.table_widget.setSortingEnabled(True) # <--- 启用排序
        self.table_widget.setWordWrap(False)
        self.table_widget.setTextElideMode(Qt.ElideRight)

        # 设置表格选择行为 - 选择整行
        self.table_widget.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table_widget.setSelectionMode(QAbstractItemView.SingleSelection)

        # 连接排序信号到更新序号的方法
        self.table_widget.horizontalHeader().sortIndicatorChanged.connect(
            self.on_account_table_sort_changed
        )

        # 隐藏行号列
        self.table_widget.verticalHeader().setVisible(False)

        # 设置表格滚动属性
        self.table_widget.setHorizontalScrollMode(QAbstractItemView.ScrollPerPixel)
        self.table_widget.setVerticalScrollMode(QAbstractItemView.ScrollPerPixel)

        # 设置表格样式
        # self.table_widget.setStyleSheet("""
        #     QTableWidget {
        #         border: none;
        #         background-color: rgba(245, 247, 250, 0.85);
        #         gridline-color: rgba(221, 228, 237, 0.8);
        #         border-radius: 6px;
        #         font-family: 'Microsoft YaHei';
        #     }
        #     QTableWidget::item {
        #         padding: 6px;
        #         border-bottom: 1px solid rgba(221, 228, 237, 0.7);
        #     }
        #     QTableWidget::item:alternate {
        #         background-color: red; /* 交替行颜色 */
        #     }
        #     QTableWidget::item:selected {
        #         background-color: rgba(229, 234, 243, 0.85);
        #         color: #364f6b;
        #     }
        #     QTableWidget::item:first-column {
        #         background-color: rgba(238, 242, 247, 0.9);
        #         border-right: 1px solid rgba(198, 208, 219, 0.9);
        #         font-weight: bold;
        #     }
        #     QHeaderView::section {
        #         background-color: rgba(211, 222, 233, 0.85);
        #         padding: 8px;
        #         border: none;
        #         border-bottom: 1px solid rgba(181, 199, 217, 0.8);
        #         font-weight: bold;
        #         color: #364f6b;
        #         font-family: 'Microsoft YaHei';
        #     }
        #     QHeaderView::section:first {
        #         background-color: rgba(195, 209, 224, 0.9);
        #         border-right: 1px solid rgba(181, 199, 217, 0.8);
        #     }
        #     QScrollBar:vertical {
        #         border: none;
        #         background: rgba(238, 242, 247, 0.6);
        #         width: 6px;
        #         border-radius: 3px;
        #     }
        #     QScrollBar::handle:vertical {
        #         background: rgba(166, 183, 200, 0.8);
        #         border-radius: 3px;
        #     }
        #     QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
        #         border: none;
        #         background: none;
        #     }
        #     QScrollBar:horizontal {
        #         border: none;
        #         background: rgba(238, 242, 247, 0.6);
        #         height: 6px;
        #         border-radius: 3px;
        #     }
        #     QScrollBar::handle:horizontal {
        #         background: rgba(166, 183, 200, 0.8);
        #         border-radius: 3px;
        #     }
        #     QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
        #         border: none;
        #         background: none;
        #     }
        # """)

        # 设置初始表格列
        headers = ["店铺ID", "店铺名称", "联系人", "微信昵称", "手机号", "微信描述", "店铺分类",
                  "微信记录", "商品数", "接授权期", "流加时间", "累水漫", "推销分", "商品分", "物流分"]

        self.table_widget.setColumnCount(len(headers))
        self.table_widget.setHorizontalHeaderLabels(headers)

        # 设置表格行数
        self.table_widget.setRowCount(20)

        # 设置表格右键菜单
        self.table_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table_widget.customContextMenuRequested.connect(self.show_table_context_menu)

        # 设置表格双击事件
        self.table_widget.itemDoubleClicked.connect(self.on_table_double_clicked)

        # 调整列宽
        for col in range(len(headers)):
            self.table_widget.setColumnWidth(col, 90)
            self.table_widget.horizontalHeader().setSectionResizeMode(col, QHeaderView.Fixed)

        main_content_layout.addWidget(self.table_widget)

        # 底部面板
        bottom_frame = QFrame()
        bottom_frame.setFixedHeight(115)
        bottom_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(245, 247, 250, 0.85);
                border-top: 1px solid rgba(221, 228, 237, 0.8);
                border-radius: 0 0 6px 6px; /* 只圆角底部 */
            }
        """)

        # 使用垂直布局来控制统计标签的位置
        bottom_layout = QVBoxLayout(bottom_frame)
        bottom_layout.setContentsMargins(20, 5, 20, 15)  # 顶部间距5px
        bottom_layout.setSpacing(0)

        # 创建水平布局容器用于统计标签
        stats_container = QHBoxLayout()
        stats_container.setContentsMargins(0, 0, 0, 0)

        # 底部统计标签区域 - 使用多彩标签样式，保持原来的统计含义
        # 总商品标签 - 绿色（原来的总商品数）
        self.all_shops_label = QLabel("总商品: 0")
        self.all_shops_label.setAlignment(Qt.AlignCenter)
        self.all_shops_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                color: #333333;
                background-color: #e8f5e8;
                border: 1px solid #c3e6c3;
                border-radius: 4px;
                padding: 4px 8px;
                margin-right: 5px;
                height: 27px;
                min-height: 27px;
                max-height: 27px;
            }
        """)

        # 有商品店铺标签 - 黄色（原来的有商品店铺数）
        self.on_sale_products_label = QLabel("有商品店铺: 0")
        self.on_sale_products_label.setAlignment(Qt.AlignCenter)
        self.on_sale_products_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                color: #333333;
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 4px;
                padding: 4px 8px;
                margin-right: 5px;
                height: 27px;
                min-height: 27px;
                max-height: 27px;
            }
        """)

        # 平均每店标签 - 蓝色（原来的平均每店商品数）
        self.total_products_label = QLabel("平均每店: 0")
        self.total_products_label.setAlignment(Qt.AlignCenter)
        self.total_products_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                color: #333333;
                background-color: #e3f2fd;
                border: 1px solid #bbdefb;
                border-radius: 4px;
                padding: 4px 8px;
                margin-right: 5px;
                height: 27px;
                min-height: 27px;
                max-height: 27px;
            }
        """)

        # 将统计标签添加到水平布局
        stats_container.addWidget(self.all_shops_label)
        stats_container.addWidget(self.on_sale_products_label)
        stats_container.addWidget(self.total_products_label)
        stats_container.addStretch(1)  # 右侧伸缩空间

        # 将水平布局添加到垂直布局的顶部
        bottom_layout.addLayout(stats_container)
        bottom_layout.addStretch(1)  # 底部伸缩空间，让统计标签贴顶显示

        # 添加所有部件到账号页面布局
        account_layout.addWidget(filter_frame)
        account_layout.addWidget(main_frame, 1)
        account_layout.addWidget(bottom_frame)

    def init_order_page(self):
        """初始化快手订单页面"""
        from 快手订单 import OrderManager

        # 创建快手订单页面布局
        order_layout = QVBoxLayout(self.order_page)
        order_layout.setContentsMargins(0, 0, 0, 0)
        order_layout.setSpacing(0)

        # 创建快手订单组件
        self.order_widget = OrderManager()
        # 获取快手订单窗口的中央部件
        order_widget = self.order_widget.centralWidget()

        # 将快手订单组件添加到布局中
        order_layout.addWidget(order_widget)

    def show_account_page(self):
        """显示账号管理页面"""
        self.stacked_widget.setCurrentWidget(self.account_page)

        # 更新菜单按钮样式 - 使用统一的方法
        self.update_menu_styles("账号管理")

    def show_order_page(self):
        """显示快手订单页面"""
        self.stacked_widget.setCurrentWidget(self.order_page)

        # 更新菜单按钮样式 - 使用统一的方法
        self.update_menu_styles("快手订单")

    def init_product_page(self):
        """初始化商品管理页面"""
        # 创建商品管理页面的布局
        product_layout = QVBoxLayout(self.product_page)
        product_layout.setContentsMargins(0, 0, 0, 0)
        product_layout.setSpacing(0)

        # 使用商品管理模块，它内部会包含树形菜单
        self.product_manager = ProductManager()
        product_widget = self.product_manager.centralWidget()

        # 添加到布局
        product_layout.addWidget(product_widget)

        # 如果有分类数据则立即更新
        if hasattr(self, 'store_categories') and self.store_categories:
            self.product_manager.update_category_tree(
                self.store_categories,
                getattr(self, 'category_stores', {})
            )

    def show_product_page(self):
        """显示商品管理页面"""
        self.stacked_widget.setCurrentWidget(self.product_page)

        # 如果存在存储的分类数据，更新分类树
        if hasattr(self, 'store_categories') and self.store_categories:
            category_stores = getattr(self, 'category_stores', {})

            # 打印调试信息
            print("正在更新分类树...")
            print(f"分类数: {len(self.store_categories)}")
            print(f"店铺分类示例: {list(self.store_categories.keys())[:3]}")

            # 确保product_manager和category_tree已初始化
            if hasattr(self, 'product_manager'):
                self.product_manager.update_category_tree(self.store_categories, category_stores)

        # 更新菜单按钮样式 - 使用统一的方法
        self.update_menu_styles("商品管理")

    def load_from_cache(self):
        """从本地缓存加载快手小店表的数据，使用JSON格式"""
        try:
            if os.path.exists(self.shop_accounts_cache):
                with open(self.shop_accounts_cache, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)

                # 解析缓存数据
                if 'data' in cache_data and 'timestamp' in cache_data:
                    records = cache_data['data']
                    timestamp = cache_data['timestamp']

                    # 显示缓存时间 - 修改为简单显示"缓存加载"
                    self.set_window_title_status("账号数据 - 已从缓存加载")

                    # 创建定时器，3秒后清除标题栏状态
                    QTimer.singleShot(3000, lambda: self.set_window_title_status(""))

                    # 显示数据
                    self.display_data(records)
                    return True
            return False
        except Exception as e:
            print(f"加载缓存失败: {str(e)}")
            return False

    def save_to_cache(self, records):
        """将快手小店表的数据保存到本地缓存，使用JSON格式"""
        try:
            # 创建包含数据和时间戳的缓存对象
            cache_data = {
                'data': records,
                'timestamp': datetime.now().timestamp()
            }

            # 确保config目录存在
            config_dir = os.path.dirname(self.shop_accounts_cache)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)

            # 保存到本地文件（JSON格式）
            with open(self.shop_accounts_cache, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=4)

            return True
        except Exception as e:
            print(f"保存缓存失败: {str(e)}")
            return False

    def load_from_server(self):
        """从服务器加载A快手小店授权表的数据并更新缓存"""
        # 更新标题栏状态，显示正在加载
        self.set_window_title_status("账号数据 - 正在从服务器加载...")

        # 创建并启动后台加载线程，传递最新的服务器地址
        self.load_thread = LoadDataThread(get_configured_server_url(), "A快手小店授权表")
        self.load_thread.data_loaded.connect(self.on_data_loaded)
        self.load_thread.start()

        # 添加安全超时机制，确保即使加载过程出现问题，标题栏提示也会在30秒后自动清除
        # 但只有在状态仍然是加载状态时才清除
        def clear_loading_status():
            current_title = self.windowTitle()
            if "正在从服务器加载" in current_title or "正在解密" in current_title:
                self.set_window_title_status("加载超时，请重试")
                # 超时错误状态也要在5秒后清除
                QTimer.singleShot(5000, lambda: self.set_window_title_status(""))

        QTimer.singleShot(30000, clear_loading_status)

    def on_data_loaded(self, records, success):
        """处理数据加载完成事件"""
        if success and records:
            # 保存到缓存
            self.save_to_cache(records)

            # 显示数据
            self.display_data(records)

            # 检查是否需要解密
            if hasattr(self, 'decrypt_checkbox') and self.decrypt_checkbox.isChecked():
                # 如果需要解密，直接显示解密状态，不显示"已从服务器加载"
                self.set_window_title_status("账号数据 - 正在解密...")
                # 启动解密线程
                threading.Thread(target=self._decrypt_accounts_thread, args=(records,), daemon=True).start()
                # 注意：解密线程完成后会在 _on_decrypt_completed 方法中清除状态
            else:
                # 不需要解密时才显示"已从服务器加载"
                self.set_window_title_status("账号数据 - 已从服务器加载")
                # 创建定时器，3秒后清除标题栏状态
                QTimer.singleShot(3000, lambda: self.set_window_title_status(""))

            # 自动更新一键下单页面的店铺下拉框
            self.update_one_click_order_shop_combo()
        else:
            QMessageBox.warning(self, "数据错误", "加载数据失败")
            self.set_window_title_status("账号数据 - 加载失败")
            # 失败时也要清除状态
            QTimer.singleShot(3000, lambda: self.set_window_title_status(""))

    def _decrypt_accounts_thread(self, records):
        """
        后台线程解密账号数据

        参数:
            records (list): 账号记录列表
        """
        try:
            print("开始解密账号数据...")

            decrypted_records = []
            success_count = 0
            total_count = len(records)

            # 使用信号槽机制更新状态
            self.decrypt_status_signal.emit("正在解密账号数据...")

            for i, record in enumerate(records):
                if not isinstance(record, dict):
                    decrypted_records.append(record)
                    continue

                # 获取店铺名称
                shop_name = record.get("店铺名称", "")
                if not shop_name:
                    print(f"第{i+1}个记录缺少店铺名称，跳过解密")
                    decrypted_records.append(record)
                    continue

                try:
                    # 调用解密API
                    decrypt_url = "http://122.114.227.188:8082"
                    decrypt_data = {
                        "sjbcx": "ks",
                        "dpmc": shop_name
                    }

                    print(f"正在解密店铺: {shop_name}")
                    response = requests.post(decrypt_url, json=decrypt_data, timeout=10)

                    if response.status_code == 200:
                        decrypt_result = response.json()

                        # 检查返回的数据格式
                        # API返回格式: {"num": "1", "data": [{"店铺ID": "...", "店铺名称": "...", "cookie": "...", "accesstoken": "..."}]}
                        if (isinstance(decrypt_result, dict) and
                            "data" in decrypt_result and
                            isinstance(decrypt_result["data"], list) and
                            len(decrypt_result["data"]) > 0):

                            # 获取第一个数据项
                            decrypt_data = decrypt_result["data"][0]

                            if isinstance(decrypt_data, dict) and "cookie" in decrypt_data and "accesstoken" in decrypt_data:
                                # 创建解密后的记录副本
                                decrypted_record = record.copy()
                                decrypted_record["cookie"] = decrypt_data["cookie"]
                                decrypted_record["accesstoken"] = decrypt_data["accesstoken"]

                                decrypted_records.append(decrypted_record)
                                success_count += 1
                                print(f"店铺 {shop_name} 解密成功")
                            else:
                                print(f"店铺 {shop_name} 解密数据项格式错误: {decrypt_data}")
                                decrypted_records.append(record)
                        else:
                            print(f"店铺 {shop_name} 解密返回数据格式错误: {decrypt_result}")
                            decrypted_records.append(record)
                    else:
                        print(f"店铺 {shop_name} 解密失败，HTTP状态码: {response.status_code}")
                        decrypted_records.append(record)

                except requests.exceptions.RequestException as e:
                    print(f"店铺 {shop_name} 解密网络请求失败: {str(e)}")
                    decrypted_records.append(record)
                except Exception as e:
                    print(f"店铺 {shop_name} 解密过程出错: {str(e)}")
                    decrypted_records.append(record)

                # 更新进度 - 使用信号槽机制显示解密进度
                progress = (i + 1) / total_count * 100
                current_progress = i + 1  # 保存当前进度值
                progress_msg = f"解密进度: {current_progress}/{total_count} ({progress:.1f}%)"

                # 使用信号槽机制更新状态
                self.decrypt_status_signal.emit(progress_msg)

            # 解密完成，保存到缓存文件
            cache_path = get_config_path('账号管理.json')
            try:
                # 确保config目录存在
                config_dir = os.path.dirname(cache_path)
                if not os.path.exists(config_dir):
                    os.makedirs(config_dir)

                # 保存解密后的数据，使用与load_from_cache一致的格式
                cache_data = {
                    'data': decrypted_records,
                    'timestamp': datetime.now().timestamp()
                }
                with open(cache_path, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, ensure_ascii=False, indent=4)

                print(f"解密数据已保存到: {cache_path}")

                # 通知主线程解密完成
                print("准备调用解密完成处理方法...")
                # 使用简单的方式：将数据存储为实例变量，然后用QTimer调用
                self._decrypt_result_data = {
                    'records': decrypted_records,
                    'success_count': success_count,
                    'total_count': total_count
                }
                # 使用QTimer在主线程中调用
                QTimer.singleShot(0, self._process_decrypt_result)

            except Exception as e:
                print(f"保存解密数据失败: {str(e)}")
                error_msg = f"解密失败: 保存数据失败 - {str(e)}"
                self.decrypt_status_signal.emit(error_msg)

        except Exception as e:
            print(f"解密线程发生错误: {str(e)}")
            error_msg = f"解密失败: {str(e)}"
            self.decrypt_status_signal.emit(error_msg)

    def on_account_list_clicked(self):
        """
        账号列表按钮点击事件处理
        根据解密复选框的状态决定加载方式

        功能：
        - 当解密复选框勾选时：先从服务器加载账号列表，然后立即进行解密
        - 当解密复选框未勾选时：直接从服务器加载数据
        - 所有状态和错误信息都在标题栏显示
        """
        try:
            # 检查解密复选框状态
            if hasattr(self, 'decrypt_checkbox') and self.decrypt_checkbox.isChecked():
                # 解密复选框已勾选，先从服务器加载数据，然后立即解密
                print("解密复选框已勾选，从服务器加载数据并解密")
                self.load_from_server()  # 从服务器加载，加载完成后会自动触发解密
            else:
                # 解密复选框未勾选，从服务器加载
                print("解密复选框未勾选，从服务器加载数据")
                self.load_from_server()

        except Exception as e:
            print(f"处理账号列表点击事件时出错: {str(e)}")
            error_msg = f"加载失败: {str(e)}"
            self.set_window_title_status(error_msg)
            # 错误状态也要在一定时间后清除
            QTimer.singleShot(5000, lambda: self.set_window_title_status(""))

    def _process_decrypt_result(self):
        """
        处理解密结果的方法（在主线程中调用）
        """
        try:
            if not hasattr(self, '_decrypt_result_data'):
                print("错误: 没有找到解密结果数据")
                return

            data = self._decrypt_result_data
            decrypted_records = data['records']
            success_count = data['success_count']
            total_count = data['total_count']

            print(f"开始处理解密结果: 成功 {success_count}/{total_count} 个店铺")

            # 调用原来的处理方法
            self._on_decrypt_completed(decrypted_records, success_count, total_count)

            # 清理临时数据
            delattr(self, '_decrypt_result_data')

        except Exception as e:
            print(f"处理解密结果时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _on_decrypt_completed(self, decrypted_records, success_count, total_count):
        """
        解密完成后的处理方法

        参数:
            decrypted_records (list): 解密后的记录列表
            success_count (int): 成功解密的数量
            total_count (int): 总数量
        """
        try:
            print(f"开始处理解密完成事件: 成功 {success_count}/{total_count} 个店铺")
            print(f"解密后的记录数量: {len(decrypted_records)}")

            # 使用信号槽机制显示解密完成状态
            final_status = f"解密完成: 成功 {success_count}/{total_count} 个店铺"
            self.decrypt_status_signal.emit(final_status)
            print(f"已设置解密完成状态: {final_status}")

            # 刷新显示解密后的数据
            print("正在刷新界面显示解密后的数据...")
            self.display_data(decrypted_records)
            print("界面刷新完成")

            # 更新店铺数量显示
            if hasattr(self, 'update_shop_count_display'):
                self.update_shop_count_display()
                print("已更新店铺数量显示")

            # 延迟显示解密完成状态，确保用户能看到完成信息
            def show_final_status():
                final_msg = f"✅ 解密完成: 成功 {success_count}/{total_count} 个店铺"
                # 使用信号槽机制显示最终状态
                self.decrypt_status_signal.emit(final_msg)
                print(f"已显示最终解密完成状态: {final_msg}")

                # 15秒后清除标题栏状态
                def clear_final_status():
                    self.decrypt_status_signal.emit("")
                    print("已清除解密完成状态")

                QTimer.singleShot(15000, clear_final_status)

            # 延迟2秒后显示最终状态，确保数据刷新完成
            QTimer.singleShot(2000, show_final_status)

            print(f"解密完成处理结束: 成功解密 {success_count}/{total_count} 个店铺")

        except Exception as e:
            print(f"处理解密完成事件时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            error_msg = f"❌ 解密后处理失败: {str(e)}"

            # 使用信号槽机制显示错误状态
            self.decrypt_status_signal.emit(error_msg)
            # 5秒后清除错误状态
            QTimer.singleShot(5000, lambda: self.decrypt_status_signal.emit(""))

    def update_category_filter_combo(self):
        """更新店铺分类筛选下拉框"""
        if not hasattr(self, 'category_combo'):
            return

        # 保存当前选中的分类
        current_category = self.category_combo.currentText() if self.category_combo.count() > 0 else ""

        # 清空下拉框
        self.category_combo.clear()

        # 添加"全部"选项
        self.category_combo.addItem("全部")

        # 从店铺分类数据中添加选项
        if hasattr(self, 'store_categories') and self.store_categories:
            for category in sorted(self.store_categories.keys()):
                self.category_combo.addItem(category)

        # 在最后面添加"暂无权限"选项
        self.category_combo.addItem("暂无权限")

        # 如果之前有选中的分类，尝试还原
        if current_category:
            index = self.category_combo.findText(current_category)
            if index >= 0:
                self.category_combo.setCurrentIndex(index)

    def apply_combined_filters(self):
        """同时应用分类筛选和暂无权限筛选"""
        if not hasattr(self, 'table_widget'):
            return



        # 暂时禁用表格排序功能，防止在筛选过程中触发排序
        was_sorting_enabled = self.table_widget.isSortingEnabled()
        self.table_widget.setSortingEnabled(False)

        # 获取当前选中的分类
        selected_category = ""
        if hasattr(self, 'category_combo'):
            selected_category = self.category_combo.currentText()


        # 获取暂无权限复选框状态 - 新逻辑
        no_permission_checkbox_checked = False
        if hasattr(self, 'no_permission_checkbox'):
            no_permission_checkbox_checked = self.no_permission_checkbox.isChecked()


        # 如果选择"全部"分类且复选框勾选，显示所有行
        if (selected_category == "全部" or not selected_category) and no_permission_checkbox_checked:
            print("选择全部分类且复选框勾选，显示所有行")
            for row in range(self.table_widget.rowCount()):
                self.table_widget.setRowHidden(row, False)

            # 更新序号
            self.update_visible_sequence_numbers(self.table_widget)

            # 更新总计显示
            self.update_shop_count_display()

            # 恢复排序功能
            self.table_widget.setSortingEnabled(was_sorting_enabled)
            return

        # 查找邀约信息列的位置
        invitation_column = -1
        for col in range(self.table_widget.columnCount()):
            if self.table_widget.horizontalHeaderItem(col):
                header_text = self.table_widget.horizontalHeaderItem(col).text()
                if "邀约信息" in header_text:
                    invitation_column = col
                    print(f"找到邀约信息列，索引: {invitation_column}")
                    break

        # 如果找不到精确的"邀约信息"列，尝试查找包含"邀约"的列
        if invitation_column == -1:
            for col in range(self.table_widget.columnCount()):
                if self.table_widget.horizontalHeaderItem(col):
                    header_text = self.table_widget.horizontalHeaderItem(col).text()
                    if "邀约" in header_text:
                        invitation_column = col
                        print(f"找到替代邀约列: {header_text}, 索引: {invitation_column}")
                        break

        # 检查是否需要排除暂无权限店铺的逻辑
        should_exclude_no_permission = (not no_permission_checkbox_checked and
                                       selected_category != "暂无权限" and
                                       selected_category != "全部" and
                                       selected_category)

        if should_exclude_no_permission and invitation_column == -1:
            print("警告: 需要排除暂无权限店铺，但未找到邀约信息列")
            # 打印所有列名，帮助调试
            print("表格所有列名:")
            for col in range(self.table_widget.columnCount()):
                if self.table_widget.horizontalHeaderItem(col):
                    print(f"  列 {col}: {self.table_widget.horizontalHeaderItem(col).text()}")

        # 判断店铺介绍列的位置
        intro_column = -1
        for col in range(self.table_widget.columnCount()):
            if self.table_widget.horizontalHeaderItem(col) and \
               ("店铺介绍" in self.table_widget.horizontalHeaderItem(col).text() or \
                "介绍" in self.table_widget.horizontalHeaderItem(col).text()):
                intro_column = col
                print(f"找到店铺介绍列，索引: {intro_column}")
                break

        # 如果找不到店铺介绍列，尝试查找店铺分类列
        if intro_column == -1:
            for col in range(self.table_widget.columnCount()):
                if self.table_widget.horizontalHeaderItem(col) and \
                "店铺分类" in self.table_widget.horizontalHeaderItem(col).text():
                    intro_column = col
                    print(f"找到店铺分类列，索引: {intro_column}")
                    break

        if selected_category != "全部" and selected_category and intro_column == -1:
            print("警告: 启用了分类筛选，但未找到店铺介绍或分类列")

        # 获取搜索文本和搜索列
        filter_text = ""
        search_columns = []
        if hasattr(self, 'filter_combo') and self.filter_combo.lineEdit().text().strip():
            filter_text = self.filter_combo.lineEdit().text().strip()


            # 定义要搜索的字段名称
            search_fields = ["姓名", "店铺名称", "营业执照", "上家旺旺"]

            # 获取表头信息，找到对应字段的列索引
            for col in range(self.table_widget.columnCount()):
                header_item = self.table_widget.horizontalHeaderItem(col)
                if header_item and header_item.text() in search_fields:
                    search_columns.append(col)


        # 统计筛选结果
        total_rows = self.table_widget.rowCount()
        visible_rows = 0
        no_permission_rows = 0
        category_match_rows = 0
        combined_match_rows = 0

        # 遍历所有行，应用筛选条件
        for row in range(total_rows):
            # 默认显示所有行
            should_show = True
            is_no_permission = False
            is_category_match = True  # 默认为True，如果没有选择特定分类

            # 检查当前行的邀约信息状态
            is_no_permission = False
            if invitation_column != -1:
                invitation_item = self.table_widget.item(row, invitation_column)
                if invitation_item:
                    invitation_text = invitation_item.text()
                    is_no_permission = "暂无权限" in invitation_text

                else:
                    pass  # 邀约信息列为空

            # 优先应用搜索筛选（如果有搜索文本，则忽略其他筛选条件）
            if filter_text and search_columns:
                search_matched = False
                for col in search_columns:
                    item = self.table_widget.item(row, col)
                    if item and filter_text.lower() in item.text().lower():
                        search_matched = True

                        break

                # 如果有搜索文本，只根据搜索结果决定是否显示
                should_show = search_matched
                if not search_matched:
                    pass  # 不匹配搜索条件时不显示
            else:
                # 没有搜索文本时，应用分类筛选和权限筛选

                # 应用分类筛选（如果选择了特定分类且当前行应该显示）
                if should_show and selected_category != "全部" and selected_category:
                    # 特殊处理"暂无权限"分类选项
                    if selected_category == "暂无权限":
                        # 检查邀约信息列是否为"暂无权限"
                        is_category_match = is_no_permission
                        print(f"行 {row}: 选择暂无权限分类, 是否为暂无权限={is_category_match}")
                        if not is_category_match:
                            should_show = False
                    elif intro_column != -1:
                        # 处理其他分类选项
                        is_category_match = False  # 重置为False，需要匹配才为True
                        intro_item = self.table_widget.item(row, intro_column)
                        if intro_item:
                            intro_text = intro_item.text()
                            extracted_category = self.extract_category_from_intro(intro_text)

                            # 检查是否匹配选中的分类
                            if extracted_category and extracted_category == selected_category:
                                is_category_match = True
                            elif selected_category in intro_text:
                                is_category_match = True

                            print(f"行 {row}: 店铺介绍='{intro_text}', 提取分类='{extracted_category}', 是否匹配分类'{selected_category}'={is_category_match}")

                            # 如果不匹配分类，则隐藏
                            if not is_category_match:
                                should_show = False
                        else:
                            print(f"行 {row}: 店铺介绍列为空")
                            should_show = False  # 如果店铺介绍为空，不显示该行
                    else:
                        print(f"警告: 选择了分类'{selected_category}'但未找到店铺介绍或分类列")
                        should_show = False

                # 应用新的复选框逻辑：当复选框未勾选且选择的不是"暂无权限"分类时，排除暂无权限店铺
                if should_show and should_exclude_no_permission and is_no_permission:
                    print(f"行 {row}: 复选框未勾选，排除暂无权限店铺")
                    should_show = False

            # 设置行的显示/隐藏状态
            self.table_widget.setRowHidden(row, not should_show)

            # 统计结果
            if should_show:
                visible_rows += 1
            if is_no_permission:
                no_permission_rows += 1
            if is_category_match:
                category_match_rows += 1
            if is_no_permission and is_category_match:
                combined_match_rows += 1

        # 筛选结果统计（不输出调试信息）
        if selected_category != "全部" and selected_category:
            print(f"匹配分类'{selected_category}'的店铺数: {category_match_rows}")
        if should_exclude_no_permission and selected_category != "全部" and selected_category:
            print(f"同时满足分类和排除暂无权限条件的店铺数: {combined_match_rows}")



        # 更新序号
        self.update_visible_sequence_numbers(self.table_widget)

        # 更新总计显示
        self.update_shop_count_display()

        # 更新账号管理统计标签
        self.update_account_statistics_display()

        # 恢复排序功能
        self.table_widget.setSortingEnabled(was_sorting_enabled)

    def on_no_permission_checkbox_changed(self, state):
        """处理暂无权限复选框状态变化 - 新功能

        Args:
            state: 复选框状态 (Qt.Checked 或 Qt.Unchecked)

        功能说明:
        - 当复选框未勾选时：选择除"暂无权限"外的其他分类时，排除掉邀约信息为"暂无权限"的店铺
        - 当复选框勾选时：显示所有店铺（包括暂无权限的）
        - 当选择"暂无权限"分类时：不受复选框影响，正常显示暂无权限的店铺
        """
        if not hasattr(self, 'table_widget'):
            return

        is_checked = state == Qt.Checked
        print(f"\n===== 暂无权限复选框状态变化: {'选中' if is_checked else '未选中'} =====")
        print(f"新功能: 复选框{'勾选' if is_checked else '未勾选'}时，{'显示所有店铺' if is_checked else '在非暂无权限分类中排除暂无权限店铺'}")

        # 重新应用当前的分类筛选
        if hasattr(self, 'category_combo'):
            current_category = self.category_combo.currentText()
            print(f"当前分类: {current_category}")
            # 调用分类筛选方法，会自动应用新的复选框逻辑
            self.on_category_filter_changed(self.category_combo.currentIndex())

    def on_no_permission_filter_changed(self, state):
        """处理暂无权限复选框状态变化 - 旧方法（保留兼容性）

        Args:
            state: 复选框状态 (Qt.Checked 或 Qt.Unchecked)
        """
        # 这个方法保留用于向后兼容，但不再使用
        print("警告: 调用了旧的on_no_permission_filter_changed方法，请使用新的on_no_permission_checkbox_changed方法")

    def on_category_filter_changed(self, index):
        """处理店铺分类筛选改变"""
        if not hasattr(self, 'table_widget') or not hasattr(self, 'category_combo'):
            return

        selected_category = self.category_combo.currentText()
        print(f"\n===== 分类筛选变化: {selected_category} =====")

        # 检查表格中是否有"店铺介绍"或"店铺分类"列
        intro_column = -1
        for col in range(self.table_widget.columnCount()):
            if self.table_widget.horizontalHeaderItem(col):
                header_text = self.table_widget.horizontalHeaderItem(col).text()
                if "店铺介绍" in header_text or "介绍" in header_text:
                    intro_column = col
                    print(f"找到店铺介绍列，索引: {intro_column}")
                    break

        if intro_column == -1:
            for col in range(self.table_widget.columnCount()):
                if self.table_widget.horizontalHeaderItem(col) and "店铺分类" in self.table_widget.horizontalHeaderItem(col).text():
                    intro_column = col
                    print(f"找到店铺分类列，索引: {intro_column}")
                    break

        if intro_column == -1:
            print("警告: 未找到店铺介绍或分类列，请检查表格列名")

        # 应用组合筛选
        self.apply_combined_filters()

    def on_shop_filter_changed(self, text):
        """
        处理多字段筛选文本变化事件（实时筛选）

        功能:
        - 边输入边筛选结果，只显示匹配的行
        - 搜索字段：姓名、店铺名称、营业执照、上家旺旺
        - 文字删除后恢复所有行
        - 使用定时器避免频繁触发
        - 与分类筛选和权限筛选结合使用
        """
        # 使用定时器延迟筛选，避免频繁触发
        if hasattr(self, 'filter_timer'):
            self.filter_timer.stop()

        self.filter_timer = QTimer()
        self.filter_timer.setSingleShot(True)
        self.filter_timer.timeout.connect(lambda: self.apply_combined_filters())
        self.filter_timer.start(200)  # 200ms延迟

    def clear_search_filter(self):
        """清空搜索筛选"""
        if hasattr(self, 'filter_combo'):
            # 清空搜索框内容
            self.filter_combo.lineEdit().clear()
            print("已清空搜索筛选")

    def get_shop_data_from_row(self, row=None):
        """
        从表格中获取指定行或当前选中行的店铺数据

        参数:
            row (int, optional): 指定的行号，如果为None则使用当前选中行

        返回值:
            dict: 店铺数据字典，如果获取失败返回None
        """
        try:
            if row is None:
                # 获取当前选中行
                current_row = self.table_widget.currentRow()
                if current_row < 0:
                    print("❌ 没有选中任何行")
                    return None
                row = current_row

            if row < 0 or row >= self.table_widget.rowCount():
                print(f"❌ 行号 {row} 超出范围")
                return None

            # 获取表头信息
            headers = []
            for col in range(self.table_widget.columnCount()):
                header_item = self.table_widget.horizontalHeaderItem(col)
                if header_item:
                    headers.append(header_item.text())

            # 构建店铺数据字典
            shop_data = {}
            for col, header in enumerate(headers):
                if header == "序号":  # 跳过序号列
                    continue

                item = self.table_widget.item(row, col)
                if item:
                    shop_data[header] = item.text()
                else:
                    shop_data[header] = ""

            # 验证必要字段
            required_fields = ['店铺名称']
            for field in required_fields:
                if field not in shop_data or not shop_data[field]:
                    print(f"❌ 缺少必要字段: {field}")
                    return None

            # 如果没有店铺ID，尝试通过店铺名称获取
            if '店铺ID' not in shop_data or not shop_data['店铺ID']:
                shop_name = shop_data.get('店铺名称', '')
                shop_id = self.get_shop_id_by_name_from_cache(shop_name)
                if shop_id:
                    shop_data['店铺ID'] = shop_id
                    print(f"✅ 通过店铺名称 '{shop_name}' 获取到店铺ID: '{shop_id}'")
                else:
                    print(f"⚠️ 无法获取店铺 '{shop_name}' 的店铺ID")

            print(f"✅ 成功获取店铺数据: {shop_data.get('店铺名称', '未知')}")
            return shop_data

        except Exception as e:
            print(f"❌ 获取店铺数据时出错: {str(e)}")
            return None

    def get_shop_id_by_name_from_cache(self, shop_name):
        """
        从缓存的账号管理文件中通过店铺名称获取店铺ID

        参数:
            shop_name (str): 店铺名称

        返回值:
            str: 店铺ID，如果没找到返回None
        """
        try:
            import json
            import os

            # 从账号管理文件中查找
            account_file = get_config_path('账号管理.json')
            if os.path.exists(account_file):
                with open(account_file, 'r', encoding='utf-8') as f:
                    accounts_data = json.load(f)

                # 处理不同的数据结构
                accounts = []
                if isinstance(accounts_data, list):
                    accounts = accounts_data
                elif isinstance(accounts_data, dict):
                    if 'data' in accounts_data and isinstance(accounts_data['data'], list):
                        accounts = accounts_data['data']
                    else:
                        accounts = list(accounts_data.values()) if accounts_data else []

                # 查找匹配的店铺
                for account in accounts:
                    if isinstance(account, dict):
                        shop_name_value = account.get('店铺名称', '') or ''
                        shop_id_value = account.get('店铺ID', '') or ''
                        account_shop_name = shop_name_value.strip() if isinstance(shop_name_value, str) else ''
                        account_shop_id = shop_id_value.strip() if isinstance(shop_id_value, str) else ''

                        if account_shop_name == shop_name.strip() and account_shop_id:
                            return account_shop_id

            return None

        except Exception as e:
            print(f"❌ 从缓存获取店铺ID时出错: {str(e)}")
            return None

    def sync_browser_config(self):
        """
        同步浏览器配置功能

        功能：
        - 获取账号管理表格中选中行的店铺数据作为源店铺
        - 通过店铺名称获取对应的店铺ID
        - 同步该店铺的浏览器配置到其他店铺
        - 同步项目：Extensions（插件配置）、Bookmarks（收藏夹配置）、
          Preferences（浏览器首选项配置）、Secure Preferences（安全首选项配置）
        - 不同步个人化数据：Cookie、历史记录、登录数据等
        - 提供操作进度反馈
        """
        try:
            import os
            import json
            from PyQt5.QtWidgets import QMessageBox

            # 获取选中行的店铺数据
            source_shop_data = self.get_shop_data_from_row()
            if not source_shop_data:
                QMessageBox.warning(self, "同步失败", "请先选择一个店铺作为源店铺")
                return

            source_shop_name = source_shop_data.get('店铺名称', '未知店铺')
            source_shop_id = source_shop_data.get('店铺ID', '')

            if not source_shop_id:
                QMessageBox.warning(self, "同步失败", f"无法获取店铺 '{source_shop_name}' 的店铺ID")
                return

            print(f"🔍 选择源店铺: '{source_shop_name}' (ID: {source_shop_id})")

            # 检查源店铺配置目录
            config_base_dir = os.path.join(os.getcwd(), "config", "kslogoin")
            source_dir = os.path.join(config_base_dir, f"shop_{source_shop_id}")

            if not os.path.exists(source_dir):
                QMessageBox.warning(self, "同步失败", f"源店铺 '{source_shop_name}' 的配置目录不存在：\n{source_dir}")
                return

            # 获取所有其他店铺数据（从账号管理文件中）
            target_shops = []
            try:
                # 从账号管理文件中获取所有店铺数据
                account_file = get_config_path('账号管理.json')
                if os.path.exists(account_file):
                    with open(account_file, 'r', encoding='utf-8') as f:
                        accounts_data = json.load(f)

                    # 处理不同的数据结构
                    accounts = []
                    if isinstance(accounts_data, list):
                        accounts = accounts_data
                    elif isinstance(accounts_data, dict):
                        if 'data' in accounts_data and isinstance(accounts_data['data'], list):
                            accounts = accounts_data['data']
                        else:
                            accounts = list(accounts_data.values()) if accounts_data else []

                    # 获取所有其他店铺（排除源店铺）
                    for account in accounts:
                        if isinstance(account, dict):
                            shop_name_value = account.get('店铺名称', '') or ''
                            shop_id_value = account.get('店铺ID', '') or ''
                            account_shop_name = shop_name_value.strip() if isinstance(shop_name_value, str) else ''
                            account_shop_id = shop_id_value.strip() if isinstance(shop_id_value, str) else ''

                            # 排除源店铺
                            if account_shop_id and account_shop_id != source_shop_id:
                                target_shops.append({
                                    '店铺名称': account_shop_name,
                                    '店铺ID': account_shop_id
                                })

                if not target_shops:
                    QMessageBox.information(self, "提示", "没有找到其他店铺")
                    return

                print(f"🎯 找到 {len(target_shops)} 个目标店铺")

            except Exception as e:
                QMessageBox.warning(self, "错误", f"读取账号管理文件时出错：\n{str(e)}")
                return

            # 确认同步操作 - 只同步指定的四个配置项
            sync_items_to_check = {
                "Extensions": {"type": "directory", "path": "Default/Extensions", "name": "插件配置", "required": True},
                "Bookmarks": {"type": "file", "path": "Default/Bookmarks", "name": "收藏夹配置", "required": True},
                "Preferences": {"type": "file", "path": "Default/Preferences", "name": "浏览器首选项配置", "required": True},
                "Secure Preferences": {"type": "file", "path": "Default/Secure Preferences", "name": "安全首选项配置", "required": True}
            }

            sync_description = []
            source_config_dir = os.path.join(os.getcwd(), "config", "kslogoin", f"shop_{source_shop_id}")
            for item_key, item_info in sync_items_to_check.items():
                item_path = os.path.join(source_config_dir, item_info["path"])
                if os.path.exists(item_path):
                    sync_description.append(f"• {item_info['name']}（{item_info['path']}）")

            if not sync_description:
                QMessageBox.warning(self, "同步失败", f"源店铺 '{source_shop_name}' 没有可同步的配置项")
                return

            reply = QMessageBox.question(
                self,
                "确认同步",
                f"将要把店铺 '{source_shop_name}' (ID: {source_shop_id}) 的浏览器配置同步到 {len(target_shops)} 个其他店铺：\n\n"
                + "\n".join(sync_description) +
                f"\n\n共 {len(sync_description)} 个配置项\n"
                f"注意：不会同步Cookie、历史记录等个人化数据\n\n"
                f"是否继续？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 启动同步线程
            self.sync_thread = BrowserConfigSyncThread(source_shop_data, target_shops, self)

            # 连接信号
            self.sync_thread.progress_updated.connect(self.on_sync_progress_updated)
            self.sync_thread.sync_completed.connect(self.on_sync_completed)

            # 启动线程
            self.sync_thread.start()

        except Exception as e:
            print(f"同步浏览器配置时出错: {str(e)}")
            QMessageBox.critical(self, "同步错误", f"同步过程中发生错误：\n{str(e)}")

    def on_sync_progress_updated(self, status_message):
        """处理同步进度更新"""
        try:
            # 使用状态管理器显示进度
            if hasattr(self, 'status_manager'):
                self.status_manager.show_update_status(status_message)
            else:
                # 如果没有状态管理器，直接设置标题
                self.set_window_title_status(f"🔄 {status_message}")
        except Exception as e:
            print(f"更新同步进度时出错: {str(e)}")

    def on_sync_completed(self, success, success_count, total_count, error_shops):
        """处理同步完成"""
        try:
            if success:
                completion_message = f"同步完成！成功同步 {success_count}/{total_count} 个店铺"
                if error_shops:
                    completion_message += f"，{len(error_shops)} 个失败"

                # 在标题栏显示完成状态
                if hasattr(self, 'status_manager'):
                    self.status_manager.show_update_complete_status(completion_message)
                else:
                    self.set_window_title_status(f"✅ {completion_message}")

                # 显示详细结果
                result_msg = f"同步完成！\n\n成功同步 {success_count}/{total_count} 个店铺"
                if error_shops:
                    result_msg += f"\n\n失败的店铺：\n" + "\n".join(error_shops)
                QMessageBox.information(self, "同步完成", result_msg)
            else:
                error_message = "没有成功同步任何店铺配置"

                # 在标题栏显示错误状态
                if hasattr(self, 'status_manager'):
                    self.status_manager.show_error_status(error_message)
                else:
                    self.set_window_title_status(f"❌ {error_message}")

                # 显示详细错误信息
                if error_shops:
                    error_msg = f"同步失败！\n\n错误信息：\n" + "\n".join(error_shops)
                else:
                    error_msg = error_message
                QMessageBox.warning(self, "同步失败", error_msg)

        except Exception as e:
            print(f"处理同步完成时出错: {str(e)}")
        finally:
            # 清理线程引用
            if hasattr(self, 'sync_thread'):
                self.sync_thread = None

    def update_one_click_order_shop_combo(self):
        """更新一键下单页面中的店铺下拉框"""
        try:
            # 查找所有顶级窗口
            for widget in QApplication.topLevelWidgets():
                # 查找一键下单窗口实例
                if widget.__class__.__name__ == "OneClickOrderWindow" and hasattr(widget, 'profit_combo'):
                    # 调用一键下单窗口的加载店铺名称方法
                    # 使用静默模式，不显示任何提示
                    widget.load_shop_names_to_combo(silence=True)
                    print("已自动更新一键下单窗口的店铺下拉框")
        except Exception as e:
            print(f"更新一键下单店铺下拉框时出错: {str(e)}")
            # 这里不需要弹出错误提示，因为这是自动操作

    def display_data(self, records):
        """显示数据到表格中"""
        if records and isinstance(records, list) and isinstance(records[0], dict):
            # 暂时禁用排序功能，防止在加载数据时触发排序
            was_sorting_enabled = self.table_widget.isSortingEnabled()
            self.table_widget.setSortingEnabled(False)

            # 从第一条记录获取字段名
            fields = list(records[0].keys())

            # 保存所有字段
            self.all_fields = fields.copy()

            # 如果有ID字段，将其移至最后
            if 'ID' in fields:
                fields.remove('ID')

            # 如果有字段设置，则按照设置的顺序排列并过滤字段
            if self.field_settings:
                # 获取要显示的字段（按设置顺序）
                visible_fields = [field for field in self.field_settings if field in fields]
                # 更新字段列表
                fields = visible_fields

            # 设置列数为字段数+1(包括序号列)
            self.table_widget.setColumnCount(len(fields) + 1)

            # 创建表头标签，添加序号列
            headers = ["序号"] + fields
            self.table_widget.setHorizontalHeaderLabels(headers)

            # 清空现有数据
            self.table_widget.setRowCount(0)

            # 创建并设置复选框代理
            if not hasattr(self, 'checkbox_delegate'):
                self.checkbox_delegate = CheckBoxDelegate(self.table_widget)
                self.table_widget.setItemDelegateForColumn(0, self.checkbox_delegate)
                # 连接复选框状态变化信号
                self.checkbox_delegate.checkStateChanged.connect(self.on_checkbox_state_changed)

            # 填充表格数据
            self.table_widget.setRowCount(len(records))
            for row_idx, row_data in enumerate(records):
                # 添加序号列（使用自定义的带复选框的表格项）
                seq_item = CheckBoxNumericTableWidgetItem(row_idx + 1)
                self.table_widget.setItem(row_idx, 0, seq_item)

                # 添加数据列
                for col_idx, field_name in enumerate(fields):
                    value = row_data.get(field_name, "")
                    
                    # 特殊处理在售列：添加上家统计信息
                    if field_name == "在售" and "上家旺旺" in row_data:
                        supplier_data = row_data.get("上家旺旺", "")
                        if supplier_data and isinstance(supplier_data, str):
                            # 按逗号分割上家旺旺数据
                            suppliers = [s.strip() for s in supplier_data.replace('，', ',').split(',') if s.strip()]
                            supplier_count = len(suppliers)
                            # 在原有在售数据后添加上家统计信息，格式：[上家数量]在售数量
                            if supplier_count > 0:
                                value = f"[{supplier_count}]{value}"
                    
                    # 根据列名决定使用哪种 TableWidgetItem
                    if field_name in self.numeric_sort_columns:
                        item = NumericTableWidgetItem(value) # 使用原始值value创建
                    else:
                        item = QTableWidgetItem(str(value))

                    # 特殊处理保证金列：如果分数格式前面数值小于后面数值，设置红色字体
                    if field_name == "保证金" and isinstance(value, str) and '/' in value:
                        try:
                            # 提取斜杠前后的数字
                            parts = value.split('/')
                            if len(parts) >= 2 and parts[0].strip() and parts[1].strip():
                                front_value = float(parts[0].strip())  # 斜杠前面的数值
                                back_value = float(parts[1].strip())   # 斜杠后面的数值
                                if front_value < back_value:
                                    # 设置字体颜色为红色
                                    item.setForeground(QColor(255, 0, 0))  # 红色
                        except (ValueError, TypeError):
                            pass  # 如果转换失败，保持默认颜色

                    # 设置工具提示，鼠标悬停时显示完整内容
                    item.setToolTip(str(value))
                    self.table_widget.setItem(row_idx, col_idx + 1, item)

            # 设置列宽和文本溢出行为
            # 序号列设为50像素且固定
            self.table_widget.setColumnWidth(0, 50)
            self.table_widget.horizontalHeader().setSectionResizeMode(0, QHeaderView.Fixed)

            # 其他列设为初始宽度90像素，但允许用户调整
            for col in range(1, len(headers)):
                self.table_widget.setColumnWidth(col, 90)
                self.table_widget.horizontalHeader().setSectionResizeMode(col, QHeaderView.Interactive)

            # 设置表头可见并启用拖动调整列宽
            self.table_widget.horizontalHeader().setVisible(True)
            self.table_widget.horizontalHeader().setStretchLastSection(False)

            # 隐藏行号列
            self.table_widget.verticalHeader().setVisible(False)

            # 恢复排序功能
            self.table_widget.setSortingEnabled(was_sorting_enabled)

            # 如果保持了分类筛选，重新应用筛选
            if hasattr(self, 'category_combo') and self.category_combo.currentText() != "全部":
                self.on_category_filter_changed(self.category_combo.currentIndex())

            # 应用表格样式
            self.apply_table_style()

            # 提取店铺分类数据并更新分类树和下拉菜单
            self.extract_and_update_categories(records, fields)
            self.update_category_filter_combo()

            # 更新总计显示
            self.update_shop_count_display()

            # 更新账号管理统计标签
            self.update_account_statistics_display()



    def on_checkbox_state_changed(self, row, checked):
        """处理复选框状态变化事件"""
        # 更新总计显示
        self.update_shop_count_display()

        # 打印调试信息
        checked_count = len(self.checkbox_delegate.get_checked_rows()) if hasattr(self, 'checkbox_delegate') else 0
        print(f"复选框状态变化: 行 {row} {'选中' if checked else '取消选中'}, 当前已选中 {checked_count} 行")

    def apply_table_style(self):
        """为表格应用磨砂效果样式"""
        self.table_widget.setStyleSheet("""
            QTableWidget {
                gridline-color: #E0E0E0; /* 网格线颜色 - 浅灰色 */
            }
            QTableWidget::item {
                background-color: #FFFFFF; /* 普通行白色 */
                padding: 6px; /* 保留一些基本内边距 */
                border: none; /* 移除项目边框，依赖表格的 gridline-color */
            }
            QTableWidget::item:alternate {
                background-color: #F5F5F5; /* 交替行浅灰色 */
            }
            QTableWidget::item:selected {
                background-color: #F0F4FF;
                color: #2563EB;
                border: none;
                font-weight: normal;
            }
            QTableWidget::item:focus {
                background-color: #E6EFFF;
                color: #1D4ED8;
                border: none;
                outline: none;
            }

            /* 垂直滚动条样式 */
            QScrollBar:vertical {
                border: none;
                background: #F0F0F0; /* 滚动条背景色 */
                width: 10px; /* 滚动条宽度 */
                margin: 0px 0px 0px 0px;
            }
            QScrollBar::handle:vertical {
                background: #C0C0C0; /* 滑块颜色 */
                min-height: 20px; /* 滑块最小高度 */
                border-radius: 5px; /* 滑块圆角 */
            }
            QScrollBar::handle:vertical:hover {
                background: #A0A0A0; /* 鼠标悬停滑块颜色 */
            }
            QScrollBar::add-line:vertical,
            QScrollBar::sub-line:vertical {
                border: none;
                background: none;
                height: 0px; /* 隐藏上下箭头 */
            }
            QScrollBar::up-arrow:vertical,
            QScrollBar::down-arrow:vertical {
                background: none;
            }
            QScrollBar::add-page:vertical,
            QScrollBar::sub-page:vertical {
                background: none;
            }

            /* 水平滚动条样式 */
            QScrollBar:horizontal {
                border: none;
                background: #F0F0F0; /* 滚动条背景色 */
                height: 10px; /* 滚动条高度 */
                margin: 0px 0px 0px 0px;
            }
            QScrollBar::handle:horizontal {
                background: #C0C0C0; /* 滑块颜色 */
                min-width: 20px; /* 滑块最小宽度 */
                border-radius: 5px; /* 滑块圆角 */
            }
            QScrollBar::handle:horizontal:hover {
                background: #A0A0A0; /* 鼠标悬停滑块颜色 */
            }
            QScrollBar::add-line:horizontal,
            QScrollBar::sub-line:horizontal {
                border: none;
                background: none;
                width: 0px; /* 隐藏左右箭头 */
            }
            QScrollBar::left-arrow:horizontal,
            QScrollBar::right-arrow:horizontal {
                background: none;
            }
            QScrollBar::add-page:horizontal,
            QScrollBar::sub-page:horizontal {
                background: none;
            }
        """)

    def extract_and_update_categories(self, records, fields):
        """从账号数据中提取店铺分类并更新分类树"""
        # 尝试从records中直接提取数据
        categories = {}  # 存储分类及其计数
        category_stores = {}  # 存储每个分类下的店铺名称列表

        print(f"开始提取店铺分类信息，共 {len(records)} 条记录")
        print(f"数据字段: {fields}")

        # 确定店铺分类和店铺名称的字段名
        # 支持多种可能的字段名
        category_field_names = ["店铺分类", "分类", "类别", "店铺类别", "类型", "店铺类型", "介绍", "店铺介绍"]
        store_name_field_names = ["店铺名称", "店铺", "名称", "商店名称", "商户名称", "店铺"]

        category_field = None
        store_name_field = None

        # 查找店铺分类字段
        for field_name in category_field_names:
            if field_name in fields:
                category_field = field_name
                print(f"找到店铺分类字段: {category_field}")
                break

        # 查找店铺名称字段
        for field_name in store_name_field_names:
            if field_name in fields:
                store_name_field = field_name
                print(f"找到店铺名称字段: {store_name_field}")
                break

        # 如果找不到店铺分类字段，尝试其他方法
        if not category_field:
            print("警告: 未找到标准的店铺分类字段，尝试查找包含'分类'或'介绍'的字段")
            for field in fields:
                if "分类" in field or "类别" in field or "类型" in field or "介绍" in field:
                    category_field = field
                    print(f"使用替代分类字段: {category_field}")
                    break

        # 如果找不到店铺名称字段，尝试其他方法
        if not store_name_field:
            print("警告: 未找到标准的店铺名称字段，尝试查找包含'店铺'或'名称'的字段")
            for field in fields:
                if "店铺" in field or "名称" in field or "商店" in field:
                    store_name_field = field
                    print(f"使用替代店铺名称字段: {store_name_field}")
                    break

        # 如果仍然没有找到，使用默认的第一个字段作为店铺名称
        if not store_name_field and len(fields) > 0:
            store_name_field = fields[0]
            print(f"未找到店铺名称字段，使用第一个字段代替: {store_name_field}")

        # 处理找不到分类字段的情况
        if not category_field:
            print("错误: 无法找到店铺分类字段，无法更新分类树")
            # 尝试使用备选方案，使用第一个字段作为分类
            if len(fields) > 0:
                category_field = fields[0]
                print(f"尝试使用第一个字段作为分类字段: {category_field}")
            else:
                return  # 如果没有任何字段可用作分类字段，则返回

        # 从records中提取分类数据
        for record in records:
            # 获取分类字段的值，如果没有就使用空字符串，确保不是None
            category_value = record.get(category_field, "") or ""
            category_name = category_value.strip() if isinstance(category_value, str) else ""

            # 获取店铺名称字段的值，确保不是None
            store_value = record.get(store_name_field, "") or "" if store_name_field else "未知店铺"
            store_name = store_value.strip() if isinstance(store_value, str) else "未知店铺"

            # 特殊处理：如果分类字段是"店铺介绍"，尝试从中提取有用的分类信息
            if category_field == "店铺介绍" or category_field == "介绍":
                # 提取介绍中的关键词作为分类
                extracted_category = self.extract_category_from_intro(category_name)
                if extracted_category:
                    category_name = extracted_category
                    print(f"从介绍中提取分类: '{category_name}' (原介绍: '{category_name[:30]}...')")

            # 如果分类为空但有店铺名称，使用"未分类"作为分类
            if not category_name and store_name:
                category_name = "未分类"
                print(f"店铺 '{store_name}' 没有分类信息，归入'未分类'")

            if category_name:  # 只处理有分类的记录
                # 统计每个分类的数量
                if category_name in categories:
                    categories[category_name] += 1
                else:
                    categories[category_name] = 1
                    category_stores[category_name] = []

                # 将店铺名称添加到对应分类的列表中（去重）
                if store_name and store_name not in category_stores.get(category_name, []):
                    category_stores[category_name].append(store_name)

        # 如果没有任何分类，创建一个默认分类
        if not categories:
            print("警告: 未能提取到任何分类信息，创建默认分类")
            categories["所有店铺"] = len(records)
            category_stores["所有店铺"] = []
            for record in records:
                store_value = record.get(store_name_field, "") or ""
                if store_value and isinstance(store_value, str):
                    store_name_clean = store_value.strip()
                    if store_name_clean:
                        category_stores["所有店铺"].append(store_name_clean)

        # 如果当前在商品管理页面，更新分类树
        if hasattr(self, 'product_page') and self.stacked_widget.currentWidget() == self.product_page:
            if hasattr(self, 'product_manager') and hasattr(self.product_manager, 'category_tree'):
                self.product_manager.update_category_tree(categories, category_stores)

        # 存储分类数据和店铺数据以便在切换到商品管理页面时使用
        self.store_categories = categories
        self.category_stores = category_stores

        # 打印分类信息用于调试
        print(f"成功提取 {len(categories)} 个店铺分类:")
        for cat, count in categories.items():
            print(f"  - {cat}: {count} 个店铺")
            if cat in category_stores:
                store_examples = category_stores[cat][:5]
                if store_examples:
                    print(f"    店铺示例: {', '.join(store_examples)}{' 等' if len(category_stores[cat]) > 5 else ''}")

    def extract_category_from_intro(self, intro_text):
        """从店铺介绍文本中汇总实际分类信息

        参数:
        -----
        intro_text : str
            店铺介绍文本

        返回:
        -----
        str 或 None
            原始的介绍文本作为分类，如果为空则返回None
        """
        if not intro_text:
            return None

        # 只做基本的文本清理，保持原始内容
        intro_text = intro_text.strip()

        # 如果文本为空，返回None
        if not intro_text:
            return None

        # 直接返回原始介绍文本作为分类
        return intro_text

    def show_table_context_menu(self, position):
        """显示表格右键菜单"""
        # 根据右键点击位置设置当前行
        item = self.table_widget.itemAt(position)
        if item:
            # 设置当前行为右键点击的行
            self.table_widget.setCurrentItem(item)

        # 创建账号管理右键菜单
        context_menu = QMenu(self)

        # 设置现代化菜单样式 - 圆角透明
        context_menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 8px;
                padding: 4px 0px;
                font-family: 'Microsoft YaHei UI', 'Microsoft YaHei';
                font-size: 13px;
                font-weight: normal;
                min-width: 160px;
            }
            QMenu::item {
                background-color: transparent;
                color: #374151;
                padding: 8px 16px;
                margin: 1px 4px;
                border-radius: 4px;
                min-width: 140px;
                border: none;
            }
            QMenu::item:selected {
                background-color: #F3F4F6;
                color: #1F2937;
            }
            QMenu::item:pressed {
                background-color: #E5E7EB;
                color: #111827;
            }
            QMenu::separator {
                height: 1px;
                background-color: #E5E7EB;
                margin: 3px 8px;
                border: none;
            }
        """)

        # 添加账号管理表格右键菜单项
        if hasattr(self, 'checkbox_delegate'):
            # 使用复选框的全选和取消全选
            select_all_action = QAction("✅ 全选", self)
            select_all_action.triggered.connect(self.select_all_checkboxes)

            deselect_all_action = QAction("❌ 取消全选", self)
            deselect_all_action.triggered.connect(self.deselect_all_checkboxes)
        else:
            # 使用表格行的全选和取消全选
            select_all_action = QAction("✅ 全选", self)
            select_all_action.triggered.connect(self.select_all_rows)

            deselect_all_action = QAction("❌ 取消全选", self)
            deselect_all_action.triggered.connect(self.deselect_all_rows)

        # 添加账号管理查看订单菜单项
        view_orders_action = QAction("📋 查看订单", self)
        view_orders_action.triggered.connect(self.view_shop_orders)

        copy_store_name_action = QAction("📋 复制店铺名称", self)
        copy_store_name_action.triggered.connect(self.copy_store_name)

        # 添加浏览商品菜单项
        browse_products_action = QAction("🛍️ 浏览商品", self)
        browse_products_action.triggered.connect(self.browse_shop_products)

        add_store_action = QAction("➕ 新增店铺", self)
        add_store_action.triggered.connect(self.add_store)

        edit_store_action = QAction("✏️ 修改店铺信息", self)
        edit_store_action.triggered.connect(self.edit_store)

        batch_edit_action = QAction("📝 批量修改邀约信息", self)
        batch_edit_action.triggered.connect(self.batch_edit_invitation)

        login_store_action = QAction("🔑 店铺登录", self)
        login_store_action.triggered.connect(self.login_store)

        # 将菜单项添加到菜单
        context_menu.addAction(select_all_action)
        context_menu.addAction(deselect_all_action)

        # 添加查看厂家菜单项
        view_suppliers_action = QAction("🏭 查看厂家", self)
        view_suppliers_action.triggered.connect(self.view_suppliers)

        context_menu.addSeparator()
        context_menu.addAction(view_orders_action)  # 添加查看订单菜单项
        context_menu.addAction(copy_store_name_action)
        context_menu.addAction(browse_products_action)  # 添加浏览商品菜单项
        context_menu.addAction(view_suppliers_action)  # 添加查看厂家菜单项
        context_menu.addSeparator()
        context_menu.addAction(add_store_action)
        context_menu.addAction(edit_store_action)
        context_menu.addAction(batch_edit_action)
        context_menu.addAction(login_store_action)

        # 设置菜单属性以实现透明圆角
        context_menu.setAttribute(Qt.WA_TranslucentBackground, True)
        context_menu.setWindowFlags(Qt.Popup | Qt.FramelessWindowHint | Qt.NoDropShadowWindowHint)

        # 显示右键菜单
        cursor_pos = QCursor.pos()
        context_menu.exec_(cursor_pos)

    def on_table_double_clicked(self, item):
        """
        处理账号管理表格双击事件 - 优化版本，避免UI阻塞

        功能：
        - 双击表格行时，在后台线程中启动店铺后台登录
        - 避免主UI线程阻塞，防止窗口显示"未响应"
        - 使用无痕模式浏览器确保各店铺数据独立
        - 提供登录状态反馈

        参数:
            item: 被双击的表格项
        """
        try:
            if not item:
                return

            row = item.row()
            print(f"双击了第 {row + 1} 行，正在准备进入店铺后台...")

            # 获取当前行的店铺数据
            shop_data = self.get_shop_data_from_row(row)
            if not shop_data:
                QMessageBox.warning(self, "错误", "无法获取店铺数据")
                return

            shop_name = shop_data.get('店铺名称', '未知店铺')
            print(f"正在为店铺 '{shop_name}' 启动后台线程...")

            # 更新窗口标题显示状态
            original_title = self.windowTitle()
            self.setWindowTitle(f"{original_title} - 正在启动 {shop_name} 后台...")

            # 导入后台登录线程
            from 店铺后台登录 import BackgroundLoginThread, ShopBackendLoginManager

            # 创建登录管理器（如果还没有的话）
            if not hasattr(self, 'login_manager'):
                self.login_manager = ShopBackendLoginManager(self)

            # 创建后台登录线程，避免UI阻塞
            self.background_login_thread = BackgroundLoginThread(shop_data, self.login_manager)

            # 连接后台线程的信号
            def on_login_completed(success, message):
                """登录完成处理"""
                try:
                    # 恢复窗口标题
                    self.setWindowTitle(original_title)

                    if success:
                        print(f"✅ 店铺 '{shop_name}' 后台启动成功")
                        # 可以在这里添加成功提示，但通常不需要弹窗
                        # self.show_temporary_status(f"店铺 '{shop_name}' 后台已成功打开")
                    else:
                        print(f"❌ 店铺 '{shop_name}' 后台启动失败: {message}")
                        # 只有在失败时才显示提示
                        QMessageBox.warning(self, "登录失败", f"店铺 '{shop_name}' 后台启动失败：\n{message}")

                    # 清理线程引用
                    if hasattr(self, 'background_login_thread'):
                        del self.background_login_thread

                except Exception as e:
                    print(f"处理登录完成事件时出错: {str(e)}")

            def on_status_update(status):
                """状态更新处理"""
                try:
                    # 更新窗口标题显示当前状态
                    self.setWindowTitle(f"{original_title} - {status}")
                    print(f"状态更新: {status}")
                except Exception as e:
                    print(f"处理状态更新时出错: {str(e)}")

            # 连接信号
            self.background_login_thread.login_completed.connect(on_login_completed)
            self.background_login_thread.status_update.connect(on_status_update)

            # 启动后台线程
            self.background_login_thread.start()
            print(f"✅ 店铺 '{shop_name}' 的后台登录线程已启动，主窗口保持响应")

        except Exception as e:
            error_msg = f"处理双击事件时出错: {str(e)}"
            print(f"❌ {error_msg}")

            # 记录详细的异常信息用于调试
            import traceback
            print(f"详细异常信息:")
            traceback.print_exc()

            # 安全地显示错误消息，避免二次异常
            try:
                QMessageBox.warning(self, "错误", error_msg)
            except Exception as msg_error:
                print(f"显示错误消息时出错: {msg_error}")

            # 安全地恢复窗口标题
            try:
                current_title = self.windowTitle()
                if " - " in current_title:
                    original_title = current_title.split(" - ")[0]
                    self.setWindowTitle(original_title)
                else:
                    # 如果标题格式不符合预期，使用默认标题
                    self.setWindowTitle("快手小店管理系统")
            except Exception as title_error:
                print(f"恢复窗口标题时出错: {title_error}")
                # 最后的保险措施
                try:
                    self.setWindowTitle("快手小店管理系统")
                except:
                    pass

            # 清理可能的线程引用，防止资源泄露
            try:
                if hasattr(self, 'background_login_thread'):
                    if self.background_login_thread.isRunning():
                        self.background_login_thread.quit()
                        self.background_login_thread.wait(1000)  # 等待1秒
                    del self.background_login_thread
            except Exception as cleanup_error:
                print(f"清理线程时出错: {cleanup_error}")



    def select_all_checkboxes(self):
        """全选所有复选框"""
        if hasattr(self, 'checkbox_delegate') and hasattr(self, 'table_widget'):
            # 获取所有可见行
            visible_rows = []
            for row in range(self.table_widget.rowCount()):
                if not self.table_widget.isRowHidden(row):
                    visible_rows.append(row)

            # 设置所有可见行的复选框为选中状态
            self.checkbox_delegate.set_checked_rows(visible_rows)

            # 更新表格显示
            self.table_widget.update()

            # 更新总计显示
            self.update_shop_count_display()

            print(f"已全选 {len(visible_rows)} 个店铺")

    def deselect_all_checkboxes(self):
        """取消全选所有复选框"""
        if hasattr(self, 'checkbox_delegate'):
            # 清除所有选中的行
            self.checkbox_delegate.clear_checked_rows()

            # 更新表格显示
            if hasattr(self, 'table_widget'):
                self.table_widget.update()

            # 更新总计显示
            self.update_shop_count_display()

            print("已取消全选店铺")

    def select_all_rows(self):
        """全选表格所有行"""
        self.table_widget.selectAll()

    def deselect_all_rows(self):
        """取消全选"""
        self.table_widget.clearSelection()
     #右键菜单查看订单
    def view_shop_orders(self):
        """查看选中店铺的订单

        功能:
        - 获取当前选中行的店铺名称
        - 跳转到一键下单页面
        - 强制选中"全部订单"选项
        - 设置日期范围为最近6个月
        - 将店铺名称填入搜索框
        - 设置关键字为"店铺名称"
        - 自动触发搜索
        """
        current_row = self.table_widget.currentRow()
        if current_row >= 0:
            # 获取表头信息
            headers = []
            for col in range(self.table_widget.columnCount()):
                header_item = self.table_widget.horizontalHeaderItem(col)
                if header_item:
                    headers.append(header_item.text())

            # 查找店铺名称列
            store_name_col = -1
            for i, header in enumerate(headers):
                if header == "店铺名称":
                    store_name_col = i
                    break

            if store_name_col >= 0:
                store_name_item = self.table_widget.item(current_row, store_name_col)
                if store_name_item and store_name_item.text():
                    store_name = store_name_item.text().strip()

                    # 跳转到一键下单页面
                    self.show_one_click_order_page()

                    # 设置搜索条件
                    if hasattr(self, 'one_click_order_widget'):
                        from PyQt5.QtCore import QDateTime, QTime, QTimer

                        # 强制选中"全部订单"选项
                        if hasattr(self.one_click_order_widget, 'menu_buttons'):
                            menu_buttons = self.one_click_order_widget.menu_buttons
                            print("开始强制选中'全部订单'选项")

                            # 取消所有按钮的选中状态
                            for text, button in menu_buttons.items():
                                button.setChecked(False)

                            # 选中"全部订单"按钮
                            if "全部订单" in menu_buttons:
                                all_orders_button = menu_buttons["全部订单"]
                                all_orders_button.setChecked(True)
                                # 更新当前菜单状态
                                self.one_click_order_widget.current_menu = "全部订单"
                                print("已强制选中'全部订单'选项")
                            else:
                                print("警告：未找到'全部订单'按钮")

                        # 设置日期范围为最近6个月
                        if hasattr(self.one_click_order_widget, 'start_date') and hasattr(self.one_click_order_widget, 'end_date'):
                            # 计算6个月前的日期
                            current_date = QDateTime.currentDateTime()
                            six_months_ago = current_date.addMonths(-6)

                            # 设置开始日期为6个月前的0点0分0秒
                            start_datetime = QDateTime(six_months_ago.date(), QTime(0, 0, 0))
                            self.one_click_order_widget.start_date.setDateTime(start_datetime)

                            # 设置结束日期为当前日期的23点59分59秒
                            end_datetime = QDateTime(current_date.date(), QTime(23, 59, 59))
                            self.one_click_order_widget.end_date.setDateTime(end_datetime)

                            print(f"设置日期范围: {start_datetime.toString('yyyy-MM-dd HH:mm:ss')} 至 {end_datetime.toString('yyyy-MM-dd HH:mm:ss')}")

                        # 设置关键字下拉框为"店铺名称"
                        if hasattr(self.one_click_order_widget, 'keyword_combo'):
                            keyword_combo = self.one_click_order_widget.keyword_combo
                            for i in range(keyword_combo.count()):
                                if keyword_combo.itemText(i) == "店铺名称":
                                    keyword_combo.setCurrentIndex(i)
                                    print(f"设置关键字为'店铺名称'")
                                    break

                        # 填入店铺名称到搜索框
                        if hasattr(self.one_click_order_widget, 'search_edit'):
                            search_edit = self.one_click_order_widget.search_edit
                            search_edit.setText(store_name)
                            search_edit.setFocus()  # 设置焦点到搜索框
                            print(f"设置搜索内容为: {store_name}")

                        # 延迟执行搜索，确保UI更新完成
                        def delayed_search():
                            try:
                                if hasattr(self.one_click_order_widget, 'on_search_clicked'):
                                    print(f"开始搜索店铺'{store_name}'的订单")
                                    self.one_click_order_widget.on_search_clicked()
                                else:
                                    print("警告：未找到搜索方法")
                            except Exception as e:
                                print(f"执行搜索时出错: {str(e)}")

                        # 使用QTimer延迟执行搜索，确保页面切换和设置完成
                        QTimer.singleShot(300, delayed_search)

                    # 更新状态提示
                    self.set_window_title_status(f"已跳转到一键下单页面，强制选中全部订单，正在搜索店铺: {store_name}")
                else:
                    QMessageBox.warning(self, "提示", "无法获取店铺名称")
            else:
                QMessageBox.warning(self, "提示", "未找到店铺名称列")
        else:
            QMessageBox.warning(self, "提示", "请先选择要查看订单的店铺")

    def copy_store_name(self):
        """复制选中店铺的名称"""
        current_row = self.table_widget.currentRow()
        if current_row >= 0:
            headers = []
            for col in range(self.table_widget.columnCount()):
                headers.append(self.table_widget.horizontalHeaderItem(col).text())

            store_name_col = headers.index("店铺名称") if "店铺名称" in headers else -1

            if store_name_col >= 0:
                store_name_item = self.table_widget.item(current_row, store_name_col)
                if store_name_item and store_name_item.text():
                    # 获取店铺名称并复制到剪贴板
                    store_name = store_name_item.text()
                    clipboard = QApplication.clipboard()
                    clipboard.setText(store_name)
                    self.set_window_title_status(f"已复制: {store_name}")

    def add_store(self):
        """新增店铺（仅显示消息，不实现功能）"""
        QMessageBox.information(self, "新增店铺", "新增店铺功能尚未实现")

    def edit_store(self):
        """修改店铺信息"""
        current_row = self.table_widget.currentRow()
        if current_row >= 0:
            # 获取表头信息
            headers = []
            for col in range(self.table_widget.columnCount()):
                header_item = self.table_widget.horizontalHeaderItem(col)
                if header_item:
                    headers.append(header_item.text())
                else:
                    headers.append("")

            # 获取当前行的所有数据
            shop_data = {}
            for col in range(self.table_widget.columnCount()):
                if col < len(headers) and headers[col]:  # 避免索引错误
                    item = self.table_widget.item(current_row, col)
                    if item:
                        value = item.text()
                        # 序号列不需要保存
                        if headers[col] != "序号":
                            shop_data[headers[col]] = value

            if not shop_data:
                QMessageBox.warning(self, "警告", "无法获取店铺数据")
                return

            # 获取店铺名称用于显示
            store_name = shop_data.get("店铺名称", "未知店铺")

            # 创建店铺编辑器对话框
            editor = ShopEditor(shop_data, self)

            # 连接保存信号
            editor.saveDone.connect(lambda updated_data: self.update_store_data(current_row, updated_data))

            # 显示编辑器对话框
            editor.exec_()

    def update_store_data(self, row, updated_data):
        """更新店铺数据到表格和服务器"""
        if not updated_data:
            return

        try:
            # 获取店铺ID
            store_id = updated_data.get("店铺ID")
            if not store_id:
                QMessageBox.warning(self, "更新失败", "无法获取店铺ID")
                return

            # 更新表格中的数据
            headers = []
            for col in range(self.table_widget.columnCount()):
                header_item = self.table_widget.horizontalHeaderItem(col)
                if header_item:
                    headers.append(header_item.text())
                else:
                    headers.append("")

            # 根据字段名更新表格
            for col, header in enumerate(headers):
                if header in updated_data and header != "序号":
                    item = QTableWidgetItem(str(updated_data[header]))
                    self.table_widget.setItem(row, col, item)

            # 更新状态
            self.set_window_title_status("店铺信息已更新")

            # 由于ShopEditor已经发送了更新请求，这里不再重复发送
            # 只更新UI并处理本地数据刷新

            # 可以在需要时重新加载数据以刷新视图
            # self.load_from_server()

        except Exception as e:
            QMessageBox.critical(
                self,
                "错误",
                f"发生错误: {str(e)}"
            )

    def batch_edit_invitation(self):
        """批量修改邀约信息（仅显示消息，不实现功能）"""
        selected_rows = set()
        for item in self.table_widget.selectedItems():
            selected_rows.add(item.row())

        if selected_rows:
            QMessageBox.information(self, "批量修改邀约信息", f"批量修改 {len(selected_rows)} 个店铺的邀约信息功能尚未实现")
        else:
            QMessageBox.warning(self, "警告", "请先选择要修改的店铺")

    def browse_shop_products(self):
        """浏览商品功能，打开店铺商品页面"""
        # 获取所有选中的行 - 优先使用当前行而不是复选框
        selected_rows = set()

        # 优先获取当前选中的行（鼠标点击的行）
        current_row = self.table_widget.currentRow()
        if current_row >= 0:
            selected_rows.add(current_row)

        # 如果没有当前行，则获取表格选中的行
        if not selected_rows:
            selected_items = self.table_widget.selectedItems()
            for item in selected_items:
                selected_rows.add(item.row())

        # 最后才考虑复选框选中的行（用于批量操作）
        if not selected_rows and hasattr(self, 'checkbox_delegate'):
            checkbox_selected = self.checkbox_delegate.get_checked_rows()
            if checkbox_selected:
                selected_rows = checkbox_selected

        if not selected_rows:
            QMessageBox.information(self, "浏览商品", "请先选择要浏览的店铺")
            return

        # 遍历所有选中的行，打开对应的商品页面
        for row in selected_rows:
            # 使用get_shop_data_from_row方法获取完整的店铺数据
            shop_data = self.get_shop_data_from_row(row)
            if not shop_data:
                continue

            shop_name = shop_data.get('店铺名称', '未知店铺')
            store_id = shop_data.get('店铺ID', '')

            if store_id:
                url = f"http://hp.ncjfx.cn/ss/hp/{store_id}.html"
                try:
                    # 使用默认浏览器打开URL
                    import webbrowser
                    webbrowser.open(url)
                    self.set_window_title_status(f"已打开店铺商品页面: {shop_name}")
                except Exception as e:
                    QMessageBox.warning(self, "浏览商品", f"打开浏览器失败: {str(e)}")
            else:
                QMessageBox.warning(self, "浏览商品", f"店铺 '{shop_name}' 没有店铺ID，无法打开商品页面")

    def login_store(self):
        """右键菜单店铺登录功能"""
        try:
            # 获取选中的行
            selected_rows = []
            for row in range(self.table_widget.rowCount()):
                if self.table_widget.item(row, 0) and self.table_widget.item(row, 0).isSelected():
                    selected_rows.append(row)

            if not selected_rows:
                QMessageBox.information(self, "提示", "请先选择要登录的店铺")
                return

            # 如果选中多行，只处理第一行
            if len(selected_rows) > 1:
                QMessageBox.information(self, "提示", "一次只能登录一个店铺，将登录第一个选中的店铺")

            row = selected_rows[0]
            shop_data = self.get_shop_data_from_row(row)
            if not shop_data:
                QMessageBox.warning(self, "错误", "无法获取店铺数据")
                return

            shop_name = shop_data.get('店铺名称', '未知店铺')

            # 导入登录管理器
            from 店铺后台登录 import ShopBackendLoginManager

            # 创建登录管理器并执行登录
            if not hasattr(self, 'login_manager'):
                self.login_manager = ShopBackendLoginManager(self)

            # 执行登录
            self.login_manager.login_shop(shop_data)

        except Exception as e:
            print(f"店铺登录时出错: {str(e)}")
            QMessageBox.warning(self, "错误", f"店铺登录时出错: {str(e)}")

    def view_suppliers(self):
        """右键菜单查看厂家 查看厂家功能 - 显示选中店铺的厂家信息"""
        try:
            # 获取当前选中的行
            current_row = self.table_widget.currentRow()

            if current_row < 0:
                QMessageBox.information(self, "提示", "请先选择要查看厂家的店铺")
                return

            row = current_row
            shop_data = self.get_shop_data_from_row(row)
            if not shop_data:
                QMessageBox.warning(self, "错误", "无法获取店铺数据")
                return

            shop_name = shop_data.get('店铺名称', '未知店铺')

            # 获取厂家数据
            suppliers_wangwang = shop_data.get('上家旺旺', '')
            suppliers_links = shop_data.get('上家链接', '')

            # 创建厂家信息窗口
            self.show_suppliers_window(shop_name, suppliers_wangwang, suppliers_links)

        except Exception as e:
            print(f"查看厂家信息时出错: {str(e)}")
            QMessageBox.warning(self, "错误", f"查看厂家信息时出错: {str(e)}")

    def show_suppliers_window(self, shop_name, suppliers_wangwang, suppliers_links):
        """显示厂家信息窗口

        参数:
            shop_name: 店铺名称
            suppliers_wangwang: 上家旺旺字符串，逗号分隔
            suppliers_links: 上家链接字符串，逗号分隔
        """
        # 创建厂家信息窗口 - 去掉模态设置，允许同时操作主窗口
        suppliers_window = QDialog(self)
        # suppliers_window.setModal(True)  # 注释掉模态设置
        suppliers_window.resize(700, 450)  # 增加高度以容纳自定义标题栏

        # 设置无边框窗口以实现自定义标题栏
        suppliers_window.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        suppliers_window.setAttribute(Qt.WA_TranslucentBackground)

        # 创建主容器
        main_container = QWidget(suppliers_window)
        main_container.setObjectName("mainContainer")

        # 设置阴影效果
        shadow = QGraphicsDropShadowEffect(suppliers_window)
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 60))
        shadow.setOffset(0, 4)
        main_container.setGraphicsEffect(shadow)

        # 设置窗口样式
        suppliers_window.setStyleSheet("""
            QDialog {
                background-color: transparent;
                font-family: 'Microsoft YaHei';
            }
            QWidget#mainContainer {
                background-color: #FFFFFF;
                border-radius: 12px;
                border: 1px solid #E5E7EB;
            }
            QFrame#customTitleBar {
                background-color: #FFFFFF;
                border-bottom: 1px solid #E5E7EB;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
            }
            QLabel#titleLabel {
                color: #1F2937;
                font-size: 16px;
                font-weight: 600;
                background-color: transparent;
            }
            QPushButton#closeButton {
                background-color: transparent;
                border: none;
                color: #6B7280;
                font-size: 18px;
                font-weight: bold;
                border-radius: 15px;
                width: 30px;
                height: 30px;
            }
            QPushButton#closeButton:hover {
                background-color: #F3F4F6;
                color: #374151;
            }
            QPushButton#closeButton:pressed {
                background-color: #E5E7EB;
            }
            QTableWidget {
                background-color: #FFFFFF;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                gridline-color: #F3F4F6;
                font-family: 'Microsoft YaHei';
                font-size: 13px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #F3F4F6;
            }
            QTableWidget::item:selected {
                background-color: #EEF2FF;
                color: #4338CA;
            }
            QHeaderView::section {
                background-color: #F9FAFB;
                color: #374151;
                padding: 10px;
                border: none;
                border-bottom: 2px solid #E5E7EB;
                font-weight: 600;
                font-family: 'Microsoft YaHei';
                font-size: 13px;
            }
        """)

        # 创建主容器布局
        main_layout = QVBoxLayout(suppliers_window)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.addWidget(main_container)

        # 创建容器内部布局
        container_layout = QVBoxLayout(main_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)

        # 创建自定义标题栏
        title_bar = QFrame()
        title_bar.setObjectName("customTitleBar")
        title_bar.setFixedHeight(50)

        title_bar_layout = QHBoxLayout(title_bar)
        title_bar_layout.setContentsMargins(20, 0, 15, 0)
        title_bar_layout.setSpacing(10)

        # 添加图标
        icon_label = QLabel()
        icon_path = get_resource_path(os.path.join("config", "imges", "logo.ico"))
        if os.path.exists(icon_path):
            pixmap = QIcon(icon_path).pixmap(QSize(20, 20))
            icon_label.setPixmap(pixmap)
        title_bar_layout.addWidget(icon_label)

        # 添加标题 - 支持双击复制店铺名称
        title_label = QLabel(shop_name)
        title_label.setObjectName("titleLabel")

        # 添加双击复制功能
        def copy_shop_name(event):
            """双击复制店铺名称"""
            try:
                # 获取系统剪贴板
                clipboard = QApplication.clipboard()
                # 复制店铺名称到剪贴板
                clipboard.setText(shop_name)
                # 复制成功时不显示提示，静默复制
            except Exception as e:
                # 复制失败时显示提示
                QMessageBox.warning(suppliers_window, "复制失败", f"复制店铺名称失败: {str(e)}")

        # 绑定双击事件
        title_label.mouseDoubleClickEvent = copy_shop_name

        title_bar_layout.addWidget(title_label)

        # 添加"添加上家"按钮
        add_supplier_button = QPushButton("添加上家")
        add_supplier_button.setObjectName("addSupplierButton")
        add_supplier_button.setFixedHeight(28)
        add_supplier_button.setMinimumWidth(80)
        add_supplier_button.setStyleSheet("""
            QPushButton#addSupplierButton {
                background-color: #3F60B2;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 12px;
                font-weight: 500;
                padding: 0px 12px;
            }
            QPushButton#addSupplierButton:hover {
                background-color: #354F9B;
            }
            QPushButton#addSupplierButton:pressed {
                background-color: #2B4084;
            }
        """)
        
        # 连接按钮点击事件
        def add_supplier_clicked():
            """添加上家按钮点击事件"""
            try:
                # 在表格下方添加新行
                current_row_count = table.rowCount()
                table.setRowCount(current_row_count + 1)

                # 设置新行的行高，与其他行保持一致
                table.setRowHeight(current_row_count, 40)

                # 设置新行的序号
                table.setItem(current_row_count, 0, QTableWidgetItem(str(current_row_count + 1)))
                
                # 创建可编辑的输入框
                wangwang_item = QTableWidgetItem("")
                wangwang_item.setFlags(wangwang_item.flags() | Qt.ItemIsEditable)
                table.setItem(current_row_count, 1, wangwang_item)
                
                link_item = QTableWidgetItem("")
                link_item.setFlags(link_item.flags() | Qt.ItemIsEditable)
                table.setItem(current_row_count, 2, link_item)
                
                # 滚动到新行并选中
                table.scrollToItem(wangwang_item)
                table.setCurrentItem(wangwang_item)
                
                # 检查是否已经存在按钮容器
                if not hasattr(suppliers_window, 'button_container'):
                    # 创建按钮容器 - 去掉灰色背景
                    button_container = QWidget()
                    button_container.setFixedHeight(50)
                    button_container.setStyleSheet("""
                        QWidget {
                            background-color: transparent;
                            border: none;
                        }
                    """)
                    suppliers_window.button_container = button_container
                    
                    button_layout = QHBoxLayout(button_container)
                    button_layout.setContentsMargins(20, 10, 20, 10)
                    button_layout.addStretch()
                    
                    # 创建取消按钮 - 去掉灰色背景
                    cancel_button = QPushButton("取消")
                    cancel_button.setFixedSize(80, 32)
                    cancel_button.setStyleSheet("""
                        QPushButton {
                            background-color: transparent;
                            color: #374151;
                            border: 1px solid #D1D5DB;
                            border-radius: 6px;
                            font-size: 13px;
                            font-weight: 500;
                        }
                        QPushButton:hover {
                            background-color: #F9FAFB;
                            border-color: #9CA3AF;
                        }
                        QPushButton:pressed {
                            background-color: #F3F4F6;
                        }
                    """)
                    
                    # 创建保存按钮 - 去掉背景色，使用边框样式
                    save_button = QPushButton("保存")
                    save_button.setFixedSize(80, 32)
                    save_button.setStyleSheet("""
                        QPushButton {
                            background-color: transparent;
                            color: #10B981;
                            border: 1px solid #10B981;
                            border-radius: 6px;
                            font-size: 13px;
                            font-weight: 500;
                        }
                        QPushButton:hover {
                            background-color: #F0FDF4;
                            border-color: #059669;
                            color: #059669;
                        }
                        QPushButton:pressed {
                            background-color: #DCFCE7;
                            border-color: #047857;
                            color: #047857;
                        }
                    """)
                    
                    # 定义按钮事件
                    def cancel_add():
                        """取消添加"""
                        try:
                            # 删除最后一行
                            current_row_count = table.rowCount()
                            if current_row_count > 0:
                                table.removeRow(current_row_count - 1)
                            
                            # 隐藏按钮容器
                            button_container.setVisible(False)
                        except Exception as e:
                            print(f"取消添加时出错: {str(e)}")
                    
                    def save_add():
                        """保存添加"""
                        try:
                            current_row_count = table.rowCount()
                            if current_row_count > 0:
                                # 获取新添加行的数据
                                wangwang_item = table.item(current_row_count - 1, 1)
                                link_item = table.item(current_row_count - 1, 2)
                                
                                wangwang_text = wangwang_item.text().strip() if wangwang_item else ""
                                link_text = link_item.text().strip() if link_item else ""
                                
                                if not wangwang_text and not link_text:
                                    QMessageBox.warning(suppliers_window, "提示", "请至少填写旺旺或上家链接")
                                    return
                                
                                # 保存到数据库
                                save_to_database(wangwang_text, link_text, shop_name)
                                
                        except Exception as e:
                            print(f"保存添加时出错: {str(e)}")
                            QMessageBox.warning(suppliers_window, "错误", f"保存时出错: {str(e)}")
                    
                    def save_to_database(new_wangwang, new_link, shop_name):
                        """保存上家信息到数据库 - 使用表级重复检测功能"""
                        try:
                            import requests
                            import json

                            # 如果没有要添加的内容，直接返回
                            if not new_wangwang and not new_link:
                                self.show_styled_message(suppliers_window, "提示", "请至少填写一项上家信息（旺旺或链接）", "info")
                                return

                            # 使用新的表级重复检测接口
                            insert_url = f"{self.server_url}/database"

                            # 准备要拼接的字段列表
                            append_fields = []

                            # 构建完整的插入数据包
                            insert_data = {
                                "店铺名称": shop_name,
                                "append_duplicate_scope": "table_wide",
                                "primary_key_field": "店铺名称",
                                "append_separator": "，",
                                "check_fields": ["上家旺旺"]  # 只检查上家旺旺字段的重复性
                            }

                            # 添加要拼接的字段数据
                            if new_wangwang:
                                insert_data["上家旺旺"] = new_wangwang
                                append_fields.append("上家旺旺")

                            if new_link:
                                insert_data["上家链接"] = new_link
                                append_fields.append("上家链接")

                            # 完成数据包：设置要拼接的字段列表
                            insert_data["append_fields"] = append_fields

                            # 调试：打印发送的数据
                            print("=== 调试信息 ===")
                            print(f"append_fields: {insert_data['append_fields']}")
                            print(f"发送的完整数据: {json.dumps(insert_data, ensure_ascii=False, indent=2)}")
                            print("================")

                            # 发送表级重复检测请求
                            insert_response = requests.post(
                                insert_url,
                                params={"action": "insert", "table": "A快手小店授权表"},
                                headers={'Content-Type': 'application/json'},
                                data=json.dumps(insert_data, ensure_ascii=False)
                            )

                            if insert_response.status_code == 200:
                                insert_result = insert_response.json()
                                if insert_result['status'] == 'success':
                                    # 根据不同的action类型显示相应的消息
                                    action = insert_result.get('action', 'unknown')

                                    if action == 'insert_skipped_table_wide':
                                        # 发现重复，跳过插入
                                        duplicate_field = insert_result.get('duplicate_field', '未知字段')
                                        duplicate_value = insert_result.get('duplicate_value', '未知值')
                                        duplicate_identifier_field = insert_result.get('duplicate_identifier_field', '')
                                        duplicate_identifier_value = insert_result.get('duplicate_identifier_value', '')
                                        existing_record = insert_result.get('existing_record', '')

                                        warning_msg = f"⚠️ 重复数据检测\n\n"
                                        warning_msg += f"重复字段：{duplicate_field}\n"
                                        warning_msg += f"重复值：{duplicate_value}\n"

                                        # 显示更详细的重复位置信息
                                        if duplicate_identifier_field and duplicate_identifier_value:
                                            warning_msg += f"\n该值已存在于：{duplicate_identifier_field} \"{duplicate_identifier_value}\" 中\n"

                                        if existing_record:
                                            warning_msg += f"\n完整记录内容：\n{existing_record}"

                                        # 创建自定义样式的警告弹窗
                                        msg_box = QMessageBox(suppliers_window)
                                        msg_box.setWindowTitle("重复数据")
                                        msg_box.setText(warning_msg)
                                        msg_box.setIcon(QMessageBox.Warning)
                                        msg_box.setStandardButtons(QMessageBox.Ok)

                                        # 设置警告弹窗样式 - 与主界面保持一致
                                        msg_box.setStyleSheet("""
                                            QMessageBox {
                                                background-color: #FFFFFF;
                                                border: 1px solid #E5E7EB;
                                                border-radius: 12px;
                                                font-family: 'Microsoft YaHei', sans-serif;
                                                font-size: 13px;
                                            }
                                            QMessageBox QLabel {
                                                color: #374151;
                                                padding: 20px;
                                                font-size: 14px;
                                                line-height: 1.6;
                                                background-color: transparent;
                                            }
                                            QMessageBox QPushButton {
                                                background-color: #F59E0B;
                                                color: white;
                                                border: none;
                                                border-radius: 8px;
                                                padding: 8px 20px;
                                                font-size: 13px;
                                                font-weight: 600;
                                                min-width: 100px;
                                                min-height: 36px;
                                            }
                                            QMessageBox QPushButton:hover {
                                                background-color: #D97706;
                                                transform: translateY(-1px);
                                            }
                                            QMessageBox QPushButton:pressed {
                                                background-color: #B45309;
                                                transform: translateY(0px);
                                            }
                                        """)

                                        msg_box.exec_()
                                        return

                                    elif action == 'insert_appended_table_wide':
                                        # 成功拼接到现有记录
                                        target_identifier_field = insert_result.get('target_identifier_field', '')
                                        target_identifier_value = insert_result.get('target_identifier_value', '')

                                        success_msg = f"✅ 拼接成功！\n\n"

                                        # 显示拼接的字段信息
                                        if new_wangwang:
                                            success_msg += f"字段：上家旺旺\n"
                                            success_msg += f"新增值：{new_wangwang}\n"
                                        if new_link:
                                            success_msg += f"字段：上家链接\n"
                                            success_msg += f"新增值：{new_link}\n"

                                        if target_identifier_field and target_identifier_value:
                                            success_msg += f"目标记录：{target_identifier_field} \"{target_identifier_value}\""

                                    else:
                                        # 其他情况（包括insert等）
                                        success_msg = f"✅ 保存成功！\n\n"

                                        # 显示保存的字段信息
                                        if new_wangwang:
                                            success_msg += f"字段：上家旺旺\n"
                                            success_msg += f"新增值：{new_wangwang}\n"
                                        if new_link:
                                            success_msg += f"字段：上家链接\n"
                                            success_msg += f"新增值：{new_link}\n"

                                        success_msg += f"目标记录：店铺名称 \"{shop_name}\""

                                    # 创建自定义样式的成功弹窗
                                    msg_box = QMessageBox(suppliers_window)
                                    msg_box.setWindowTitle("保存成功")
                                    msg_box.setText(success_msg)
                                    msg_box.setIcon(QMessageBox.Information)
                                    msg_box.setStandardButtons(QMessageBox.Ok)

                                    # 设置成功弹窗样式 - 与主界面保持一致
                                    msg_box.setStyleSheet("""
                                        QMessageBox {
                                            background-color: #FFFFFF;
                                            border: 1px solid #E5E7EB;
                                            border-radius: 12px;
                                            font-family: 'Microsoft YaHei', sans-serif;
                                            font-size: 13px;
                                        }
                                        QMessageBox QLabel {
                                            color: #374151;
                                            padding: 20px;
                                            font-size: 14px;
                                            line-height: 1.6;
                                            background-color: transparent;
                                        }
                                        QMessageBox QPushButton {
                                            background-color: #10B981;
                                            color: white;
                                            border: none;
                                            border-radius: 8px;
                                            padding: 8px 20px;
                                            font-size: 13px;
                                            font-weight: 600;
                                            min-width: 100px;
                                            min-height: 36px;
                                        }
                                        QMessageBox QPushButton:hover {
                                            background-color: #059669;
                                            transform: translateY(-1px);
                                        }
                                        QMessageBox QPushButton:pressed {
                                            background-color: #047857;
                                            transform: translateY(0px);
                                        }
                                    """)

                                    msg_box.exec_()

                                    # 隐藏按钮容器
                                    button_container.setVisible(False)

                                    # 刷新窗口数据 - 使用高级搜索只查询当前店铺数据
                                    def refresh_suppliers_data():
                                        """从数据库重新查询当前店铺的最新厂家数据"""
                                        try:
                                            # 构建高级搜索URL - 只查询当前店铺
                                            search_url = f"{get_configured_server_url()}/advanced_search"
                                            search_params = {
                                                "table": "A快手小店授权表",
                                                "page": 1,
                                                "page_size": 1  # 只需要1条记录
                                            }

                                            # 构建搜索条件 - 精确匹配店铺名称
                                            search_conditions = {
                                                "店铺名称": shop_name
                                            }

                                            # 发送高级搜索请求
                                            search_response = requests.post(
                                                search_url,
                                                params=search_params,
                                                headers={'Content-Type': 'application/json'},
                                                data=json.dumps(search_conditions, ensure_ascii=False),
                                                timeout=10
                                            )

                                            if search_response.status_code == 200:
                                                search_result = search_response.json()
                                                if search_result.get('status') == 'success' and 'data' in search_result:
                                                    data_list = search_result['data']
                                                    if data_list and len(data_list) > 0:
                                                        # 获取第一条（也是唯一一条）记录
                                                        shop_data = data_list[0]

                                                        # 获取最新的厂家数据
                                                        latest_wangwang = shop_data.get('上家旺旺', '')
                                                        latest_links = shop_data.get('上家链接', '')

                                                        # 更新本地缓存数据
                                                        self.update_local_cache_data(shop_name, shop_data)

                                                        # 更新主表格中当前行的数据
                                                        self.update_main_table_row(shop_name, shop_data)

                                                        # 关闭当前窗口
                                                        suppliers_window.close()

                                                        # 使用最新数据重新打开窗口
                                                        outer_self = suppliers_window.parent()
                                                        if hasattr(outer_self, 'show_suppliers_window'):
                                                            outer_self.show_suppliers_window(shop_name, latest_wangwang, latest_links)
                                                    else:
                                                        # 没找到店铺数据，显示错误
                                                        self.show_styled_message(suppliers_window, "刷新失败", f"未找到店铺 '{shop_name}' 的数据", "error")
                                                else:
                                                    # 查询失败
                                                    self.show_styled_message(suppliers_window, "刷新失败", "查询数据库失败", "error")
                                            else:
                                                # HTTP请求失败
                                                self.show_styled_message(suppliers_window, "刷新失败", f"网络请求失败，状态码：{search_response.status_code}", "error")

                                        except Exception as e:
                                            # 处理异常
                                            print(f"刷新厂家数据时出错: {str(e)}")
                                            self.show_styled_message(suppliers_window, "刷新失败", f"刷新过程中发生错误：{str(e)}", "error")

                                    # 执行刷新
                                    refresh_suppliers_data()
                                else:
                                    # 处理API返回的错误
                                    error_msg = insert_result.get('message', '未知错误')
                                    self.show_styled_message(suppliers_window, "保存失败", f"数据库操作失败：{error_msg}", "error")
                            else:
                                # 处理HTTP请求错误
                                self.show_styled_message(suppliers_window, "保存失败", f"网络请求失败，状态码：{insert_response.status_code}", "error")

                        except Exception as e:
                            # 处理程序异常
                            self.show_styled_message(suppliers_window, "保存失败", f"保存过程中发生错误：{str(e)}", "error")
                    
                    # 连接按钮事件
                    cancel_button.clicked.connect(cancel_add)
                    save_button.clicked.connect(save_add)
                    
                    # 添加按钮到布局
                    button_layout.addWidget(cancel_button)
                    button_layout.addWidget(save_button)
                    
                    # 将按钮容器添加到内容布局
                    content_layout.addWidget(button_container)
                else:
                    # 如果按钮容器已存在，直接显示
                    suppliers_window.button_container.setVisible(True)
                
            except Exception as e:
                print(f"添加上家时出错: {str(e)}")
                # 创建自定义样式的错误弹窗
                error_msg = QMessageBox(suppliers_window)
                error_msg.setWindowTitle("错误")
                error_msg.setText(f"添加上家时出错: {str(e)}")
                error_msg.setIcon(QMessageBox.Critical)
                error_msg.setStandardButtons(QMessageBox.Ok)
                error_msg.setStyleSheet("""
                    QMessageBox {
                        background-color: #f8f9fa;
                        border: 2px solid #dc3545;
                        border-radius: 8px;
                        font-family: 'Microsoft YaHei';
                        font-size: 12px;
                        color: #333;
                        padding: 10px;
                    }
                    QMessageBox QPushButton {
                        background-color: #dc3545;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 8px 16px;
                        font-weight: bold;
                        min-width: 60px;
                    }
                    QMessageBox QPushButton:hover {
                        background-color: #c82333;
                    }
                    QMessageBox QPushButton:pressed {
                        background-color: #bd2130;
                    }
                """)
                error_msg.exec_()
        
        add_supplier_button.clicked.connect(add_supplier_clicked)
        title_bar_layout.addWidget(add_supplier_button)

        # 添加弹性空间
        title_bar_layout.addStretch()

        # 添加关闭按钮
        close_button = QPushButton("×")
        close_button.setObjectName("closeButton")
        close_button.setFixedSize(30, 30)
        close_button.clicked.connect(suppliers_window.close)
        title_bar_layout.addWidget(close_button)

        # 添加标题栏到容器
        container_layout.addWidget(title_bar)

        # 创建内容区域布局
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(20, 15, 20, 20)
        content_layout.setSpacing(15)

        # 创建表格
        table = QTableWidget()
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["序号", "旺旺", "上家链接"])

        # 解析数据
        wangwang_list = []
        links_list = []

        # 只有当数据不为空且不是"none"时才解析
        if suppliers_wangwang and suppliers_wangwang.lower() != 'none':
            wangwang_list = [item.strip() for item in suppliers_wangwang.split('，') if item.strip() and item.strip().lower() != 'none']

        if suppliers_links and suppliers_links.lower() != 'none':
            links_list = [item.strip() for item in suppliers_links.split('，') if item.strip() and item.strip().lower() != 'none']

        # 确保两个列表长度一致，以较长的为准
        max_length = max(len(wangwang_list), len(links_list))

        # 如果两个列表都为空，不显示任何行
        if max_length == 0:
            table.setRowCount(0)
        else:
            # 补齐较短的列表
            while len(wangwang_list) < max_length:
                wangwang_list.append('')
            while len(links_list) < max_length:
                links_list.append('')

            # 设置表格行数
            table.setRowCount(max_length)

            # 填充数据
            for i in range(max_length):
                # 序号
                table.setItem(i, 0, QTableWidgetItem(str(i + 1)))
                # 旺旺
                table.setItem(i, 1, QTableWidgetItem(wangwang_list[i]))
                # 上家链接
                table.setItem(i, 2, QTableWidgetItem(links_list[i]))

        # 设置列宽
        table.setColumnWidth(0, 60)   # 序号列
        table.setColumnWidth(1, 200)  # 旺旺列
        table.setColumnWidth(2, 370)  # 上家链接列

        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.verticalHeader().setVisible(False)

        # 设置行高，确保输入框与单元格高度一致
        # 计算行高：字体高度 + padding(8px*2) + 边框 = 约40px
        for row in range(table.rowCount()):
            table.setRowHeight(row, 40)

        # 设置右键菜单
        table.setContextMenuPolicy(Qt.CustomContextMenu)

        # 添加右键菜单事件处理
        def show_suppliers_context_menu(position):
            """显示厂家信息表格右键菜单"""
            try:
                # 创建右键菜单
                context_menu = QMenu(suppliers_window)

                # 设置现代化菜单样式 - 圆角透明
                context_menu.setStyleSheet("""
                    QMenu {
                        background-color: white;
                        border: 1px solid #D1D5DB;
                        border-radius: 8px;
                        padding: 4px 0px;
                        font-family: 'Microsoft YaHei UI', 'Microsoft YaHei';
                        font-size: 13px;
                        font-weight: normal;
                        min-width: 160px;
                    }
                    QMenu::item {
                        background-color: transparent;
                        color: #374151;
                        padding: 8px 16px;
                        margin: 1px 4px;
                        border-radius: 4px;
                        min-width: 140px;
                        border: none;
                    }
                    QMenu::item:selected {
                        background-color: #F3F4F6;
                        color: #1F2937;
                    }
                    QMenu::item:pressed {
                        background-color: #E5E7EB;
                        color: #111827;
                    }
                    QMenu::separator {
                        height: 1px;
                        background-color: #E5E7EB;
                        margin: 3px 8px;
                        border: none;
                    }
                """)

                # 添加查看订单菜单项
                view_orders_action = QAction("📋 查看订单", suppliers_window)

                def view_supplier_orders():
                    """查看选中上家的订单"""
                    try:
                        current_row = table.currentRow()
                        if current_row < 0:
                            print("没有选中任何行")
                            return

                        # 获取选中行的旺旺名称（第1列，索引为1）
                        wangwang_item = table.item(current_row, 1)
                        if not wangwang_item or not wangwang_item.text().strip():
                            print("未找到上家旺旺名称")
                            return

                        supplier_wangwang = wangwang_item.text().strip()
                        print(f"准备查看1688订单: {supplier_wangwang}")

                        # 关闭厂家信息窗口
                        suppliers_window.close()

                        # 调用查看1688订单的方法
                        self.view_supplier_orders_from_suppliers_window(supplier_wangwang)

                    except Exception as e:
                        print(f"查看1688订单时出错: {str(e)}")

                view_orders_action.triggered.connect(view_supplier_orders)
                context_menu.addAction(view_orders_action)

                # 添加分隔符
                context_menu.addSeparator()

                # 添加删除菜单项
                delete_action = QAction("🗑️ 删除", suppliers_window)

                def delete_supplier_row():
                    """删除选中的厂家行并更新数据库"""
                    try:
                        current_row = table.currentRow()
                        if current_row < 0:
                            self.show_styled_message(suppliers_window, "提示", "请先选择要删除的厂家行", "warning")
                            return

                        # 获取选中行的旺旺名称用于确认
                        wangwang_item = table.item(current_row, 1)
                        wangwang_name = wangwang_item.text().strip() if wangwang_item else "未知厂家"

                        # 确认删除 - 使用自定义样式对话框
                        reply = self.show_styled_confirmation(
                            suppliers_window,
                            "确认删除厂家",
                            f"确定要删除厂家 '{wangwang_name}' 吗？\n\n此操作将同时更新数据库，不可撤销。",
                            "🗑️ 删除",
                            "❌ 取消"
                        )

                        if reply:
                            # 1. 收集当前表格中的所有数据（除了要删除的行）
                            updated_wangwang_list = []
                            updated_links_list = []

                            for row in range(table.rowCount()):
                                if row != current_row:  # 跳过要删除的行
                                    wangwang_item = table.item(row, 1)
                                    link_item = table.item(row, 2)

                                    wangwang_text = wangwang_item.text().strip() if wangwang_item else ""
                                    link_text = link_item.text().strip() if link_item else ""

                                    updated_wangwang_list.append(wangwang_text)
                                    updated_links_list.append(link_text)

                            # 2. 将数组重新组合成逗号分隔的字符串
                            updated_wangwang_str = "，".join(updated_wangwang_list)
                            updated_links_str = "，".join(updated_links_list)

                            # 3. 更新数据库 - 使用条件更新格式
                            try:
                                # 构建条件更新格式的数据
                                update_data = {
                                    "conditions": {
                                        "店铺名称": shop_name
                                    },
                                    "values": {
                                        "上家旺旺": updated_wangwang_str,
                                        "上家链接": updated_links_str
                                    }
                                }

                                # 调用数据库更新API
                                server_url = get_configured_server_url()
                                update_url = f"{server_url}/database"

                                # 使用条件更新模式
                                update_params = {
                                    "action": "update",
                                    "table": "A快手小店授权表"
                                }

                                update_response = requests.post(
                                    update_url,
                                    params=update_params,
                                    headers={'Content-Type': 'application/json'},
                                    data=json.dumps(update_data, ensure_ascii=False),
                                    timeout=10
                                )

                                if update_response.status_code == 200:
                                    result = update_response.json()
                                    if result.get('status') == 'success':
                                        # 4. 数据库更新成功，删除表格行并重新设置序号
                                        table.removeRow(current_row)

                                        # 重新设置序号
                                        for row in range(table.rowCount()):
                                            table.setItem(row, 0, QTableWidgetItem(str(row + 1)))

                                        # 5. 更新本地缓存
                                        updated_shop_data = {
                                            "上家旺旺": updated_wangwang_str,
                                            "上家链接": updated_links_str
                                        }
                                        self.update_local_cache_data(shop_name, updated_shop_data)

                                        # 6. 更新主表格中的数据
                                        self.update_main_table_row(shop_name, updated_shop_data)

                                        print(f"✅ 已删除厂家: {wangwang_name}")
                                        self.show_styled_message(suppliers_window, "删除成功", f"已删除厂家 '{wangwang_name}'\n数据已同步到数据库", "success")
                                    else:
                                        error_msg = result.get('message', '未知错误')
                                        self.show_styled_message(suppliers_window, "删除失败", f"数据库更新失败：{error_msg}", "error")
                                else:
                                    self.show_styled_message(suppliers_window, "删除失败", f"数据库请求失败，状态码：{update_response.status_code}", "error")

                            except Exception as db_error:
                                print(f"❌ 数据库更新失败: {str(db_error)}")
                                self.show_styled_message(suppliers_window, "删除失败", f"数据库更新失败：{str(db_error)}", "error")

                    except Exception as e:
                        print(f"❌ 删除厂家时出错: {str(e)}")
                        self.show_styled_message(suppliers_window, "删除失败", f"删除厂家时出错：{str(e)}", "error")

                delete_action.triggered.connect(delete_supplier_row)
                context_menu.addAction(delete_action)

                # 设置菜单属性以实现透明圆角
                context_menu.setAttribute(Qt.WA_TranslucentBackground, True)
                context_menu.setWindowFlags(Qt.Popup | Qt.FramelessWindowHint | Qt.NoDropShadowWindowHint)

                # 显示右键菜单
                cursor_pos = QCursor.pos()
                context_menu.exec_(cursor_pos)

            except Exception as e:
                print(f"显示右键菜单时出错: {str(e)}")

        # 连接右键菜单信号
        table.customContextMenuRequested.connect(show_suppliers_context_menu)

        # 添加双击事件处理 - 双击打开上家链接
        def on_table_double_clicked(item):
            """处理表格双击事件 - 打开上家链接"""
            try:
                if not item:
                    return

                row = item.row()
                # 获取上家链接列的数据（第2列，索引为2）
                link_item = table.item(row, 2)
                if link_item and link_item.text().strip():
                    link_url = link_item.text().strip()
                    print(f"正在打开上家链接: {link_url}")

                    # 使用默认浏览器打开链接
                    import webbrowser
                    webbrowser.open(link_url)
                else:
                    print("该行没有上家链接")

            except Exception as e:
                print(f"打开上家链接时出错: {str(e)}")

        # 连接双击信号
        table.itemDoubleClicked.connect(on_table_double_clicked)

        # 添加表格到内容布局
        content_layout.addWidget(table)

        # 将内容布局添加到容器
        container_layout.addLayout(content_layout)

        # 添加窗口拖拽功能
        def mousePressEvent(event):
            if event.button() == Qt.LeftButton:
                suppliers_window.drag_position = event.globalPos() - suppliers_window.frameGeometry().topLeft()
                event.accept()

        def mouseMoveEvent(event):
            if event.buttons() == Qt.LeftButton and hasattr(suppliers_window, 'drag_position'):
                suppliers_window.move(event.globalPos() - suppliers_window.drag_position)
                event.accept()

        # 绑定拖拽事件到标题栏
        title_bar.mousePressEvent = mousePressEvent
        title_bar.mouseMoveEvent = mouseMoveEvent

        # 显示窗口 - 使用非模态显示，允许同时操作主窗口
        suppliers_window.show()

    def refresh_account_table_from_local(self):
        """从本地config\账号管理.json文件刷新账号管理表格"""
        try:
            # 显示刷新状态
            self.set_window_title_status("🔄 正在从本地文件刷新数据...")

            # 读取本地缓存文件
            cache_file_path = get_config_path("账号管理.json")

            if not os.path.exists(cache_file_path):
                self.show_styled_message(self, "刷新失败", f"本地缓存文件不存在：{cache_file_path}", "error")
                self.set_window_title_status("")
                return

            # 读取缓存数据
            with open(cache_file_path, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)

            # 检查数据格式
            if 'data' not in cache_data:
                self.show_styled_message(self, "刷新失败", "本地缓存文件格式错误，缺少data字段", "error")
                self.set_window_title_status("")
                return

            records = cache_data['data']
            if not isinstance(records, list):
                self.show_styled_message(self, "刷新失败", "本地缓存文件格式错误，data字段不是数组", "error")
                self.set_window_title_status("")
                return

            # 清空当前表格
            if hasattr(self, 'table_widget') and self.table_widget:
                # 检查是否有数据
                if not records:
                    self.table_widget.setRowCount(0)
                    self.show_styled_message(self, "刷新完成", "本地缓存文件为空", "warning")
                    self.set_window_title_status("账号数据 - 本地缓存为空")
                    return

                # 从第一条记录获取字段名
                fields = list(records[0].keys())

                # 保存所有字段
                self.all_fields = fields.copy()

                # 如果有ID字段，将其移至最后
                if 'ID' in fields:
                    fields.remove('ID')

                # 如果有字段设置，则按照设置的顺序排列并过滤字段
                if hasattr(self, 'field_settings') and self.field_settings:
                    # 获取要显示的字段（按设置顺序）
                    visible_fields = [field for field in self.field_settings if field in fields]
                    # 更新字段列表
                    fields = visible_fields

                # 设置列数为字段数+1(包括序号列)
                self.table_widget.setColumnCount(len(fields) + 1)

                # 创建表头标签，添加序号列
                headers = ["序号"] + fields
                self.table_widget.setHorizontalHeaderLabels(headers)

                # 重新填充表格数据
                self.table_widget.setRowCount(len(records))

                for row_idx, row_data in enumerate(records):
                    # 添加序号列（使用自定义的带复选框的表格项）
                    seq_item = CheckBoxNumericTableWidgetItem(row_idx + 1)
                    self.table_widget.setItem(row_idx, 0, seq_item)

                    # 添加数据列
                    for col_idx, field_name in enumerate(fields):
                        value = row_data.get(field_name, "")

                        # 特殊处理在售列：添加上家统计信息
                        if field_name == "在售" and "上家旺旺" in row_data:
                            supplier_data = row_data.get("上家旺旺", "")
                            if supplier_data and isinstance(supplier_data, str):
                                # 按逗号分割上家旺旺数据
                                suppliers = [s.strip() for s in supplier_data.replace('，', ',').split(',') if s.strip()]
                                supplier_count = len(suppliers)
                                # 在原有在售数据后添加上家统计信息，格式：[上家数量]在售数量
                                if supplier_count > 0:
                                    value = f"[{supplier_count}]{value}"

                        # 根据列名决定使用哪种 TableWidgetItem
                        if field_name in self.numeric_sort_columns:
                            item = NumericTableWidgetItem(value) # 使用原始值value创建
                        else:
                            item = QTableWidgetItem(str(value))

                        # 特殊处理保证金列：如果分数格式前面数值小于后面数值，设置红色字体
                        if field_name == "保证金" and isinstance(value, str) and '/' in value:
                            try:
                                # 提取斜杠前后的数字
                                parts = value.split('/')
                                if len(parts) >= 2 and parts[0].strip() and parts[1].strip():
                                    front_value = float(parts[0].strip())  # 斜杠前面的数值
                                    back_value = float(parts[1].strip())   # 斜杠后面的数值
                                    if front_value < back_value:
                                        # 设置字体颜色为红色
                                        item.setForeground(QColor(255, 0, 0))  # 红色
                            except (ValueError, TypeError):
                                pass  # 如果转换失败，保持默认颜色

                        # 设置工具提示，鼠标悬停时显示完整内容
                        item.setToolTip(str(value))
                        self.table_widget.setItem(row_idx, col_idx + 1, item)  # +1 因为第0列是序号列

                # 显示成功信息
                cache_time = cache_data.get('timestamp', '未知时间')
                success_msg = f"✅ 刷新成功！\n\n"
                success_msg += f"数据来源：本地缓存文件\n"
                success_msg += f"记录数量：{len(records)} 条\n"
                success_msg += f"缓存时间：{cache_time}"

                self.show_styled_message(self, "刷新成功", success_msg, "success")
                self.set_window_title_status(f"账号数据 - 已从本地缓存刷新（{len(records)}条记录）")

                print(f"✅ 成功从本地缓存刷新账号管理表格，共{len(records)}条记录")
            else:
                self.show_styled_message(self, "刷新失败", "表格组件不存在", "error")
                self.set_window_title_status("")

        except json.JSONDecodeError as e:
            error_msg = f"本地缓存文件JSON格式错误：{str(e)}"
            self.show_styled_message(self, "刷新失败", error_msg, "error")
            self.set_window_title_status("")
            print(f"❌ JSON解析错误: {error_msg}")

        except Exception as e:
            error_msg = f"刷新过程中发生错误：{str(e)}"
            self.show_styled_message(self, "刷新失败", error_msg, "error")
            self.set_window_title_status("")
            print(f"❌ 刷新账号管理表格时出错: {error_msg}")

    def update_main_table_row(self, shop_name, updated_shop_data):
        """更新主表格中当前行的数据

        参数:
            shop_name: 店铺名称
            updated_shop_data: 从数据库获取的最新店铺数据
        """
        try:
            # 检查是否有主表格
            if not hasattr(self, 'table_widget') or not self.table_widget:
                print("主表格不存在，跳过更新")
                return

            # 查找店铺名称列的索引
            shop_name_column = -1
            wangwang_column = -1
            link_column = -1

            for col in range(self.table_widget.columnCount()):
                header_item = self.table_widget.horizontalHeaderItem(col)
                if header_item:
                    header_text = header_item.text()
                    if "店铺名称" in header_text:
                        shop_name_column = col
                    elif "上家旺旺" in header_text:
                        wangwang_column = col
                    elif "上家链接" in header_text:
                        link_column = col

            if shop_name_column == -1:
                print("未找到店铺名称列，跳过更新")
                return

            # 查找对应的行
            target_row = -1
            for row in range(self.table_widget.rowCount()):
                item = self.table_widget.item(row, shop_name_column)
                if item and item.text() == shop_name:
                    target_row = row
                    break

            if target_row == -1:
                print(f"在主表格中未找到店铺 '{shop_name}'，跳过更新")
                return

            # 更新上家旺旺列
            if wangwang_column != -1:
                latest_wangwang = updated_shop_data.get('上家旺旺', '')
                wangwang_item = QTableWidgetItem(latest_wangwang)
                self.table_widget.setItem(target_row, wangwang_column, wangwang_item)
                print(f"已更新主表格第{target_row+1}行的上家旺旺数据")

            # 更新上家链接列
            if link_column != -1:
                latest_links = updated_shop_data.get('上家链接', '')
                link_item = QTableWidgetItem(latest_links)
                self.table_widget.setItem(target_row, link_column, link_item)
                print(f"已更新主表格第{target_row+1}行的上家链接数据")

            # 刷新表格显示
            self.table_widget.viewport().update()
            print(f"主表格行数据更新完成：店铺 '{shop_name}'")

        except Exception as e:
            print(f"更新主表格行数据时出错: {str(e)}")

    def update_local_cache_data(self, shop_name, updated_shop_data):
        """更新本地缓存数据

        参数:
            shop_name: 店铺名称
            updated_shop_data: 从数据库获取的最新店铺数据
        """
        try:
            cache_file_path = get_config_path("账号管理.json")

            # 检查缓存文件是否存在
            if not os.path.exists(cache_file_path):
                print(f"缓存文件不存在: {cache_file_path}")
                return

            # 读取现有缓存数据
            with open(cache_file_path, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)

            # 查找并更新对应的店铺记录
            updated = False
            if 'data' in cache_data and isinstance(cache_data['data'], list):
                for i, record in enumerate(cache_data['data']):
                    if record.get('店铺名称') == shop_name:
                        # 更新厂家相关字段
                        record['上家旺旺'] = updated_shop_data.get('上家旺旺', '')
                        record['上家链接'] = updated_shop_data.get('上家链接', '')

                        # 可以选择性更新其他字段
                        # record['备注'] = updated_shop_data.get('备注', record.get('备注', ''))

                        cache_data['data'][i] = record
                        updated = True
                        print(f"已更新本地缓存中店铺 '{shop_name}' 的厂家数据")
                        break

            if updated:
                # 更新时间戳
                cache_data['timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 写回缓存文件
                with open(cache_file_path, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, ensure_ascii=False, indent=2)

                print(f"本地缓存已更新: {cache_file_path}")
            else:
                print(f"在本地缓存中未找到店铺 '{shop_name}'")

        except Exception as e:
            print(f"更新本地缓存时出错: {str(e)}")

    def view_supplier_orders_from_suppliers_window(self, supplier_wangwang):
        """从厂家信息窗口查看1688订单

        参数:
            supplier_wangwang: 上家旺旺名称
        """
        try:
            print(f"准备查看上家 '{supplier_wangwang}' 的订单")

            # 检查是否有一键下单组件
            if not hasattr(self, 'one_click_order_widget'):
                print("主窗口没有一键下单组件")
                return

            one_click_widget = self.one_click_order_widget

            # 设置日期范围为最近6个月
            from PyQt5.QtCore import QDateTime, QTime, QTimer
            current_date = QDateTime.currentDateTime()
            six_months_ago = current_date.addMonths(-6)

            # 设置开始日期为6个月前的0点0分0秒
            start_datetime = QDateTime(six_months_ago.date(), QTime(0, 0, 0))
            if hasattr(one_click_widget, 'start_date'):
                one_click_widget.start_date.setDateTime(start_datetime)
                print(f"设置开始日期: {start_datetime.toString('yyyy-MM-dd HH:mm:ss')}")

            # 设置结束日期为今天的23点59分59秒
            end_datetime = QDateTime(current_date.date(), QTime(23, 59, 59))
            if hasattr(one_click_widget, 'end_date'):
                one_click_widget.end_date.setDateTime(end_datetime)
                print(f"设置结束日期: {end_datetime.toString('yyyy-MM-dd HH:mm:ss')}")

            # 设置关键字下拉框为"软件备注"
            if hasattr(one_click_widget, 'keyword_combo'):
                keyword_combo = one_click_widget.keyword_combo
                for i in range(keyword_combo.count()):
                    if keyword_combo.itemText(i) == "软件备注":
                        keyword_combo.setCurrentIndex(i)
                        print("设置关键字为: 软件备注")
                        break

            # 将上家旺旺名称填入搜索框
            if hasattr(one_click_widget, 'search_edit'):
                one_click_widget.search_edit.setText(supplier_wangwang)
                print(f"设置搜索内容: {supplier_wangwang}")

            # 切换到一键下单页面和"全部订单"页面
            self.switch_to_all_orders_page_for_supplier(one_click_widget)

            # 延迟执行搜索，确保页面切换完成
            QTimer.singleShot(500, lambda: self.execute_search_for_supplier(one_click_widget, supplier_wangwang))

            # 更新状态
            self.set_window_title_status(f"正在查看上家 '{supplier_wangwang}' 的订单")

        except Exception as e:
            print(f"查看1688订单时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def switch_to_all_orders_page_for_supplier(self, one_click_widget):
        """切换到全部订单页面（供应商查看专用）

        参数:
            one_click_widget: 一键下单组件实例
        """
        try:
            print("开始切换到全部订单页面")

            # 方法1：直接调用主窗口的显示一键下单页面方法
            if hasattr(self, 'show_one_click_order_page'):
                self.show_one_click_order_page()
                print("通过show_one_click_order_page切换到一键下单页面")

            # 方法2：如果一键下单组件有菜单按钮，尝试点击"全部订单"按钮
            if hasattr(one_click_widget, 'menu_buttons'):
                menu_buttons = one_click_widget.menu_buttons
                for text, button in menu_buttons.items():
                    if "全部订单" in text:
                        button.setChecked(True)
                        button.click()
                        print(f"点击了 '{text}' 按钮")
                        break

            # 方法3：如果有快速查找下拉框，设置为"全部"
            if hasattr(one_click_widget, 'quickfind_combo'):
                quickfind_combo = one_click_widget.quickfind_combo
                for i in range(quickfind_combo.count()):
                    if quickfind_combo.itemText(i) in ["全部", "全部订单"]:
                        quickfind_combo.setCurrentIndex(i)
                        print("设置快速查找为: 全部")
                        break

        except Exception as e:
            print(f"切换到全部订单页面时出错: {str(e)}")

    def execute_search_for_supplier(self, one_click_widget, supplier_wangwang):
        """执行供应商搜索

        参数:
            one_click_widget: 一键下单组件实例
            supplier_wangwang: 上家旺旺名称
        """
        try:
            print(f"开始执行搜索: {supplier_wangwang}")

            # 尝试调用搜索方法
            if hasattr(one_click_widget, 'on_search_clicked'):
                one_click_widget.on_search_clicked()
                print("调用了on_search_clicked方法")
            elif hasattr(one_click_widget, 'search_button'):
                # 如果有搜索按钮，点击它
                one_click_widget.search_button.click()
                print("点击了搜索按钮")

            print(f"搜索执行完成: {supplier_wangwang}")

            # 更新窗口标题显示状态
            original_title = self.windowTitle()
            self.setWindowTitle(f"{original_title} - 正在查看上家 '{supplier_wangwang}' 的订单")

            # 3秒后恢复标题
            QTimer.singleShot(3000, lambda: self.setWindowTitle(original_title))

        except Exception as e:
            print(f"执行搜索时出错: {str(e)}")

    def close_all_backends(self):
        """关闭所有店铺后台浏览器窗口 - 完全异步执行，避免卡住主界面"""
        try:
            # 检查是否有登录管理器
            if not hasattr(self, 'login_manager'):
                self.set_window_title_status("没有打开的店铺后台")
                QTimer.singleShot(3000, lambda: self.set_window_title_status(""))
                return

            # 检查是否有活跃的浏览器窗口
            window_count = self.login_manager.get_window_count()
            if window_count == 0:
                self.set_window_title_status("没有需要关闭的店铺后台")
                QTimer.singleShot(3000, lambda: self.set_window_title_status(""))
                return

            # 立即显示关闭状态，不等待
            self.set_window_title_status("正在快速关闭浏览器...")

            # 使用QTimer延迟执行关闭操作，确保UI立即响应
            QTimer.singleShot(10, self._execute_close_backends)

        except Exception as e:
            print(f"关闭店铺后台时出错: {str(e)}")
            self.set_window_title_status(f"关闭失败: {str(e)}")
            QTimer.singleShot(3000, lambda: self.set_window_title_status(""))

    def _execute_close_backends(self):
        """实际执行关闭操作 - 在延迟后执行"""
        try:
            # 连接状态更新信号
            self.login_manager.status_update.connect(self.set_window_title_status)
            self.login_manager.close_completed.connect(self.on_backends_closed)

            # 执行关闭操作（在线程中）
            self.login_manager.close_all_windows()

        except Exception as e:
            print(f"执行关闭操作时出错: {str(e)}")
            self.set_window_title_status(f"关闭失败: {str(e)}")
            QTimer.singleShot(3000, lambda: self.set_window_title_status(""))

    def on_backends_closed(self, closed_count):
        """
        店铺后台关闭完成的处理方法

        参数:
            closed_count (int): 成功关闭的窗口数量
        """
        try:
            if closed_count > 0:
                self.set_window_title_status(f"已成功关闭 {closed_count} 个店铺后台")
            else:
                self.set_window_title_status("关闭操作完成，但没有窗口被关闭")

            # 3秒后清除状态
            QTimer.singleShot(3000, lambda: self.set_window_title_status(""))

            print(f"店铺后台关闭完成，共关闭 {closed_count} 个窗口")

        except Exception as e:
            print(f"处理后台关闭完成事件时出错: {str(e)}")



    def resizeEvent(self, event):
        """窗口大小变化时保持序号列固定"""
        super().resizeEvent(event)
        # 确保表格存在并且有数据时，保持序号列固定
        if hasattr(self, 'table_widget') and self.table_widget.columnCount() > 0:
            self.table_widget.setColumnWidth(0, 50)

    def show_styled_confirmation(self, parent, title, message, confirm_text="确定", cancel_text="取消"):
        """显示样式化的确认对话框

        参数:
            parent: 父窗口
            title: 对话框标题
            message: 消息内容
            confirm_text: 确认按钮文本
            cancel_text: 取消按钮文本

        返回:
            bool: True表示确认，False表示取消
        """
        # 创建自定义对话框
        dialog = QDialog(parent)
        dialog.setWindowTitle(title)
        dialog.setModal(True)
        dialog.setFixedSize(400, 200)

        # 设置对话框样式
        dialog.setStyleSheet("""
            QDialog {
                background-color: white;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
            }
            QLabel {
                color: #374151;
                font-size: 14px;
                line-height: 1.5;
                padding: 10px;
            }
            QPushButton {
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: 500;
                min-width: 80px;
                min-height: 32px;
            }
            QPushButton:hover {
                background-color: #f1f5f9;
                border-color: #cbd5e1;
            }
            QPushButton:pressed {
                background-color: #e2e8f0;
            }
            QPushButton#confirmBtn {
                background-color: #dc2626;
                color: white;
                border-color: #dc2626;
            }
            QPushButton#confirmBtn:hover {
                background-color: #b91c1c;
                border-color: #b91c1c;
            }
            QPushButton#confirmBtn:pressed {
                background-color: #991b1b;
            }
        """)

        # 创建布局
        layout = QVBoxLayout(dialog)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # 添加消息标签
        message_label = QLabel(message)
        message_label.setWordWrap(True)
        message_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(message_label)

        # 添加按钮布局
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        # 取消按钮
        cancel_btn = QPushButton(cancel_text)
        cancel_btn.clicked.connect(dialog.reject)
        button_layout.addWidget(cancel_btn)

        # 确认按钮
        confirm_btn = QPushButton(confirm_text)
        confirm_btn.setObjectName("confirmBtn")
        confirm_btn.clicked.connect(dialog.accept)
        button_layout.addWidget(confirm_btn)

        layout.addLayout(button_layout)

        # 显示对话框并返回结果
        result = dialog.exec_()
        return result == QDialog.Accepted

    def show_styled_message(self, parent, title, message, msg_type="info"):
        """显示自定义样式的消息框

        Args:
            parent: 父窗口
            title: 标题
            message: 消息内容
            msg_type: 消息类型 ("info", "warning", "error", "success")
        """
        msg_box = QMessageBox(parent)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStandardButtons(QMessageBox.Ok)

        # 根据消息类型设置图标和样式 - 与主界面保持一致
        if msg_type == "error":
            msg_box.setIcon(QMessageBox.Critical)
            btn_color = "#EF4444"
            btn_hover = "#DC2626"
            btn_pressed = "#B91C1C"
        elif msg_type == "warning":
            msg_box.setIcon(QMessageBox.Warning)
            btn_color = "#F59E0B"
            btn_hover = "#D97706"
            btn_pressed = "#B45309"
        elif msg_type == "success":
            msg_box.setIcon(QMessageBox.Information)
            btn_color = "#10B981"
            btn_hover = "#059669"
            btn_pressed = "#047857"
        else:  # info
            msg_box.setIcon(QMessageBox.Information)
            btn_color = "#4F46E5"
            btn_hover = "#4338CA"
            btn_pressed = "#3730A3"

        # 设置统一的现代化样式
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: #FFFFFF;
                border: 1px solid #E5E7EB;
                border-radius: 12px;
                font-family: 'Microsoft YaHei', sans-serif;
                font-size: 13px;
            }}
            QMessageBox QLabel {{
                color: #374151;
                padding: 20px;
                font-size: 14px;
                line-height: 1.6;
                background-color: transparent;
            }}
            QMessageBox QPushButton {{
                background-color: {btn_color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 20px;
                font-size: 13px;
                font-weight: 600;
                min-width: 100px;
                min-height: 36px;
            }}
            QMessageBox QPushButton:hover {{
                background-color: {btn_hover};
                transform: translateY(-1px);
            }}
            QMessageBox QPushButton:pressed {{
                background-color: {btn_pressed};
                transform: translateY(0px);
            }}
        """)

        msg_box.exec_()

    def on_login_success(self, username, password, user_info):
        """登录成功后显示主窗口并加载数据"""
        # 保存用户登录信息
        self.username = username
        self.user_info = user_info

        # 更新窗口标题，增加角色显示
        role_display = f" [{user_info.get('role', '用户')}]" if user_info else ""
        user_part = f"{username}{role_display}"
        _new_base = f"{self.base_title_prefix} - {user_part}"
        if self.instance_id != 0:
            _new_base += f' - 实例 {self.instance_id + 1}'
        self.base_window_title = _new_base # 更新基础标题以包含用户信息
        self.set_window_title_status("登录成功，正在加载初始数据...") # 使用新方法设置标题和状态

        # 显示主窗口
        self.show()

        # 根据用户角色设置权限
        self.set_permissions_by_role(user_info.get('role', 'user'))

        # 启动时只从缓存加载数据，不从服务器获取
        if self.load_from_cache():
            print("成功从缓存加载账号数据")
        else:
            print("无缓存数据，请点击账号列表按钮加载数据")
            # 显示提示信息
            self.set_window_title_status("请点击账号列表按钮加载数据")
            QTimer.singleShot(5000, lambda: self.set_window_title_status(""))



    def set_permissions_by_role(self, role):
        """根据用户角色设置界面权限"""
        # 默认所有功能可用
        all_enabled = True

        if role.lower() == 'admin':
            # 管理员有所有权限
            pass
        elif role.lower() == 'editor':
            # 编辑可以编辑但不能删除
            # 可以在这里禁用特定按钮或功能
            pass
        elif role.lower() == 'viewer':
            # 查看者只能查看，不能编辑或删除
            all_enabled = False
            # 禁用编辑和删除功能

        # 根据角色设置界面组件的启用状态
        if not all_enabled:
            # 示例：禁用删除功能
            # 可以在这里找到所有需要禁用的按钮并设置其状态
            # 例如: self.delete_btn.setEnabled(False)
            pass

    def update_menu_styles(self, active_menu):
        """统一更新菜单样式的方法

        Parameters:
        -----------
        active_menu : str
            当前活动的菜单项名称
        """
        # 遍历所有菜单按钮，设置样式
        for menu_name, button in self.menu_buttons.items():
            if menu_name == active_menu:
                button.setStyleSheet(self.active_menu_style)
            else:
                button.setStyleSheet(self.default_menu_style)

    def init_product_copy_page(self):
        """初始化商品复制页面"""
        from 商品复制 import ProductCopyWindow

        # 创建商品复制页面
        product_copy_layout = QVBoxLayout(self.product_copy_page)
        product_copy_layout.setContentsMargins(0, 0, 0, 0)
        product_copy_layout.setSpacing(0)

        self.product_copy_widget = ProductCopyWindow()
        product_copy_widget = self.product_copy_widget.centralWidget()
        product_copy_layout.addWidget(product_copy_widget)

    def show_product_copy_page(self):
        """显示商品复制页面

        切换到主窗口中的商品复制页面标签
        更新菜单按钮样式以反映当前活动页面
        """
        self.stacked_widget.setCurrentWidget(self.product_copy_page)

        # 更新菜单按钮样式 - 使用统一的方法
        self.update_menu_styles("商品复制")



    def showEvent(self, event):
        """窗口显示事件"""
        super().showEvent(event)
        # 仅保留必要的初始化代码，移除特效设置

        # 设置窗口标题栏颜色为白色 (#FFFFFF)
        if sys.platform == 'win32':
            try:
                import ctypes
                DWMWA_CAPTION_COLOR = 35  # Windows 11 22000以上版本适用

                # 将颜色值从 #FFFFFF 转换为 COLORREF 值 (BGR格式)
                # 白色的 COLORREF 值为 0x00FFFFFF
                caption_color = ctypes.c_int(0x00FFFFFF)

                # 设置窗口标题栏颜色
                ctypes.windll.dwmapi.DwmSetWindowAttribute(
                    int(self.winId()),
                    DWMWA_CAPTION_COLOR,
                    ctypes.byref(caption_color),
                    ctypes.sizeof(caption_color)
                )
                print("窗口标题栏颜色已设置为白色")
            except Exception as e:
                print(f"设置窗口标题栏颜色失败: {str(e)}")

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        try:
            # 显示自定义确认对话框
            dialog = CustomConfirmDialog(self)
            reply = dialog.exec_()

            # 如果用户选择"取消"，则取消关闭操作
            if reply != QDialog.Accepted:
                event.ignore()
                return

            print("🔄 正在关闭程序...")

            # 关闭所有店铺后台
            if hasattr(self, 'login_manager'):
                print("🔄 正在关闭所有店铺后台...")
                self.login_manager.close_all_windows()

            # 不再清理chromedriver进程，避免误杀正常工作的进程
            # try:
            #     from 店铺后台登录 import cleanup_all_chromedriver_processes
            #     print("🧹 正在清理ChromeDriver进程...")
            #     cleanup_all_chromedriver_processes()
            # except Exception as e:
            #     print(f"清理ChromeDriver进程时出错: {str(e)}")
            print("⚠️ ChromeDriver进程清理已禁用，防止误杀正常工作的进程")

            print("✅ 程序关闭清理完成")
            # 接受关闭事件
            event.accept()
        except Exception as e:
            print(f"关闭窗口时出错: {str(e)}")
            event.accept()



    def show_field_settings(self):
        """显示字段设置窗口，用户可以选择要显示的字段和顺序"""
        # 确保已经加载了数据并获取了字段列表
        if not self.all_fields:
            QMessageBox.information(self, "提示", "请先加载数据才能设置字段显示。")
            return

        field_dialog = FieldSettingsDialog(self.all_fields, self.field_settings, self)
        if field_dialog.exec_() == QDialog.Accepted:
            # 获取用户选择的字段列表（按顺序）
            self.field_settings = field_dialog.get_selected_fields()
            # 保存设置
            self.save_field_settings()
            # 重新加载数据以应用新的字段设置
            if hasattr(self, 'table_widget') and self.table_widget.rowCount() > 0:
                # 获取当前表格中的数据
                records = []
                field_count = self.table_widget.columnCount() - 1  # 减去序号列
                row_count = self.table_widget.rowCount()

                # 重新构建数据记录
                for row in range(row_count):
                    record = {}
                    for col in range(1, field_count + 1):  # 跳过序号列
                        field_name = self.table_widget.horizontalHeaderItem(col).text()
                        item = self.table_widget.item(row, col)
                        if item:
                            record[field_name] = item.text()
                    # 添加到记录列表
                    if record:
                        records.append(record)

                # 重新显示数据
                if records:
                    self.display_data(records)

    def save_field_settings(self):
        """将字段设置保存到本地缓存，使用JSON格式"""
        try:
            # 创建包含数据和时间戳的缓存对象
            cache_data = {
                'field_settings': self.field_settings,
                'timestamp': datetime.now().timestamp()
            }

            # 确保config目录存在
            config_dir = os.path.dirname(self.field_settings_cache)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)

            # 保存到本地文件（JSON格式）
            with open(self.field_settings_cache, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=4)

            print(f"字段设置已保存到: {self.field_settings_cache}")
            return True
        except Exception as e:
            print(f"保存字段设置失败: {str(e)}")
            return False

    def load_field_settings(self):
        """从本地缓存加载字段设置，支持从旧格式平稳过渡到新格式"""
        try:
            # 首先尝试从新的JSON格式文件加载
            if os.path.exists(self.field_settings_cache):
                try:
                    with open(self.field_settings_cache, 'r', encoding='utf-8') as f:
                        cache_data = json.load(f)

                    # 解析缓存数据
                    if 'field_settings' in cache_data:
                        self.field_settings = cache_data['field_settings']
                        print(f"从JSON格式加载字段设置: {self.field_settings_cache}")
                        return True
                except Exception as e:
                    print(f"加载JSON格式字段设置失败: {str(e)}")

            # 如果新格式文件不存在或加载失败，尝试从旧的pickle格式文件加载并转换
            if os.path.exists(self.old_field_settings_cache):
                try:
                    with open(self.old_field_settings_cache, 'rb') as f:
                        self.field_settings = pickle.load(f)

                    print(f"从旧的pickle格式加载字段设置: {self.old_field_settings_cache}")

                    # 自动保存为新的JSON格式
                    if self.save_field_settings():
                        print("已自动转换并保存为JSON格式")
                        # 删除旧的pickle文件
                        try:
                            os.remove(self.old_field_settings_cache)
                            print("已删除旧的pickle格式文件")
                        except Exception as e:
                            print(f"删除旧文件失败: {str(e)}")

                    return True
                except Exception as e:
                    print(f"加载旧格式字段设置失败: {str(e)}")

            return False
        except Exception as e:
            print(f"加载字段设置失败: {str(e)}")
            return False

    def init_plan_manager_page(self):
        """初始化计划管理页面"""
        # 创建计划管理页面布局
        plan_layout = QVBoxLayout(self.plan_manager_page)
        plan_layout.setContentsMargins(0, 0, 0, 0)
        plan_layout.setSpacing(0)

        # 创建计划管理组件
        self.plan_manager_widget = PlanManager(self)
        plan_layout.addWidget(self.plan_manager_widget)

    def init_influencer_invitation_page(self):
        """初始化达人邀约页面"""
        from 达人邀约 import InfluencerInvitationUI

        # 创建达人邀约页面布局
        invitation_layout = QVBoxLayout(self.influencer_invitation_page)
        invitation_layout.setContentsMargins(0, 0, 0, 0)
        invitation_layout.setSpacing(0)

        # 创建达人邀约组件
        self.influencer_invitation_widget = InfluencerInvitationUI(
            main_window_ref=self,
            parent=self.influencer_invitation_page
        )
        invitation_layout.addWidget(self.influencer_invitation_widget)

    def init_message_page(self):
        """初始化通用消息页面，用于显示开发中的功能"""
        message_layout = QVBoxLayout(self.message_page)

        # 创建居中的内容容器
        content_widget = QWidget()
        content_widget.setStyleSheet("""
            background-color: white;
            border-radius: 12px;
        """)
        content_layout = QVBoxLayout(content_widget)

        # 添加图标
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        icon_path = get_resource_path(os.path.join("config", "imges", "logo.ico"))
        if os.path.exists(icon_path):
            pixmap = QIcon(icon_path).pixmap(QSize(100, 100))
            icon_label.setPixmap(pixmap)

        # 消息标签
        self.message_label = QLabel("功能开发中，敬请期待...")
        self.message_label.setStyleSheet("""
            font-family: 'Microsoft YaHei';
            font-size: 22px;
            color: #4a6bdf;
            font-weight: bold;
            margin: 20px;
        """)
        self.message_label.setAlignment(Qt.AlignCenter)

        # 添加到布局
        content_layout.addStretch(1)
        content_layout.addWidget(icon_label)
        content_layout.addWidget(self.message_label)
        content_layout.addStretch(1)

        # 设置内容容器的大小
        content_widget.setFixedSize(400, 300)

        # 将内容容器添加到主布局并居中
        message_layout.addStretch(1)
        message_layout.addWidget(content_widget, 0, Qt.AlignCenter)
        message_layout.addStretch(1)

    def show_message_page(self, message):
        """显示带有自定义消息的功能开发中页面"""
        # 更新消息文本
        self.message_label.setText(message)

        # 显示消息页面
        self.stacked_widget.setCurrentWidget(self.message_page)

        # 更新菜单按钮样式
        # 尝试从消息中提取菜单名称
        menu_name = message.split("功能")[0].strip() if "功能" in message else ""
        if menu_name:
            self.update_menu_styles(menu_name)

    def init_one_click_order_page(self):
        """初始化一键下单页面"""
        from 一键下单 import OneClickOrderWindow

        # 创建一键下单页面布局
        one_click_order_layout = QVBoxLayout(self.one_click_order_page)
        one_click_order_layout.setContentsMargins(0, 0, 0, 0)
        one_click_order_layout.setSpacing(0)

        # 创建一键下单组件
        self.one_click_order_widget = OneClickOrderWindow()
        one_click_order_widget = self.one_click_order_widget.centralWidget()
        one_click_order_layout.addWidget(one_click_order_widget)

    def show_one_click_order_page(self):
        """显示一键下单页面"""
        self.stacked_widget.setCurrentWidget(self.one_click_order_page)

        # 更新菜单按钮样式
        self.update_menu_styles("一键下单")

        # 设置窗口标题状态
        self.set_window_title_status("一键下单页面已准备就绪")

    def on_stats_button_clicked(self, button_text):
        """处理统计页面按钮点击事件"""
        print(f"主窗口处理统计按钮点击: {button_text}")

        # 如果有数据统计模块，先让它切换表格
        if hasattr(self, 'data_stats_widget'):
            success = self.data_stats_widget.switch_stats_table(button_text)
            if success:
                # 同步主窗口的属性
                self.current_stats_type = self.data_stats_widget.current_stats_type
                self.stats_table_widget = self.data_stats_widget.stats_table_widget
                self.stats_tables = self.data_stats_widget.stats_tables
                self.stats_buttons = self.data_stats_widget.stats_buttons
            else:
                print(f"数据统计模块切换表格失败: {button_text}")
                return
        else:
            # 兼容旧版本的处理方式
            # 更新所有按钮状态
            for text, btn in self.stats_buttons.items():
                btn.setChecked(text == button_text)

            # 更新当前统计类型和表格引用
            self.current_stats_type = button_text
            self.stats_table_widget = self.stats_tables[button_text]

            # 切换到对应的表格
            table_index = list(self.stats_tables.keys()).index(button_text)
            self.stats_stacked_widget.setCurrentIndex(table_index)

        # 根据报表类型启用或禁用"店铺分类"下拉框
        if hasattr(self, 'data_stats_widget'):
            if button_text == "店铺统计":
                self.data_stats_widget.set_shop_category_enabled(True)
                print(f"已启用店铺分类下拉框 (当前报表类型: {button_text})")
            else:
                # 其他报表类型，禁用并重置为"全部"
                self.data_stats_widget.set_shop_category_enabled(False)
                shop_combo = self.data_stats_widget.get_shop_category_combo()
                if shop_combo:
                    shop_combo.setCurrentText("全部")
                print(f"已禁用店铺分类下拉框 (当前报表类型: {button_text})")
        else:
            # 兼容旧版本 - 修复：使用正确的属性名
            if hasattr(self, 'category_combo'):
                if button_text == "店铺统计":
                    self.category_combo.setEnabled(True)
                    print(f"已启用店铺分类下拉框 (当前报表类型: {button_text})")
                else:
                    # 其他报表类型，禁用并重置为"全部"
                    self.category_combo.setEnabled(False)
                    self.category_combo.setCurrentText("全部")
                    print(f"已禁用店铺分类下拉框 (当前报表类型: {button_text})")
            else:
                print(f"警告：未找到店铺分类下拉框 (当前报表类型: {button_text})")

        # 根据报表类型设置列宽调整模式
        header = self.stats_table_widget.horizontalHeader()
        if self.current_stats_type == "每日报表":
            if header.count() > 1: # 确保至少有日期列
                print(f"on_stats_button_clicked: 为每日报表设置 日期列ResizeToContents, 其他列Stretch, StretchLastSection(False)")
                header.setSectionResizeMode(1, QHeaderView.ResizeToContents) # 日期列根据内容调整
                for i in range(header.count()):
                    if i != 1: # 非日期列
                        header.setSectionResizeMode(i, QHeaderView.Stretch)
                header.setStretchLastSection(False) # <--- 确保其他列的Stretch生效
            else: # 如果列不够，则全部Stretch
                header.setSectionResizeMode(QHeaderView.Stretch)
        else:
            # 其他报表恢复为可交互调整，并允许拉伸最后一列（如果之前是这样设计的）
            header.setSectionResizeMode(QHeaderView.Interactive)
            header.setStretchLastSection(True) # <--- 其他报表可能依赖这个行为

        # 首先清空并显示表头
        self.show_stats_table_headers() # 这会清空表格并设置基本表头

        # 清空底部总计信息
        if hasattr(self, 'data_stats_widget'):
            self.data_stats_widget.update_stats_summary("请查询数据以显示总计信息")
        elif hasattr(self, 'stats_summary_label'):
            self.stats_summary_label.setText("请查询数据以显示总计信息")

        # 尝试从缓存加载数据
        if self.load_stats_from_cache(self.current_stats_type, self.stats_table_widget):
            self.set_window_title_status(f"{self.current_stats_type} - 已加载上次缓存")
            # 更新底部总计信息
            if hasattr(self, 'data_stats_widget') and self.data_stats_widget:
                self.data_stats_widget.update_stats_summary_from_table(self.stats_table_widget, self.current_stats_type)
            else:
                # 如果数据统计模块不存在，使用简单的提示
                print("数据统计模块不存在，无法更新底部总计信息")

            # 如果是店铺统计，确认店铺详情提示功能状态
            if self.current_stats_type == "店铺统计":
                # 数据统计模块在创建表格时已自动设置店铺详情提示功能
                if hasattr(self, 'data_stats_widget') and self.data_stats_widget:
                    if hasattr(self.data_stats_widget, 'shop_detail_tooltip') and self.data_stats_widget.shop_detail_tooltip:
                        print("从缓存加载店铺统计数据后，店铺详情提示功能已启用")
                    else:
                        print("从缓存加载店铺统计数据后，店铺详情提示功能未启用")
                else:
                    print("数据统计模块不存在，店铺详情提示功能不可用")

            # 如果是每日报表，并且从缓存加载成功，再次确保列宽设置正确
            if self.current_stats_type == "每日报表":
                header = self.stats_table_widget.horizontalHeader()
                if header.count() > 1:
                    print(f"on_stats_button_clicked (cache loaded): 为每日报表再次设置 日期列ResizeToContents, 其他列Stretch, StretchLastSection(False)")
                    header.setSectionResizeMode(1, QHeaderView.ResizeToContents) # 日期列根据内容调整
                    for i in range(header.count()):
                        if i != 1: # 非日期列
                            header.setSectionResizeMode(i, QHeaderView.Stretch)
                    header.setStretchLastSection(False) # <--- 确保
                else: # 如果列不够，则全部Stretch
                    header.setSectionResizeMode(QHeaderView.Stretch)
                    header.setStretchLastSection(False) # <--- 确保

            # 数据加载完成后启用排序功能并连接信号
            self.stats_table_widget.setSortingEnabled(True)

            # 断开旧连接以避免重复连接
            try:
                self.stats_table_widget.horizontalHeader().sortIndicatorChanged.disconnect()
            except TypeError:
                # 如果没有连接，会抛出异常，但可以忽略
                pass

            # 重新连接排序信号，确保排序后重新生成序号
            self.stats_table_widget.horizontalHeader().sortIndicatorChanged.connect(
                lambda logicalIndex, order, tbl=self.stats_table_widget:
                self.on_stats_table_sort_changed(logicalIndex, order, tbl)
            )
        else:
            # 如果没有缓存，或者缓存加载失败
            # _setup_empty_stats_table 内部已经处理了每日报表的Stretch逻辑，所以这里不需要额外代码
            self._setup_empty_stats_table(self.current_stats_type, self.stats_table_widget)
            self.set_window_title_status(f"请为 {self.current_stats_type} 选择日期并查询")

    def on_stats_quick_filter_changed(self, index):
        """处理数据统计页面快捷筛选下拉框选项变化"""
        # 从数据统计模块获取快捷筛选下拉框
        if hasattr(self, 'data_stats_widget') and hasattr(self.data_stats_widget, 'stats_quick_filter_combo'):
            selected_option = self.data_stats_widget.stats_quick_filter_combo.itemText(index)
        else:
            print("错误：无法找到快捷筛选下拉框")
            return

        print(f"快捷筛选选择变更为: {selected_option}")

        today = QDate.currentDate()
        # 统一使用 QDateTime 初始化，包含时间部分，即使时间是00:00:00
        start_date_dt = QDateTime(today, QTime(0, 0, 0))
        end_date_dt = QDateTime(today, QTime(0, 0, 0))

        # 确保日期选择器始终保持启用状态，用户可以自由选择时间
        if hasattr(self, 'data_stats_widget') and hasattr(self.data_stats_widget, 'stats_start_date'):
            self.data_stats_widget.stats_start_date.setEnabled(True)
            self.data_stats_widget.stats_end_date.setEnabled(True)
            print(f"快捷筛选选择了: {selected_option}，日期选择器保持启用状态")

        # 处理"快速查询"和"自定义"选项 - 不自动设置日期，保持当前设置
        if selected_option in ["快速查询", "自定义"]:
            print(f"{selected_option}：保持当前日期设置，不自动触发查询")
            return

        if selected_option == "今日":
            start_date_dt = QDateTime(today, QTime(0, 0, 0))
            end_date_dt = QDateTime(today, QTime(0, 0, 0))
        elif selected_option == "昨日":
            yesterday = today.addDays(-1)
            start_date_dt = QDateTime(yesterday, QTime(0, 0, 0))
            end_date_dt = QDateTime(yesterday, QTime(0, 0, 0))
        elif selected_option == "本月":
            start_date_dt = QDateTime(today.addDays(-today.day() + 1), QTime(0, 0, 0))
            end_date_dt = QDateTime(today, QTime(0, 0, 0))
        elif selected_option == "上月":
            last_month_end = today.addDays(-today.day())
            last_month_start = last_month_end.addDays(-last_month_end.day() + 1)
            start_date_dt = QDateTime(last_month_start, QTime(0, 0, 0))
            end_date_dt = QDateTime(last_month_end, QTime(0, 0, 0))
        elif selected_option == "最近3个月":
            # Correct logic: from today minus 3 months plus one day, to today
            start_date_dt = QDateTime(today.addMonths(-3).addDays(1), QTime(0,0,0))
            end_date_dt = QDateTime(today, QTime(0,0,0))
        elif selected_option == "最近6个月":
            start_date_dt = QDateTime(today.addMonths(-6).addDays(1), QTime(0,0,0))
            end_date_dt = QDateTime(today, QTime(0,0,0))
        elif selected_option == "今年":
            start_date_dt = QDateTime(QDate(today.year(), 1, 1), QTime(0, 0, 0))
            end_date_dt = QDateTime(today, QTime(0, 0, 0))

        if hasattr(self, 'data_stats_widget') and hasattr(self.data_stats_widget, 'stats_start_date'):
            self.data_stats_widget.stats_start_date.setDateTime(start_date_dt)
            self.data_stats_widget.stats_end_date.setDateTime(end_date_dt)

        # 对于具体的快捷筛选选项（今日、昨日等），自动设置日期并触发查询
        if selected_option not in ["自定义", "快速查询"]:
            print(f"快捷筛选选择了: {selected_option}，自动设置日期并触发查询...") # 调试信息
            self.on_stats_query()

    def on_stats_query(self):
        """处理统计页面查询按钮点击事件"""
        print("--- on_stats_query: 开始处理查询 ---") # 调试信息

        try:
            # 获取当前选中的按钮文本
            current_button = None
            # 修复这里的缩进
            for text, btn in self.stats_buttons.items():
                if btn.isChecked():
                    current_button = text
                    break

            print(f"on_stats_query: 当前选中的统计类型: {current_button}") # 调试信息

            # 获取日期范围 (从数据统计模块获取)
            if hasattr(self, 'data_stats_widget'):
                # 获取完整的日期时间，然后格式化为带时间的字符串
                start_datetime = self.data_stats_widget.get_stats_start_date()
                end_datetime = self.data_stats_widget.get_stats_end_date()

                # 确保开始时间是当天的00:00:00，结束时间是当天的23:59:59
                start_datetime = QDateTime(start_datetime.date(), QTime(0, 0, 0))
                end_datetime = QDateTime(end_datetime.date(), QTime(23, 59, 59))

                # 格式化为API需要的格式
                start_date = start_datetime.toString("yyyy-MM-dd HH:mm:ss")
                end_date = end_datetime.toString("yyyy-MM-dd HH:mm:ss")

                selected_category = self.data_stats_widget.shop_category_combo.currentText()
                print(f"查询时间范围: {start_date} 至 {end_date}")
            else:
                print("错误: 数据统计模块不存在")
                return

            # 检查店铺分类下拉框的状态、并根据当前报表类型进行处理
            # 店铺分类下拉框只对店铺统计生效
            if current_button == "店铺统计":
                self.data_stats_widget.set_shop_category_enabled(True)
                print(f"查询筛选：店铺统计 - 选择分类: '{selected_category}'")
            else:
                # 其他报表类型，禁用店铺分类下拉框并重置为"全部"
                self.data_stats_widget.set_shop_category_enabled(False)
                shop_combo = self.data_stats_widget.get_shop_category_combo()
                if shop_combo:
                    shop_combo.setCurrentText("全部")
                print(f"查询筛选：{current_button} - 店铺分类筛选不适用")

            # 清空表格，不显示加载提示
            self.stats_table_widget.clear()
            self.stats_table_widget.setRowCount(0)
            self.stats_table_widget.setColumnCount(0)

            # if hasattr(self, 'stats_status_label'): # 旧代码
            if self.stacked_widget.currentWidget() == self.data_stats_page:
                # self.stats_status_label.setText(f"正在加载数据: {start_date} 至 {end_date}") # 旧代码
                self.set_window_title_status(f"正在加载 {current_button}...")

            QApplication.processEvents() # 确保UI刷新，显示加载中

            # 根据所选按钮执行不同的加载功能
            if current_button == "每日报表":
                # 检查是否有正在运行的加载线程
                try:
                    if hasattr(self, 'daily_stats_thread') and self.daily_stats_thread:
                        try:
                            if self.daily_stats_thread.isRunning():
                                QMessageBox.information(self, "操作提示", "每日报表数据正在加载中，请稍候。")
                                return
                        except RuntimeError:
                            print("每日报表线程对象已被删除，重置线程对象")
                            self.daily_stats_thread = None
                except Exception as e:
                    print(f"检查每日报表线程状态时出错: {str(e)}")
                    self.daily_stats_thread = None

                # 设置表头
                self.stats_table_widget.setColumnCount(18)
                self.stats_table_widget.setHorizontalHeaderLabels(["序号", "日期", "订单数", "有效订单", "客单价", "退货率", "实收款", "预收款", "收的运费", "采购金额", "采购单价", "支出运费", "利润", "实际利润", "毛利率", "净利率", "实际利率", "包邮率"])
                self.stats_table_widget.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch) # 新增：列平铺可视区域

                # 清空表格内容
                self.stats_table_widget.setRowCount(0)

                # 在窗口标题中显示状态
                self.set_window_title_status(f"正在从服务器加载每日报表数据，请稍候...")
                QApplication.processEvents()

                # 使用线程加载每日报表数据
                self.daily_stats_thread = LoadDailyStatsThread(
                    server_url=get_configured_server_url(),
                    start_date=start_date,
                    end_date=end_date,
                    parent=self,
                    data_stats_widget=getattr(self, 'data_stats_widget', None)
                )
                # 连接到数据统计模块的回调方法
                if hasattr(self, 'data_stats_widget') and self.data_stats_widget:
                    self.daily_stats_thread.data_loaded.connect(self.data_stats_widget.on_daily_stats_data_loaded)
                else:
                    # 如果数据统计模块不存在，连接到主窗口的方法（向后兼容）
                    self.daily_stats_thread.data_loaded.connect(self.on_daily_stats_data_loaded)

                # 添加清理函数
                def cleanup_daily_stats_thread():
                    print("每日报表线程已完成，正在清理线程对象")
                    if hasattr(self, 'daily_stats_thread') and self.daily_stats_thread:
                        self.daily_stats_thread.deleteLater()
                        self.daily_stats_thread = None

                self.daily_stats_thread.finished.connect(cleanup_daily_stats_thread)
                self.daily_stats_thread.start()

                # 保存当前查询参数，用于线程完成后的处理
                self.current_daily_stats_query = {
                    'button': current_button,
                    'start_date': start_date,
                    'end_date': end_date
                }
            elif current_button == "每月报表":
                # 设置表头
                self.stats_table_widget.setColumnCount(18)
                self.stats_table_widget.setHorizontalHeaderLabels(["序号", "月份", "订单数", "有效订单", "客单价", "退货率", "实收款", "预收款", "收的运费", "采购金额", "采购单价", "支出运费", "利润", "实际利润", "毛利率", "净利率", "实际利率", "包邮率"])

                # 清空表格内容
                self.stats_table_widget.setRowCount(0)

                # 在窗口标题中显示状态
                self.set_window_title_status(f"正在从服务器加载每月报表数据，请稍候...")
                QApplication.processEvents()

                # 使用线程加载每月报表数据
                self.monthly_stats_thread = LoadMonthlyStatsThread(
                    server_url=get_configured_server_url(),
                    start_date=start_date,
                    end_date=end_date,
                    parent=self,
                    data_stats_widget=getattr(self, 'data_stats_widget', None)
                )
                # 连接到数据统计模块的回调方法
                if hasattr(self, 'data_stats_widget') and self.data_stats_widget:
                    self.monthly_stats_thread.data_loaded.connect(self.data_stats_widget.on_monthly_stats_data_loaded)
                else:
                    # 如果数据统计模块不存在，连接到主窗口的方法（向后兼容）
                    self.monthly_stats_thread.data_loaded.connect(self.on_monthly_stats_data_loaded)

                # 添加清理函数
                def cleanup_monthly_stats_thread():
                    print("每月报表线程已完成，正在清理线程对象")
                    if hasattr(self, 'monthly_stats_thread') and self.monthly_stats_thread:
                        self.monthly_stats_thread.deleteLater()
                        self.monthly_stats_thread = None

                self.monthly_stats_thread.finished.connect(cleanup_monthly_stats_thread)
                self.monthly_stats_thread.start()

                # 保存当前查询参数，用于线程完成后的处理
                self.current_monthly_stats_query = {
                    'button': current_button,
                    'start_date': start_date,
                    'end_date': end_date
                }
            elif current_button == "年度报表":
                # 设置表头
                self.stats_table_widget.setColumnCount(18)
                self.stats_table_widget.setHorizontalHeaderLabels(["序号", "年份", "订单数", "有效订单", "客单价", "退货率", "实收款", "预收款", "收的运费", "采购金额", "采购单价", "支出运费", "利润", "实际利润", "毛利率", "净利率", "实际利率", "包邮率"])

                # 清空表格内容
                self.stats_table_widget.setRowCount(0)

                # 在窗口标题中显示状态
                self.set_window_title_status(f"正在从服务器加载年度报表数据，请稍候...")
                QApplication.processEvents()

                # 使用线程加载年度报表数据
                self.yearly_stats_thread = LoadYearlyStatsThread(
                    server_url=get_configured_server_url(),
                    start_date=start_date,
                    end_date=end_date,
                    parent=self,
                    data_stats_widget=getattr(self, 'data_stats_widget', None)
                )
                # 连接到数据统计模块的回调方法
                if hasattr(self, 'data_stats_widget') and self.data_stats_widget:
                    self.yearly_stats_thread.data_loaded.connect(self.data_stats_widget.on_yearly_stats_data_loaded)
                else:
                    # 如果数据统计模块不存在，连接到主窗口的方法（向后兼容）
                    self.yearly_stats_thread.data_loaded.connect(self.on_yearly_stats_data_loaded)

                # 添加清理函数
                def cleanup_yearly_stats_thread():
                    print("年度报表线程已完成，正在清理线程对象")
                    if hasattr(self, 'yearly_stats_thread') and self.yearly_stats_thread:
                        self.yearly_stats_thread.deleteLater()
                        self.yearly_stats_thread = None

                self.yearly_stats_thread.finished.connect(cleanup_yearly_stats_thread)
                self.yearly_stats_thread.start()

                # 保存当前查询参数，用于线程完成后的处理
                self.current_yearly_stats_query = {
                    'button': current_button,
                    'start_date': start_date,
                    'end_date': end_date
                }
            elif current_button == "店铺统计":
                # 检查是否有正在运行的加载线程
                try:
                    if hasattr(self, 'shop_stats_thread') and self.shop_stats_thread:
                        try:
                            if self.shop_stats_thread.isRunning():
                                QMessageBox.information(self, "操作提示", "店铺统计数据正在加载中，请稍候。")
                                return
                        except RuntimeError:
                            print("店铺统计线程对象已被删除，重置线程对象")
                            self.shop_stats_thread = None
                except Exception as e:
                    print(f"检查店铺统计线程状态时出错: {str(e)}")
                    self.shop_stats_thread = None

                # 设置表头
                self.stats_table_widget.setColumnCount(18)
                self.stats_table_widget.setHorizontalHeaderLabels([
                    "序号", "日期", "店铺", "订单数", "有效订单", "客单价", "退货率", "实收款", "预收款",
                    "收的运费", "采购金额", "采购单价", "支出运费", "利润", "实际利润",
                    "毛利率", "净利率", "实际利率"
                ])

                # 清空表格内容
                self.stats_table_widget.setRowCount(0)

                # 在窗口标题中显示状态
                self.set_window_title_status(f"正在从服务器加载店铺统计数据，请稍候...")
                QApplication.processEvents()

                # 使用线程加载店铺统计数据
                self.shop_stats_thread = LoadShopStatsThread(
                    server_url=get_configured_server_url(),
                    start_date=start_date,
                    end_date=end_date,
                    parent=self,
                    data_stats_widget=getattr(self, 'data_stats_widget', None)
                )
                # 连接到数据统计模块的回调方法
                if hasattr(self, 'data_stats_widget') and self.data_stats_widget:
                    self.shop_stats_thread.data_loaded.connect(self.data_stats_widget.on_shop_stats_data_loaded)
                else:
                    # 如果数据统计模块不存在，连接到主窗口的方法（向后兼容）
                    self.shop_stats_thread.data_loaded.connect(self.on_shop_stats_data_loaded)

                # 添加清理函数
                def cleanup_shop_stats_thread():
                    print("店铺统计线程已完成，正在清理线程对象")
                    if hasattr(self, 'shop_stats_thread') and self.shop_stats_thread:
                        self.shop_stats_thread.deleteLater()
                        self.shop_stats_thread = None

                self.shop_stats_thread.finished.connect(cleanup_shop_stats_thread)
                self.shop_stats_thread.start()

                # 保存当前查询参数，用于线程完成后的处理
                self.current_shop_stats_query = {
                    'button': current_button,
                    'start_date': start_date,
                    'end_date': end_date
                }
            elif current_button == "上家统计":
                # 检查是否有正在运行的加载线程
                try:
                    if hasattr(self, 'supplier_stats_thread') and self.supplier_stats_thread:
                        try:
                            if self.supplier_stats_thread.isRunning():
                                QMessageBox.information(self, "操作提示", "上家统计数据正在加载中，请稍候。")
                                return
                        except RuntimeError:
                            print("线程对象已被删除，重置线程对象")
                            self.supplier_stats_thread = None
                except Exception as e:
                    print(f"检查线程状态时出错: {str(e)}")
                    self.supplier_stats_thread = None

                # 上家统计始终从服务器加载，不使用缓存
                print(f"上家统计：始终从服务器加载数据，不使用缓存")

                # 获取上家统计表格
                table_widget_supplier = self.stats_tables.get(current_button)
                if not table_widget_supplier:
                    print(f"错误：在on_stats_query中找不到 {current_button} 的表格")
                    self.set_window_title_status(f"{current_button} - 表格丢失")
                    return

                self._setup_empty_stats_table(current_button, table_widget_supplier)
                self.set_window_title_status(f"正在从服务器加载上家统计数据，请稍候...")
                QApplication.processEvents()

                self.supplier_stats_thread = LoadSupplierStatsThread(
                    server_url=get_configured_server_url(),
                    start_date=start_date,
                    end_date=end_date,
                    parent=self
                )

                # 连接到数据统计模块的回调方法
                if hasattr(self, 'data_stats_widget') and self.data_stats_widget:
                    # 上家统计使用特殊的信号，需要适配
                    self.supplier_stats_thread.data_loaded.connect(self.on_supplier_stats_data_loaded)
                else:
                    # 如果数据统计模块不存在，连接到主窗口的方法（向后兼容）
                    self.supplier_stats_thread.data_loaded.connect(self.on_supplier_stats_data_loaded)

                def cleanup_thread():
                    print("上家统计线程已完成，正在清理线程对象")
                    if hasattr(self, 'supplier_stats_thread') and self.supplier_stats_thread:
                        self.supplier_stats_thread.deleteLater()
                        self.supplier_stats_thread = None

                self.supplier_stats_thread.finished.connect(cleanup_thread)
                self.supplier_stats_thread.start()

                # 保存当前查询参数，用于线程完成后的处理
                self.current_supplier_stats_query = {
                    'button': current_button,
                    'start_date': start_date,
                    'end_date': end_date
                }

        except Exception as e:
            # 显示错误信息
            self.stats_table_widget.setRowCount(1)
            self.stats_table_widget.setColumnCount(1)
            self.stats_table_widget.setHorizontalHeaderLabels(["错误"])
            self.stats_table_widget.setItem(0, 0, QTableWidgetItem(f"加载数据时出错: {str(e)}"))

            # if hasattr(self, 'stats_status_label'): # 旧代码
            if self.stacked_widget.currentWidget() == self.data_stats_page:
                # self.stats_status_label.setText(f"加载失败: {str(e)}") # 旧代码
                self.set_window_title_status(f"{current_button} 加载出错: {str(e)[:100]}") # 截断错误信息以防过长

            print(f"on_stats_query 错误: {str(e)}")
            print(traceback.format_exc())



    def on_stats_date_changed(self, _):
        """统计页面日期变更处理"""
        # 暂时不自动刷新数据，由用户点击查询按钮刷新
        pass

    def show_stats_table_headers(self):
        """清空统计表格内容并更新状态，不再设置表头。"""
        try:
            # 确保表格控件存在
            if not hasattr(self, 'stats_table_widget') or not self.stats_table_widget:
                print("错误: show_stats_table_headers - 表格控件不存在")
                return

            # # 设置表格表头 - 不再由此函数负责
            # headers = ["序号", "日期", "实收款", "客单价", "订单数", "收的运费", "退货率", "采购金额", "采购单价", "支出运费", "利润", "平均利润", "利润率", "实际利润", "实际利润率", "预收款", "包邮率"]
            # self.stats_table_widget.setColumnCount(len(headers))
            # self.stats_table_widget.setHorizontalHeaderLabels(headers)

            # 清空表格内容
            print("show_stats_table_headers: 清空表格内容")
            self.stats_table_widget.setRowCount(0)
            # 也可以考虑清除表头，但 on_stats_query 会重新设置
            # self.stats_table_widget.setColumnCount(0)

            # 重新启用排序功能并连接排序信号
            self.stats_table_widget.setSortingEnabled(True)

            # 断开旧连接以避免重复连接
            try:
                self.stats_table_widget.horizontalHeader().sortIndicatorChanged.disconnect()
            except:
                # 如果没有连接，会抛出异常，但可以忽略
                pass

            # 重新连接排序信号
            self.stats_table_widget.horizontalHeader().sortIndicatorChanged.connect(
                lambda logicalIndex, order, tbl=self.stats_table_widget:
                self.on_stats_table_sort_changed(logicalIndex, order, tbl)
            )

            # # 调整列宽 - 不需要，因为没有列了
            # for col in range(len(headers)):
            #     self.stats_table_widget.horizontalHeader().setSectionResizeMode(col, QHeaderView.ResizeToContents)

            # 确保表格显示并调整大小
            self.stats_table_widget.show()
            self.stats_table_widget.update()
            QApplication.processEvents()

            # 更新状态标签
            if hasattr(self, 'stats_status_label'):
                self.stats_status_label.setText("请选择报表类型和日期范围，然后点击查询") # 更新提示

            if hasattr(self, 'order_stats_label'):
                self.order_stats_label.setText("") # 清空统计信息

        except Exception as e:
            print(f"清空表格时发生错误: {str(e)}")
            if hasattr(self, 'stats_status_label'):
                self.stats_status_label.setText(f"错误: {str(e)}")

    def extract_payment_amount(self, payment_text):
        """从付款文本中提取金额（去掉括号部分）"""
        try:
            if not payment_text:
                return 0

            # 如果是数字，直接返回
            if isinstance(payment_text, (int, float)):
                return float(payment_text)

            # 字符串处理：提取括号前的部分
            if '(' in payment_text:
                payment_text = payment_text.split('(')[0].strip()

            # 转换为浮点数
            return float(payment_text)
        except:
            return 0

    def load_daily_report(self):
        """加载每日报表数据 - 统计每日订单数量和实收款"""
        try:
            # 确保表格控件存在
            if not hasattr(self, 'stats_table_widget') or not self.stats_table_widget:
                print("错误: 表格控件不存在")
                return

            # 获取日期范围
            start_date = self.stats_start_date.dateTime().toString("yyyy-MM-dd 00:00:00")
            end_date = self.stats_end_date.dateTime().toString("yyyy-MM-dd 23:59:59")

            # 更新状态标签
            if hasattr(self, 'stats_status_label'):
                self.stats_status_label.setText(f"正在加载数据: {start_date} 至 {end_date}")

            # 显示正在加载提示
            self.stats_table_widget.setRowCount(0)
            self.stats_table_widget.setColumnCount(1)
            self.stats_table_widget.setHorizontalHeaderLabels(["正在加载数据，请稍候..."])
            self.stats_table_widget.show()  # 确保表格显示
            QApplication.processEvents()  # 立即更新UI

            # 准备API请求，从下单记录表获取数据
            server_url = self.get_current_server_url()
            print(f"使用服务器URL: {server_url}")

            # 使用标准的database接口替代data_statistics接口
            api_url = f"{server_url}/query_by_time"

            # 构建查询参数 - 更简单的API请求格式
            params = {
                "table": "下单记录表",
                "time_field": "下单时间",
                "start_time": start_date,
                "end_time": end_date
            }

            print(f"API请求参数: {params}")

            # 发送API请求
            try:
                print(f"正在发送请求到: {api_url}")
                response = requests.get(api_url, params=params, timeout=30)

                # 打印响应状态和前几个字节的响应内容，以便调试
                print(f"API响应状态: {response.status_code}")
                print(f"API响应前200个字符: {response.text[:200] if response.text else '无响应内容'}")

                response.raise_for_status()  # 如果HTTP请求返回了不成功的状态码，将引发HTTPError异常

                # 解析返回的JSON数据
                try:
                    result = response.json()
                    print(f"成功解析JSON响应，数据类型: {type(result)}")
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {e}, 响应内容: {response.text[:500]}")
                    raise ValueError(f"无法解析服务器响应为JSON: {e}")

                if not result or "data" not in result or not result["data"]:
                    # 没有数据，清空表格显示空结果
                    self.stats_table_widget.setRowCount(0)
                    self.stats_table_widget.setColumnCount(len(["日期", "订单数", "实收款"]))
                    self.stats_table_widget.setHorizontalHeaderLabels(["日期", "订单数", "实收款"])

                    if hasattr(self, 'stats_status_label'):
                        self.stats_status_label.setText(f"无数据: {start_date} 至 {end_date}")

                    return

                # 处理返回的数据
                data = result["data"]
                records_count = len(data)
                print(f"获取到 {records_count} 条记录")

                # 按日期分组数据，统计每日订单数量和实收款
                date_dict = {}

                for record in data:
                    # 提取日期
                    order_date_str = record.get("下单时间", "")
                    if not order_date_str:
                        continue

                    # 只保留日期部分
                    if " " in order_date_str:
                        order_date = order_date_str.split(" ")[0]
                    else:
                        order_date = order_date_str[:10]  # 假设格式为yyyy-MM-dd

                    # 提取付款金额
                    payment_text = record.get("付款", "0")
                    try:
                        # 提取括号前的数字部分
                        if isinstance(payment_text, str) and "(" in payment_text:
                            payment_text = payment_text.split("(")[0].strip()
                        payment_amount = float(payment_text) if payment_text else 0
                    except (ValueError, TypeError):
                        print(f"警告: 无法将付款值 '{payment_text}' 转换为数字，使用0替代")
                        payment_amount = 0

                    # 初始化该日期的统计数据
                    if order_date not in date_dict:
                        date_dict[order_date] = {"count": 0, "revenue": 0}

                    # 更新该日期的统计数据
                    date_dict[order_date]["count"] += 1
                    date_dict[order_date]["revenue"] += payment_amount

                # 将日期字典转换为排序后的列表
                sorted_dates = sorted(date_dict.keys())

                # 准备表格数据
                headers = ["序号", "日期", "订单数", "实收款"]
                self.stats_table_widget.setColumnCount(len(headers))
                self.stats_table_widget.setHorizontalHeaderLabels(headers)

                # 设置表格行数
                rows_count = len(sorted_dates)
                self.stats_table_widget.setRowCount(rows_count)

                # 计算总订单数和总实收款
                total_orders = sum(data["count"] for data in date_dict.values())
                total_revenue = sum(data["revenue"] for data in date_dict.values())

                # 填充表格数据
                for row, date in enumerate(sorted_dates):
                    stats = date_dict[date]
                    order_count = stats["count"]
                    revenue = stats["revenue"]

                    # 设置单元格内容
                    self.stats_table_widget.setItem(row, 0, QTableWidgetItem(str(row + 1)))  # 序号
                    self.stats_table_widget.setItem(row, 1, QTableWidgetItem(date))  # 日期
                    self.stats_table_widget.setItem(row, 2, QTableWidgetItem(str(order_count)))  # 订单数
                    self.stats_table_widget.setItem(row, 3, QTableWidgetItem(f"{revenue:.2f}"))  # 实收款

                    # 设置单元格对齐方式
                    self.stats_table_widget.item(row, 0).setTextAlignment(Qt.AlignCenter)
                    self.stats_table_widget.item(row, 1).setTextAlignment(Qt.AlignCenter)
                    self.stats_table_widget.item(row, 2).setTextAlignment(Qt.AlignCenter)
                    self.stats_table_widget.item(row, 3).setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)

                # 添加总计行
                if rows_count > 0:
                    self.stats_table_widget.setRowCount(rows_count + 1)
                    total_row = rows_count

                    # 设置总计行
                    self.stats_table_widget.setItem(total_row, 0, QTableWidgetItem(""))
                    self.stats_table_widget.setItem(total_row, 1, QTableWidgetItem("总计"))
                    self.stats_table_widget.setItem(total_row, 2, QTableWidgetItem(str(total_orders)))
                    self.stats_table_widget.setItem(total_row, 3, QTableWidgetItem(f"{total_revenue:.2f}"))

                    # 设置总计行样式
                    for col in range(4):
                        item = self.stats_table_widget.item(total_row, col)
                        if item:
                            font = item.font()
                            font.setBold(True)
                            item.setFont(font)
                            item.setBackground(QColor(240, 240, 240))

                    # 设置总计行对齐方式
                    self.stats_table_widget.item(total_row, 1).setTextAlignment(Qt.AlignCenter)
                    self.stats_table_widget.item(total_row, 2).setTextAlignment(Qt.AlignCenter)
                    self.stats_table_widget.item(total_row, 3).setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)

                # 调整列宽
                for col in range(len(headers)):
                    self.stats_table_widget.horizontalHeader().setSectionResizeMode(col, QHeaderView.ResizeToContents)

                # 更新状态信息
                if hasattr(self, 'stats_status_label'):
                    self.stats_status_label.setText(f"加载完成: {start_date} 至 {end_date}")

                if hasattr(self, 'order_stats_label'):
                    self.order_stats_label.setText(f"总订单数: {total_orders} | 总实收款: {total_revenue:.2f} | 处理记录数: {records_count}")

            except requests.exceptions.RequestException as e:
                # 处理API请求错误
                error_msg = f"连接服务器失败: {str(e)}"
                print(error_msg)
                if hasattr(self, 'stats_status_label'):
                    self.stats_status_label.setText(error_msg)

                # 显示错误信息
                self.stats_table_widget.setRowCount(1)
                self.stats_table_widget.setColumnCount(1)
                self.stats_table_widget.setHorizontalHeaderLabels(["加载失败"])
                self.stats_table_widget.setItem(0, 0, QTableWidgetItem(error_msg))

        except Exception as e:
            print(f"加载报表数据时出错: {str(e)}")
            traceback_info = traceback.format_exc()
            print(f"详细错误信息: {traceback_info}")

            if hasattr(self, 'stats_status_label'):
                self.stats_status_label.setText(f"加载失败: {str(e)}")

            # 显示错误信息到表格
            self.stats_table_widget.setRowCount(1)
            self.stats_table_widget.setColumnCount(1)
            self.stats_table_widget.setHorizontalHeaderLabels(["加载失败"])
            self.stats_table_widget.setItem(0, 0, QTableWidgetItem(f"错误: {str(e)}"))

    def load_monthly_report(self):
        """加载每月报表数据"""
        self.show_stats_table_headers()

    def load_shop_stats(self):
        """加载店铺统计数据"""
        # 获取当前选择的日期范围
        start_date = self.stats_start_date.date().toString("yyyy-MM-dd")
        end_date = self.stats_end_date.date().toString("yyyy-MM-dd")

        # 调用新的shop_statistics方法
        self.load_shop_statistics(start_date, end_date)

    def load_supplier_stats(self):
        """加载上家统计数据"""
        self.show_stats_table_headers()

    def show_data_stats_page(self):
        """显示数据统计页面"""
        self.stacked_widget.setCurrentWidget(self.data_stats_page)

        # 更新菜单按钮样式
        self.update_menu_styles("数据统计")

        # 确保服务器URL已设置
        print(f"数据统计页面：当前服务器URL: {self.server_url}")

        # 更新状态标签 - 保持初始提示
        self.set_window_title_status("") # 切换到数据统计页面时，清空标题栏状态

        # 确保数据统计模块的属性与主窗口同步
        if hasattr(self, 'data_stats_widget'):
            # 同步当前统计类型和表格引用
            self.current_stats_type = self.data_stats_widget.current_stats_type
            self.stats_table_widget = self.data_stats_widget.stats_table_widget
            self.stats_tables = self.data_stats_widget.stats_tables
            self.stats_buttons = self.data_stats_widget.stats_buttons

    def show_plan_manager_page(self):
        """显示计划管理页面"""
        self.stacked_widget.setCurrentWidget(self.plan_manager_page)

        # 更新菜单按钮样式
        self.update_menu_styles("计划管理")

    def show_influencer_invitation_page(self):
        """显示达人邀约页面"""
        self.stacked_widget.setCurrentWidget(self.influencer_invitation_page)

        # 更新菜单按钮样式
        self.update_menu_styles("快手邀约")

        # 设置窗口标题状态
        self.set_window_title_status("达人邀约页面已准备就绪")

    def save_server_config(self):
        """保存服务器配置到配置文件"""
        try:
            # 先读取完整的配置文件
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                config = {}

            # 更新服务器配置
            config["server"] = self.server_config

            # 保存回配置文件
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)

            return True
        except Exception as e:
            print(f"保存服务器配置失败: {str(e)}")
            return False



    def get_server_config(self):
        # 此方法现在变得多余，因为 server_config 不再是主要的配置来源
        # 但为了防止其他地方（如果存在）调用它而出错，可以返回一个包含当前URL的字典
        return {
             "current": "custom", # 或其他合适的标签
             "servers": {
                 "custom": {
                     "url": get_configured_server_url(), # 确保总是获取最新的
                     "description": "通过config.json配置"
                 }
             }
        }














    def on_supplier_stats_data_loaded(self, supplier_daily_stats, sorted_keys, start_date, end_date, success, error_message):
        """处理上家统计数据加载完成的信号"""
        current_report_type = "上家统计"
        table_widget = self.stats_tables.get(current_report_type)
        print(f"on_supplier_stats_data_loaded: Received data. Success: {success}. Stats count: {len(supplier_daily_stats) if supplier_daily_stats else 0}")
        print(f"从服务器加载上家统计数据: {start_date} 至 {end_date}")

        try:
            if not table_widget:
                print(f"错误: {current_report_type} 表格未找到。")
                self.set_window_title_status(f"{current_report_type} - 表格控件丢失")
                return

            if not success:
                print(f"上家统计加载失败: {error_message}")
                self.set_window_title_status(f"{current_report_type} - 加载失败: {error_message[:100]}")
                self._setup_empty_stats_table(current_report_type, table_widget) # Clear and set headers
                table_widget.setRowCount(1)
                error_item = QTableWidgetItem(f"加载数据失败: {error_message}")
                error_item.setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)
                table_widget.setItem(0, 0, error_item)
                if table_widget.columnCount() > 0:
                    table_widget.setSpan(0, 0, 1, table_widget.columnCount())
                QApplication.processEvents()
                return

            if not supplier_daily_stats or not sorted_keys:
                self.set_window_title_status(f"{current_report_type} ({start_date} 至 {end_date}) - 无数据")
                self._setup_empty_stats_table(current_report_type, table_widget)
                print(f"{current_report_type} ({start_date} 至 {end_date}) - 查询成功但无数据返回")
                QApplication.processEvents()
                return

            print(f"--- on_supplier_stats_data_loaded: 开始填充 {current_report_type} 表格 ({len(sorted_keys)} 条目) ---")

            expected_headers = ["序号", "日期", "上家名称", "订单数", "有效订单", "客单价", "退货率", "实收款", "预收款", "收的运费", "采购金额", "采购单价", "支出运费", "利润", "实际利润", "毛利率", "净利率", "实际利率", "包邮率"]
            expected_columns = len(expected_headers)

            table_widget.setUpdatesEnabled(False)
            table_widget.setSortingEnabled(False)

            if table_widget.columnCount() != expected_columns:
                table_widget.setColumnCount(expected_columns)
            table_widget.setHorizontalHeaderLabels(expected_headers)
            # 不再添加总计行，只设置数据行数
            table_widget.setRowCount(len(sorted_keys))

            total_orders, total_valid_orders, total_refunds, total_refunded = 0, 0, 0, 0
            total_payment, total_prepayment, total_shipping, total_purchase = 0.0, 0.0, 0.0, 0.0
            total_outgoing_shipping, total_profit, total_actual_profit = 0.0, 0.0, 0.0
            total_free_shipping_orders_in_valid = 0

            for i, key in enumerate(sorted_keys):
                stats = supplier_daily_stats[key]
                row_position = i

                display_date_range = stats.get("display_date_range", f"{start_date}至{end_date}")
                display_supplier_name = stats.get("display_supplier_name", key[1] if isinstance(key, tuple) and len(key) > 1 else "未知上家")

                order_count = stats.get("order_count", 0)
                valid_order_count = stats.get("valid_order_count", 0)
                refund_count = stats.get("refund_count", 0)
                refunded_count = stats.get("refunded_count", 0)
                payment = stats.get("payment", 0.0)
                prepayment = stats.get("prepayment", 0.0)
                shipping = stats.get("shipping", 0.0)
                purchase_amount = stats.get("purchase_amount", 0.0)
                outgoing_shipping = stats.get("outgoing_shipping", 0.0)
                profit_from_remark = stats.get("profit", 0.0)
                actual_profit_field = stats.get("actual_profit", 0.0)
                free_shipping_count_for_this_entry = stats.get("free_shipping_count", 0)

                customer_price = prepayment / order_count if order_count > 0 else 0.0
                purchase_unit_price = purchase_amount / valid_order_count if valid_order_count > 0 else 0.0
                denominator_return_rate = order_count - refunded_count
                return_rate = (refund_count / denominator_return_rate * 100) if denominator_return_rate > 0 else 0.0
                gross_margin_rate = (profit_from_remark / payment * 100) if payment > 0 else 0.0
                net_margin_rate = (actual_profit_field / profit_from_remark * 100) if profit_from_remark != 0 else 0.0
                actual_margin_rate = (actual_profit_field / payment * 100) if payment > 0 else 0.0
                effective_free_shipping_rate = (free_shipping_count_for_this_entry / valid_order_count * 100) if valid_order_count > 0 else 0.0

                table_widget.setItem(row_position, 0, NumericTableWidgetItem(i + 1))
                table_widget.setItem(row_position, 1, QTableWidgetItem(display_date_range))
                table_widget.setItem(row_position, 2, QTableWidgetItem(display_supplier_name))
                table_widget.setItem(row_position, 3, NumericTableWidgetItem(order_count, str(order_count)))
                table_widget.setItem(row_position, 4, NumericTableWidgetItem(valid_order_count, str(valid_order_count)))
                table_widget.setItem(row_position, 5, NumericTableWidgetItem(customer_price, f"{customer_price:.2f}"))
                table_widget.setItem(row_position, 6, NumericTableWidgetItem(return_rate, f"{return_rate:.2f}%"))
                table_widget.setItem(row_position, 7, NumericTableWidgetItem(payment, f"{payment:.2f}"))
                table_widget.setItem(row_position, 8, NumericTableWidgetItem(prepayment, f"{prepayment:.2f}"))
                table_widget.setItem(row_position, 9, NumericTableWidgetItem(shipping, f"{shipping:.2f}"))
                table_widget.setItem(row_position, 10, NumericTableWidgetItem(purchase_amount, f"{purchase_amount:.2f}"))
                table_widget.setItem(row_position, 11, NumericTableWidgetItem(purchase_unit_price, f"{purchase_unit_price:.2f}"))
                table_widget.setItem(row_position, 12, NumericTableWidgetItem(outgoing_shipping, f"{outgoing_shipping:.2f}"))
                table_widget.setItem(row_position, 13, NumericTableWidgetItem(profit_from_remark, f"{profit_from_remark:.2f}"))
                table_widget.setItem(row_position, 14, NumericTableWidgetItem(actual_profit_field, f"{actual_profit_field:.2f}"))
                table_widget.setItem(row_position, 15, NumericTableWidgetItem(gross_margin_rate, f"{gross_margin_rate:.2f}%"))
                table_widget.setItem(row_position, 16, NumericTableWidgetItem(net_margin_rate, f"{net_margin_rate:.2f}%"))
                table_widget.setItem(row_position, 17, NumericTableWidgetItem(actual_margin_rate, f"{actual_margin_rate:.2f}%"))
                table_widget.setItem(row_position, 18, NumericTableWidgetItem(effective_free_shipping_rate, f"{effective_free_shipping_rate:.2f}%"))

                total_orders += order_count
                total_valid_orders += valid_order_count
                total_refunds += refund_count
                total_refunded += refunded_count
                total_payment += payment
                total_prepayment += prepayment
                total_shipping += shipping
                total_purchase += purchase_amount
                total_outgoing_shipping += outgoing_shipping
                total_profit += profit_from_remark
                total_actual_profit += actual_profit_field
                total_free_shipping_orders_in_valid += free_shipping_count_for_this_entry

            # 总计的衍生指标将在update_stats_summary_from_table方法中计算
            # 这里不再需要计算这些指标

            # 不再添加总计行到表格中，总计信息将显示在底部状态栏
            # 确保不添加总计行，只显示数据行

            table_widget.setUpdatesEnabled(True)
            table_widget.setSortingEnabled(True)

            status_message = (
                f"{current_report_type} ({start_date} 至 {end_date}): {len(sorted_keys)} 条记录, "
                f"总订单: {total_orders}, 总有效: {total_valid_orders}, 总实收: {total_payment:.2f}"
            )
            self.set_window_title_status(status_message)
            print(f"on_supplier_stats_data_loaded: {status_message}")

            header = table_widget.horizontalHeader()
            if header and table_widget.columnCount() > 0:
                for i_col in range(table_widget.columnCount()):
                    header.setSectionResizeMode(i_col, QHeaderView.Interactive)

                # 调整列宽并限制最大宽度为125像素
                table_widget.resizeColumnsToContents()
                for i_col in range(table_widget.columnCount()):
                    if header.sectionSize(i_col) > 125:
                        header.resizeSection(i_col, 125)

                if table_widget.columnCount() > 2:
                    header.setStretchLastSection(True)

            self.update_sequence_numbers(table_widget)
            # 缓存保存现在由数据统计模块处理

            # 确保数据加载完成后序号正确显示
            if hasattr(self, 'data_stats_widget') and self.data_stats_widget:
                print("数据加载完成，更新上家统计表格序号")
                self.data_stats_widget.ensure_sequence_numbers_after_data_load("上家统计")

            # 更新底部总计信息
            # 确保在表格填充完成后更新底部总计信息
            if hasattr(self, 'data_stats_widget') and self.data_stats_widget:
                self.data_stats_widget.update_stats_summary_from_table(table_widget, current_report_type)
            else:
                print("数据统计模块不存在，无法更新底部总计信息")

            # 处理事件队列，确保UI更新
            QApplication.processEvents()

        except Exception as e:
            error_msg_detail = f"处理上家统计数据时发生未知错误: {str(e)}"
            print(f"on_supplier_stats_data_loaded 严重错误: {error_msg_detail}")
            print(traceback.format_exc())
            self.set_window_title_status(f"上家统计 - 处理数据出错: {str(e)[:100]}")
            if table_widget:
                table_widget.setUpdatesEnabled(True)
                table_widget.setSortingEnabled(True)
                self._setup_empty_stats_table(current_report_type, table_widget) # Clear and set headers
                table_widget.setRowCount(1)
                error_item = QTableWidgetItem(f"处理显示数据时出错: {str(e)}")
                error_item.setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)
                table_widget.setItem(0, 0, error_item)
                if table_widget.columnCount() > 0:
                    table_widget.setSpan(0, 0, 1, table_widget.columnCount())
            QApplication.processEvents()









    def on_permission_changed(self, state):
        """处理暂无权限复选框状态变化（旧方法，保留向后兼容）"""
        # 根据复选框状态启用或禁用导出按钮
        has_permission = state != Qt.Checked
        # 查找导出按钮并设置其启用状态
        for child in self.findChildren(QPushButton):
            if child.text() == "导出表格":
                child.setEnabled(has_permission)
                # 更新样式以反映启用/禁用状态
                if has_permission:
                    child.setStyleSheet("""
                        QPushButton {
                            background-color: #6c757d;
                            color: white;
                            border: none;
                            border-radius: 5px;
                            padding: 5px 12px;
                            font-size: 13px;
                            font-weight: bold;
                        }
                        QPushButton:hover {
                            background-color: #5a6268;
                        }
                        QPushButton:pressed {
                            background-color: #4e555b;
                        }
                    """)
                else:
                    child.setStyleSheet("""
                        QPushButton {
                            background-color: #adb5bd;
                            color: #e9ecef;
                            border: none;
                            border-radius: 5px;
                            padding: 5px 12px;
                            font-size: 13px;
                            font-weight: bold;
                        }
                    """)
                break

        # 新增: 对店铺统计表格进行筛选
        if hasattr(self, 'stats_table_widget'):
            # 获取当前选中的按钮
            current_report = None
            if hasattr(self, 'stats_buttons'):
                for text, btn in self.stats_buttons.items():
                    if btn.isChecked():
                        current_report = text
                        break

            # 如果当前是店铺统计，应用筛选
            if current_report == "店铺统计":
                # 查找"邀约信息"为"暂无权限"的店铺
                no_permission_shops = set()

                if state == Qt.Checked and hasattr(self, 'table_widget'):
                    # 获取账号管理表格中所有表头
                    header_labels = [self.table_widget.horizontalHeaderItem(i).text()
                                    for i in range(self.table_widget.columnCount())]

                    # 查找"店铺名称"和"邀约信息"列的索引
                    store_name_col_idx = -1
                    invitation_col_idx = -1

                    for i, header in enumerate(header_labels):
                        if header in ["店铺名称", "店铺", "名称"]:
                            store_name_col_idx = i
                        if header in ["邀约信息", "邀约情况", "邀约状态"]:
                            invitation_col_idx = i

                    # 如果找到了两个需要的列
                    if store_name_col_idx != -1 and invitation_col_idx != -1:
                        for row in range(self.table_widget.rowCount()):
                            invitation_item = self.table_widget.item(row, invitation_col_idx)
                            store_name_item = self.table_widget.item(row, store_name_col_idx)

                            if invitation_item and store_name_item:
                                invitation_info = invitation_item.text().strip()
                                store_name = store_name_item.text().strip()

                                if invitation_info == "暂无权限" and store_name:
                                    no_permission_shops.add(store_name)

                        print(f"找到 {len(no_permission_shops)} 个邀约信息为\"暂无权限\"的店铺")
                    else:
                        print(f"未找到需要的列: 店铺名称列索引={store_name_col_idx}, 邀约信息列索引={invitation_col_idx}")

                # 遍历店铺统计表格，根据勾选状态显示/隐藏行
                row_count = self.stats_table_widget.rowCount()
                visible_row_count = 0  # 用于追踪可见行数
                for row in range(row_count):
                    shop_name_item = self.stats_table_widget.item(row, 2)  # 店铺名称在第3列(索引2)

                    if shop_name_item:
                        shop_name = shop_name_item.text()

                        # 如果勾选了"暂无权限"且不是"暂无权限"店铺，则隐藏
                        if state == Qt.Checked and shop_name not in no_permission_shops:
                            self.stats_table_widget.hideRow(row)
                        else:
                            self.stats_table_widget.showRow(row)
                            visible_row_count += 1

                # 筛选完成后重新排序序号
                self.update_visible_sequence_numbers(self.stats_table_widget)

    def export_stats_table(self):
        """导出当前显示的统计表格数据到 CSV 文件。"""
        # 检查权限
        if hasattr(self, "no_permission_check") and self.no_permission_check.isChecked():
            QMessageBox.warning(self, "权限不足", "您暂无导出表格的权限。")
            return

        if not hasattr(self, "stats_table_widget") or self.stats_table_widget.rowCount() == 0:
            QMessageBox.warning(self, "无法导出", "表格中没有数据可导出。")
            return

        # 1. 获取当前选中的报表类型按钮
        report_type = "未知报表"
        if hasattr(self, "stats_buttons"):
            for text, btn in self.stats_buttons.items():
                if btn.isChecked():
                    report_type = text
                    break

        # 2. 获取日期范围并格式化为YYYY-MM-DD格式
        try:
            start_date_str = self.stats_start_date.date().toString("yyyy-MM-dd")
            end_date_str = self.stats_end_date.date().toString("yyyy-MM-dd")
        except Exception as e:
            print(f"获取日期错误: {e}")
            start_date_str = "开始日期"
            end_date_str = "结束日期"

        # 3. 构建优化的文件名格式：[报表类型]_开始日期_结束日期_.csv
        default_filename = f"{report_type}_{start_date_str}_{end_date_str}.csv"
        print(f"导出文件名: {default_filename}")

        # 4. 弹出文件保存对话框
        options = QFileDialog.Options()
        options |= QFileDialog.DontUseNativeDialog
        file_path, _ = QFileDialog.getSaveFileName(self,
                                                    "导出表格为 CSV",
                                                    default_filename,
                                                    "CSV 文件 (*.csv);;所有文件 (*)",
                                                    options=options)

        if not file_path:
            return # 用户取消

        # 确保文件扩展名为 .csv
        if not file_path.lower().endswith(".csv"):
            file_path += ".csv"

        # 5. 写入 CSV 文件
        try:
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)

                # 写入表头
                headers = []
                for col in range(self.stats_table_widget.columnCount()):
                    header_item = self.stats_table_widget.horizontalHeaderItem(col)
                    if header_item:
                        headers.append(header_item.text())
                    else:
                        headers.append(f"列{col}") # 如果没有表头项
                writer.writerow(headers)

                # 写入数据行
                for row in range(self.stats_table_widget.rowCount()):
                    row_data = []
                    for col in range(self.stats_table_widget.columnCount()):
                        item = self.stats_table_widget.item(row, col)
                        row_data.append(item.text() if item else "")
                    writer.writerow(row_data)

            QMessageBox.information(self, "导出成功", f"表格数据已成功导出到:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "导出失败", f"导出表格时发生错误:\n{str(e)}")
            print(f"导出 CSV 错误: {e}")
            traceback.print_exc()

    # -------- 新增：缓存相关方法 --------
    def _get_stats_cache_path(self, report_type):
        """根据报表类型获取缓存文件路径"""
        # 移除文件名中的非法字符，例如空格
        safe_report_type = report_type.replace(" ", "_")
        filename = f"{safe_report_type}.pkl"
        return os.path.join(self.stats_cache_config_dir, filename)

    # 已删除save_stats_to_cache方法，现在使用数据统计模块的缓存方法



    def load_stats_from_cache(self, report_type, table_widget):
        """从缓存文件加载数据到表格"""
        cache_path = self._get_stats_cache_path(report_type)
        if not os.path.exists(cache_path):
            return False

        try:
            # 暂时禁用排序功能，避免在设置数据过程中触发排序
            table_widget.setSortingEnabled(False)

            # 断开旧连接以避免重复连接
            try:
                table_widget.horizontalHeader().sortIndicatorChanged.disconnect()
            except TypeError:
                # 如果没有连接，会抛出异常，但可以忽略
                pass

            with open(cache_path, 'rb') as f:
                cached_data = pickle.load(f)

            headers = cached_data.get("headers", [])
            rows_data = cached_data.get("rows", [])
            # timestamp = cached_data.get("timestamp") # 可选，用于检查缓存时效性

            # 检查缓存数据的有效性
            if not headers or not isinstance(headers, list):
                print(f"缓存数据无效: headers={headers}")
                return False

            if not rows_data or not isinstance(rows_data, list):
                print(f"缓存数据无效: rows_data长度={len(rows_data) if rows_data else 0}")
                return False

            # 设置表格列数和表头
            table_widget.setColumnCount(len(headers))
            table_widget.setHorizontalHeaderLabels(headers)
            table_widget.setRowCount(len(rows_data))

            # 记录处理的行数和列数，用于调试
            processed_rows = 0
            processed_cells = 0

            # 填充表格数据
            for row_idx, row_content in enumerate(rows_data):
                if not isinstance(row_content, list):
                    print(f"跳过无效行数据: row_idx={row_idx}, row_content={type(row_content)}")
                    continue

                processed_rows += 1

                for col_idx, cell_data in enumerate(row_content):
                    if col_idx >= len(headers):
                        print(f"列索引超出范围: row={row_idx}, col={col_idx}, headers_len={len(headers)}")
                        continue

                    try:
                        if isinstance(cell_data, tuple) and len(cell_data) == 2:
                            # 这是 NumericTableWidgetItem 的数据 (value, display_text)
                            # 确保数值类型正确，转换为浮点数，避免字符串比较问题
                            value, display_text = cell_data
                            try:
                                numeric_value = float(value) if isinstance(value, (int, float, str)) and value != "" else 0.0
                            except (ValueError, TypeError):
                                # 如果转换失败，使用默认值
                                numeric_value = 0.0
                                print(f"警告: 无法转换为数值: {value}")
                            item = NumericTableWidgetItem(numeric_value, display_text)
                        else:
                            # 对于普通文本数据，检查是否是数值列（针对所有报表类型）
                            current_header = headers[col_idx] if col_idx < len(headers) else ""

                            # 检查是否是数值列 - 扩展关键词列表以确保所有数值列都被正确识别
                            is_numeric_column = (
                                # 序号列总是数值列
                                col_idx == 0 or
                                # 检查列标题是否包含数值相关关键词
                                any(keyword in current_header for keyword in [
                                    '金额', '数量', '件数', '成本', '利润', '序号',
                                    '订单数', '有效订单', '客单价', '退货率', '实收款',
                                    '预收款', '运费', '采购', '率', '价'
                                ]) or
                                # 对于每日报表、每月报表、年度报表、店铺统计、上家统计，除了序号、日期/月份/年份/店铺/上家名称列外，其余都是数值列
                                (report_type in ["每日报表", "每月报表", "年度报表"] and col_idx >= 2) or
                                # 对于店铺统计和上家统计，第3列(索引2)是店铺名称/上家名称，不是数值列
                                (report_type in ["店铺统计", "上家统计"] and col_idx >= 3)
                            )

                            if is_numeric_column and cell_data:
                                try:
                                    # 尝试将文本转换为数值
                                    text = str(cell_data)
                                    # 清理文本，移除百分号、¥符号和逗号
                                    cleaned_text = text.replace('%', '').replace('¥', '').replace(',', '').strip()
                                    if cleaned_text:  # 确保不是空字符串
                                        # 尝试提取数字部分
                                        import re
                                        match = re.search(r'-?\d+\.?\d*', cleaned_text)
                                        if match:
                                            numeric_value = float(match.group(0))
                                            item = NumericTableWidgetItem(numeric_value, text)
                                        else:
                                            item = QTableWidgetItem(text)
                                    else:
                                        item = QTableWidgetItem(text)
                                except (ValueError, TypeError):
                                    # 转换失败时使用普通表格项
                                    item = QTableWidgetItem(str(cell_data))
                            else:
                                # 非数值列或空值使用普通表格项
                                item = QTableWidgetItem(str(cell_data) if cell_data is not None else "")

                        table_widget.setItem(row_idx, col_idx, item)
                        processed_cells += 1
                    except Exception as cell_error:
                        print(f"设置单元格数据时出错: row={row_idx}, col={col_idx}, error={str(cell_error)}")
                        # 创建一个空的单元格项，避免留下空白
                        table_widget.setItem(row_idx, col_idx, QTableWidgetItem(""))

            print(f"缓存加载统计: 总行数={len(rows_data)}, 处理行数={processed_rows}, 处理单元格={processed_cells}")

            # 对序号进行重新排序以确保数据一致性
            self.update_sequence_numbers(table_widget)

            # 处理事件队列，确保UI更新
            QApplication.processEvents()

            # 调整列宽，设置为可自由拖动
            header = table_widget.horizontalHeader()
            if header and table_widget.columnCount() > 0:
                # 序号列设为自适应内容宽度
                header.setSectionResizeMode(0, QHeaderView.ResizeToContents) # 序号列

                # 其它列设为可自由拖动调整宽度
                for i in range(1, table_widget.columnCount()):
                    header.setSectionResizeMode(i, QHeaderView.Interactive)

                # 设置默认列宽
                if table_widget.columnCount() > 1:
                    header.resizeSection(1, 120)  # 时间列(日期/月份/年份列)设置默认宽度

                # 设置表格可以水平拉伸
                table_widget.horizontalHeader().setStretchLastSection(True)

                print(f"从缓存加载的{report_type}表格已设置列宽: 序号列自适应，其它列可自由拖动")

            # 启用表格排序功能
            table_widget.setSortingEnabled(True)

            # 强制重新连接排序信号到处理方法
            try:
                # 确保信号连接正确
                table_widget.horizontalHeader().sortIndicatorChanged.connect(
                    lambda logicalIndex, order, tbl=table_widget: self.on_stats_table_sort_changed(logicalIndex, order, tbl)
                )
                # 强制设置CURRENT_SORT_ORDER全局变量初始值
                global CURRENT_SORT_ORDER
                CURRENT_SORT_ORDER = Qt.AscendingOrder
            except Exception as e:
                print(f"连接排序信号失败: {e}")

            # 强制重绘表格保证更新
            table_widget.update()

            # 为特定表格应用列宽设置（缓存加载完成后）
            if hasattr(self, 'data_stats_widget') and self.data_stats_widget:
                if report_type in ["每日报表", "店铺统计", "上家统计"]:
                    self.data_stats_widget.apply_column_width_for_table(report_type, table_widget)
                    print(f"缓存加载完成后，{report_type} 表格列宽设置已应用")

            print(f"{report_type} 的数据已从缓存加载，排序功能已启用")
            return True
        except Exception as e:
            print(f"从缓存加载 {report_type} 失败: {e}")
            traceback.print_exc()
            # 如果加载失败，尝试删除可能损坏的缓存文件
            try:
                if os.path.exists(cache_path):
                    os.remove(cache_path)
                    print(f"已删除损坏的缓存文件: {cache_path}")
            except Exception as del_e:
                print(f"删除损坏的缓存文件失败: {del_e}")
            # self.set_window_title_status(f"{report_type} - 缓存加载失败") # 状态由 on_stats_button_clicked 设置 - 移除此行
            if self.stacked_widget.currentWidget() == self.data_stats_page: # 新增: 确保只在数据统计页面活动时更新
                 self.set_window_title_status(f"{report_type} - 缓存加载失败: {str(e)[:100]}") # 新增: 显示带错误详情的提示
            return False

    def _setup_empty_stats_table(self, report_type, table_widget):
        """根据报表类型设置空表格的表头和列数。"""
        print(f"_setup_empty_stats_table: 为 {report_type} 设置空表")
        if not table_widget:
            print(f"错误: _setup_empty_stats_table - table_widget 无效 for {report_type}")
            return

        # 暂时禁用排序功能，避免在设置过程中触发排序
        table_widget.setSortingEnabled(False)

        # 断开旧连接以避免重复连接
        try:
            table_widget.horizontalHeader().sortIndicatorChanged.disconnect()
        except TypeError:
            # 如果没有连接，会抛出异常，但可以忽略
            pass

        headers_map = {
            "每日报表": ["序号", "日期", "订单数", "有效订单", "客单价", "退货率", "实收款", "预收款", "收的运费", "采购金额", "采购单价", "支出运费", "利润", "实际利润", "毛利率", "净利率", "实际利率", "包邮率"],
            "每月报表": ["序号", "月份", "订单数", "有效订单", "客单价", "退货率", "实收款", "预收款", "收的运费", "采购金额", "采购单价", "支出运费", "利润", "实际利润", "毛利率", "净利率", "实际利率", "包邮率"],
            "年度报表": ["序号", "年份", "订单数", "有效订单", "客单价", "退货率", "实收款", "预收款", "收的运费", "采购金额", "采购单价", "支出运费", "利润", "实际利润", "毛利率", "净利率", "实际利率", "包邮率"],
            "店铺统计": ["序号", "日期", "店铺", "订单数", "有效订单", "客单价", "退货率", "实收款", "预收款", "收的运费", "采购金额", "采购单价", "支出运费", "利润", "实际利润", "毛利率", "净利率", "实际利率"],
            "上家统计": ["序号", "日期", "上家名称", "订单数", "有效订单", "客单价", "退货率", "实收款", "预收款", "收的运费", "采购金额", "采购单价", "支出运费", "利润", "实际利润", "毛利率", "净利率", "实际利率", "包邮率"]
        }
        current_headers = headers_map.get(report_type)

        if current_headers:
            table_widget.setColumnCount(len(current_headers))
            table_widget.setHorizontalHeaderLabels(current_headers)
        else:
            # 如果没有找到对应的表头定义，设置一个默认的错误提示表头
            print(f"警告: _setup_empty_stats_table - 未找到 {report_type} 的表头定义")
            table_widget.setColumnCount(1)
            table_widget.setHorizontalHeaderLabels([f"{report_type} 表头未定义"])

        # 清空表格内容，不添加"正在加载"的行
        table_widget.setRowCount(0)

        # 设置列宽
        header = table_widget.horizontalHeader()
        if header and table_widget.columnCount() > 0:
            # 对所有报表类型，设置列宽：序号列自适应，其他列可自由拖动
            print(f"_setup_empty_stats_table: 为{report_type}设置 序号列ResizeToContents, 其他列Interactive(可自由拖动)")
            if header.count() > 1:
                # 序号列(0)设为ResizeToContents
                header.setSectionResizeMode(0, QHeaderView.ResizeToContents) # 序号列

                # 其它列设为Interactive，可自由拖动调整宽度
                for i in range(1, header.count()):
                    header.setSectionResizeMode(i, QHeaderView.Interactive)

                # 设置默认列宽
                header.resizeSection(1, 120)  # 时间列(日期/月份/年份列)设置默认宽度

                # 设置表格可以水平拉伸
                header.setStretchLastSection(True)
            else:
                header.setSectionResizeMode(QHeaderView.Interactive)
                header.setStretchLastSection(True) # 即使只有一列，也设置为可拉伸

        # 强制处理事件队列确保表格更新
        QApplication.processEvents()

        # 重新启用排序功能
        table_widget.setSortingEnabled(True)

        # 重新连接排序信号
        try:
            # 确保信号连接正确
            table_widget.horizontalHeader().sortIndicatorChanged.connect(
                lambda logicalIndex, order, tbl=table_widget: self.on_stats_table_sort_changed(logicalIndex, order, tbl)
            )
            # 强制设置CURRENT_SORT_ORDER全局变量初始值
            global CURRENT_SORT_ORDER
            CURRENT_SORT_ORDER = Qt.AscendingOrder
        except Exception as e:
            print(f"连接排序信号失败: {e}")

        # 强制重绘表格
        table_widget.update()

        # 注意：窗口标题的状态（如\"请查询\"）应该由调用者（如 load_stats_from_cache 的失败路径）处理

    def update_sequence_numbers(self, table_widget):
        """更新表格中的序号列

        该方法会更新表格中每一行的序号，并且正确处理总计行
        """
        if not table_widget:
            return

        print(f"开始更新序号列: 表格行数={table_widget.rowCount()}")

        # 暂时阻塞信号，防止setItem触发不必要的连锁反应，例如再次触发排序
        table_widget.blockSignals(True)
        try:
            for row in range(table_widget.rowCount()):
                # 跳过隐藏行
                if table_widget.isRowHidden(row):
                    continue

                # 检查是否为总计行 (接受多种可能的总计行检测方法)
                is_total_row = False

                # 检查方法1: 查看第一列是否为非数字
                first_item = table_widget.item(row, 0)
                if first_item and (not first_item.text().strip().isdigit() or not first_item.text().strip()):
                    is_total_row = True

                # 检查方法2: 查看第二列是否包含"总计"
                if not is_total_row and table_widget.columnCount() > 1:
                    second_item = table_widget.item(row, 1)
                    if second_item and "总计" in second_item.text():
                        is_total_row = True

                # 检查方法3: 查看第三列店铺名称是否为"总计"
                if not is_total_row and table_widget.columnCount() > 2:
                    third_item = table_widget.item(row, 2)
                    if third_item and third_item.text() == "总计":
                        is_total_row = True

                if not is_total_row: # 只更新非总计行
                    # 使用NumericTableWidgetItem替代普通QTableWidgetItem来确保正确的数字排序
                    sequence_item = NumericTableWidgetItem(row + 1, str(row + 1))
                    sequence_item.setTextAlignment(Qt.AlignCenter)

                    # 保持序号列不可编辑
                    flags = sequence_item.flags()
                    if flags & Qt.ItemIsEditable:
                         sequence_item.setFlags(flags & ~Qt.ItemIsEditable)

                    # 设置序号单元格
                    table_widget.setItem(row, 0, sequence_item)
                else:
                    # 如果是总计行，确保其序号列显示特殊标记
                    total_item = QTableWidgetItem("-")
                    total_item.setTextAlignment(Qt.AlignCenter)
                    total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
                    table_widget.setItem(row, 0, total_item)

            print(f"序号列更新完成")
        except Exception as e:
            print(f"更新序号时出错: {e}")
            traceback.print_exc()
        finally:
            # 操作完成后恢复信号
            table_widget.blockSignals(False)

    def on_account_table_sort_changed(self, logicalIndex, order):
        """处理账号管理表格排序变化事件

        当点击表头进行排序时触发，更新序号并保持分类筛选状态

        Args:
            logicalIndex: 排序列的逻辑索引
            order: 排序方向
        """
        # 更新全局排序方向变量
        global CURRENT_SORT_ORDER
        CURRENT_SORT_ORDER = order
        print(f"账号管理表格排序: 列={logicalIndex}, 方向={'降序' if order == Qt.DescendingOrder else '升序'}")

        # 保存当前的筛选状态
        current_category = ""
        current_no_permission = False

        if hasattr(self, 'category_combo'):
            current_category = self.category_combo.currentText()

        if hasattr(self, 'no_permission_checkbox'):
            current_no_permission = self.no_permission_checkbox.isChecked()

        # 先通过隐藏行列表保存当前筛选状态
        hidden_rows = [i for i in range(self.table_widget.rowCount())
                       if self.table_widget.isRowHidden(i)]

        # 如果当前有分类筛选，重新应用筛选条件
        if current_category and current_category != "全部":
            print(f"重新应用分类筛选: {current_category}")
            # 调用分类筛选函数，这会重新隐藏/显示行
            self.on_category_filter_changed(self.category_combo.currentIndex())
        elif current_no_permission:
            print("重新应用暂无权限筛选")
            # 重新应用暂无权限筛选
            self.on_no_permission_filter_changed(Qt.Checked)
        else:
            # 如果没有当前筛选，则恢复之前的筛选状态
            for row in hidden_rows:
                if row < self.table_widget.rowCount():
                    self.table_widget.setRowHidden(row, True)

        # 在筛选完成后，更新可见行的序号
        print("更新可见行序号...")
        self.update_visible_sequence_numbers(self.table_widget)

        # 更新总计显示
        self.update_shop_count_display()

        # 更新账号管理统计标签
        self.update_account_statistics_display()

        print("账号管理表格排序处理完成")

    def update_shop_count_display(self):
        """更新店铺总计显示"""
        if not hasattr(self, 'table_widget') or not hasattr(self, 'total_shops_label'):
            return

        # 计算总行数和可见行数
        total_rows = self.table_widget.rowCount()
        visible_rows = 0
        checked_rows = 0

        # 获取复选框代理中已选择的行数
        if hasattr(self, 'checkbox_delegate'):
            checked_rows = len(self.checkbox_delegate.get_checked_rows())

        # 计算可见行数
        for row in range(total_rows):
            if not self.table_widget.isRowHidden(row):
                visible_rows += 1

        # 更新总计显示标签
        self.total_shops_label.setText(f"总店铺: {total_rows} | 已选: {visible_rows} | 已勾选: {checked_rows}")

        # 更新统计标签
        self.update_statistics_display()

    def update_account_statistics_display(self):
        """更新账号管理页面底部统计标签显示（在售商品统计）"""
        if not hasattr(self, 'table_widget'):
            return

        # 获取表头信息，查找在售列
        headers = []
        for col in range(self.table_widget.columnCount()):
            header_item = self.table_widget.horizontalHeaderItem(col)
            if header_item:
                headers.append(header_item.text())

        # 查找在售列的索引
        on_sale_col = -1
        for i, header in enumerate(headers):
            if header == "在售":
                on_sale_col = i
                break

        if on_sale_col == -1:
            # 没有找到在售列，显示默认信息
            if hasattr(self, 'all_shops_label'):
                self.all_shops_label.setText("全部店铺: 0")
                self.on_sale_products_label.setText("在售商品: 0")
                self.total_products_label.setText("总商品: 0")
            return

        # 获取当前选中的分类
        current_category = "全部店铺"
        if hasattr(self, 'category_combo'):
            current_category = self.category_combo.currentText()
            if current_category == "全部":
                current_category = "全部店铺"

        # 统计在售商品数据 - 保持原来的统计逻辑
        total_products = 0  # 商品总数
        valid_shop_count = 0  # 有商品的店铺数

        for row in range(self.table_widget.rowCount()):
            # 只统计可见行（考虑分类筛选）
            if self.table_widget.isRowHidden(row):
                continue

            item = self.table_widget.item(row, on_sale_col)
            if item:
                value = item.text().strip()
                if value:
                    # 提取在售商品数量
                    extracted_number = self.extract_on_sale_number(value)
                    if extracted_number > 0:
                        total_products += extracted_number
                        valid_shop_count += 1

        # 计算平均每店商品数
        avg_products_per_shop = int(total_products / valid_shop_count) if valid_shop_count > 0 else 0

        # 更新多彩统计标签 - 保持原来的统计含义
        if hasattr(self, 'all_shops_label'):
            self.all_shops_label.setText(f"总商品: {total_products}")
            self.on_sale_products_label.setText(f"有商品店铺: {valid_shop_count}")
            self.total_products_label.setText(f"平均每店: {avg_products_per_shop}")

        print(f"账号管理统计更新: {current_category}：{total_products}|{valid_shop_count}|{avg_products_per_shop} (总商品数:{total_products}, 有商品店铺数:{valid_shop_count}, 平均每店:{avg_products_per_shop})")

    def extract_total_number(self, value):
        """从在售列数据中提取总商品数量（在售+下架）

        支持的格式：
        1. [3]420/0 -> 提取420+0=420
        2. 2892/590 -> 提取2892+590=3482
        3. 420 -> 提取420

        Args:
            value (str): 在售列的值

        Returns:
            int: 总商品数量
        """
        try:
            import re

            # 格式1: [3]420/0 - 提取420和0
            match1 = re.search(r'\](\d+)/(\d+)', value)
            if match1:
                on_sale = int(match1.group(1))
                off_sale = int(match1.group(2))
                return on_sale + off_sale

            # 格式2: 2892/590 - 提取2892和590
            match2 = re.search(r'^(\d+)/(\d+)', value)
            if match2:
                on_sale = int(match2.group(1))
                off_sale = int(match2.group(2))
                return on_sale + off_sale

            # 格式3: 420 - 纯数字
            match3 = re.search(r'^\d+$', value)
            if match3:
                return int(value)

            return 0
        except:
            return 0

    def extract_on_sale_number(self, value):
        """从在售列数据中提取在售商品数量

        支持的格式：
        1. [3]420/0 -> 提取420（]和/之间的数字）
        2. 2892/590 -> 提取2892（/前面的数字）
        3. 420 -> 提取420（纯数字）

        Args:
            value (str): 在售列的原始值

        Returns:
            int: 提取的商品数量，提取失败返回0
        """
        if not value or not isinstance(value, str):
            return 0

        try:
            value = value.strip()

            # 格式1: [数字]数字/数字 -> 提取]和/之间的数字
            if ']' in value and '/' in value:
                parts = value.split(']')
                if len(parts) >= 2:
                    after_bracket = parts[1].strip()
                    if '/' in after_bracket:
                        slash_parts = after_bracket.split('/')
                        if len(slash_parts) >= 1 and slash_parts[0].strip():
                            return int(float(slash_parts[0].strip()))

            # 格式2: 数字/数字 -> 提取/前面的数字
            elif '/' in value:
                parts = value.split('/')
                if len(parts) >= 1 and parts[0].strip():
                    return int(float(parts[0].strip()))

            # 格式3: 纯数字
            else:
                return int(float(value))

        except (ValueError, IndexError):
            pass

        return 0

    def update_statistics_display(self):
        """更新统计标签显示（余额、保证金、运费险）"""
        if not hasattr(self, 'table_widget'):
            return

        # 获取表头信息
        headers = []
        for col in range(self.table_widget.columnCount()):
            header_item = self.table_widget.horizontalHeaderItem(col)
            if header_item:
                headers.append(header_item.text())

        # 查找对应列的索引
        balance_col = -1
        deposit_col = -1
        freight_insurance_col = -1
        invitation_col = -1
        shipping_fee_col = -1

        for i, header in enumerate(headers):
            if header == "钱包":
                balance_col = i
            elif header == "保证金":
                deposit_col = i
            elif header == "运费险":
                freight_insurance_col = i
            elif header == "邀约信息":
                invitation_col = i
            elif header == "运费":
                shipping_fee_col = i

        # 计算统计数据
        balance_total = 0
        deposit_total = 0
        freight_insurance_total = 0
        invitation_total = 0
        shipping_fee_total = 0
        shipping_fee_count = 0  # 有运费数据的行数

        for row in range(self.table_widget.rowCount()):
            # 只统计可见行
            if self.table_widget.isRowHidden(row):
                continue

            # 计算余额（钱包列）
            if balance_col >= 0:
                item = self.table_widget.item(row, balance_col)
                if item:
                    try:
                        value = float(item.text().replace(',', ''))
                        balance_total += value
                    except (ValueError, AttributeError):
                        pass

            # 计算保证金（只取/前面的数值）
            if deposit_col >= 0:
                item = self.table_widget.item(row, deposit_col)
                if item:
                    try:
                        text = item.text()
                        if '/' in text:
                            # 取/前面的数值
                            value_str = text.split('/')[0].strip().replace(',', '')
                            value = float(value_str)
                            deposit_total += value
                        else:
                            # 如果没有/，直接取整个数值
                            value = float(text.replace(',', ''))
                            deposit_total += value
                    except (ValueError, AttributeError):
                        pass

            # 计算运费险
            if freight_insurance_col >= 0:
                item = self.table_widget.item(row, freight_insurance_col)
                if item:
                    try:
                        value = float(item.text().replace(',', ''))
                        freight_insurance_total += value
                    except (ValueError, AttributeError):
                        pass

            # 计算邀约（取第一个/和第二个/之间的数值）
            if invitation_col >= 0:
                item = self.table_widget.item(row, invitation_col)
                if item:
                    try:
                        text = item.text()
                        # 查找所有/的位置
                        slash_positions = [i for i, char in enumerate(text) if char == '/']
                        if len(slash_positions) >= 2:
                            # 取第一个/和第二个/之间的数值
                            start_pos = slash_positions[0] + 1
                            end_pos = slash_positions[1]
                            value_str = text[start_pos:end_pos].strip().replace(',', '')
                            value = float(value_str)
                            invitation_total += value
                    except (ValueError, AttributeError, IndexError):
                        pass

            # 计算运费（求平均值）
            if shipping_fee_col >= 0:
                item = self.table_widget.item(row, shipping_fee_col)
                if item and item.text().strip():  # 确保有数据
                    try:
                        value = float(item.text().replace(',', ''))
                        shipping_fee_total += value
                        shipping_fee_count += 1
                    except (ValueError, AttributeError):
                        pass

        # 更新标签显示
        if hasattr(self, 'balance_label'):
            self.balance_label.setText(f"余额: {balance_total:,.0f}")

        if hasattr(self, 'deposit_label'):
            self.deposit_label.setText(f"保证金: {deposit_total:,.0f}")

        if hasattr(self, 'freight_insurance_label'):
            self.freight_insurance_label.setText(f"运费险: {freight_insurance_total:,.0f}")

        if hasattr(self, 'invitation_label'):
            self.invitation_label.setText(f"邀约: {invitation_total:,.0f}")

        if hasattr(self, 'shipping_fee_label'):
            # 计算运费平均值
            if shipping_fee_count > 0:
                shipping_fee_avg = shipping_fee_total / shipping_fee_count
                self.shipping_fee_label.setText(f"运费: {shipping_fee_avg:,.1f}")
            else:
                self.shipping_fee_label.setText("运费: 0")

    def update_visible_sequence_numbers(self, table_widget):
        """更新可见行的序号，跳过隐藏行"""
        if not table_widget:
            return

        # 暂时阻塞信号，防止setItem触发不必要的连锁反应
        table_widget.blockSignals(True)
        try:
            visible_row_index = 1  # 可见行的序号，从1开始

            # 如果有复选框代理，先更新显示序号映射
            if hasattr(self, 'checkbox_delegate'):
                # 更新复选框代理中的显示序号映射
                self.checkbox_delegate.update_display_numbers(table_widget)

            for row in range(table_widget.rowCount()):
                # 如果行是隐藏的，则跳过
                if table_widget.isRowHidden(row):
                    continue

                # 检查是否为总计行
                is_total_row = False
                if table_widget.columnCount() > 2:
                    shop_name_item = table_widget.item(row, 2)  # 店铺名称在第3列(索引2)
                    if shop_name_item and shop_name_item.text() == "总计":
                        is_total_row = True

                if not is_total_row:  # 只更新非总计行
                    # 创建带复选框的序号项
                    if hasattr(self, 'checkbox_delegate'):
                        seq_item = CheckBoxNumericTableWidgetItem(visible_row_index)
                    else:
                        # 如果没有复选框代理，使用普通的NumericTableWidgetItem
                        seq_item = NumericTableWidgetItem(visible_row_index, str(visible_row_index))
                        seq_item.setTextAlignment(Qt.AlignCenter)

                        # 保持序号列不可编辑
                        flags = seq_item.flags()
                        if flags & Qt.ItemIsEditable:
                            seq_item.setFlags(flags & ~Qt.ItemIsEditable)

                    # 设置序号
                    table_widget.setItem(row, 0, seq_item)
                    visible_row_index += 1  # 增加可见行索引

            # 更新表格显示
            table_widget.update()
        finally:
            # 操作完成后恢复信号
            table_widget.blockSignals(False)

    def update_stats_visible_sequence_numbers(self, table_widget):
        """更新数据统计表格中可见行的序号，跳过隐藏行"""
        if not table_widget:
            print("表格控件为空，跳过序号更新")
            return

        print(f"开始更新数据统计表格可见行序号: 表格行数={table_widget.rowCount()}")

        # 暂时阻塞信号，防止setItem触发不必要的连锁反应
        table_widget.blockSignals(True)
        try:
            visible_row_index = 1  # 可见行的序号，从1开始
            updated_count = 0  # 记录更新的行数

            for row in range(table_widget.rowCount()):
                # 如果行是隐藏的，则跳过
                if table_widget.isRowHidden(row):
                    continue

                # 检查是否为总计行
                is_total_row = False
                if table_widget.columnCount() > 1:
                    # 检查第二列（通常是日期列或店铺名称列）是否包含总计标识
                    second_col_item = table_widget.item(row, 1)
                    if second_col_item:
                        cell_text = second_col_item.text().strip()
                        if any(keyword in cell_text for keyword in ["总计", "合计", "汇总", "总和"]):
                            is_total_row = True

                if not is_total_row:  # 只更新非总计行
                    # 创建序号项
                    sequence_item = QTableWidgetItem(str(visible_row_index))
                    sequence_item.setTextAlignment(Qt.AlignCenter)

                    # 保持序号列不可编辑
                    flags = sequence_item.flags()
                    if flags & Qt.ItemIsEditable:
                        sequence_item.setFlags(flags & ~Qt.ItemIsEditable)

                    # 设置序号
                    table_widget.setItem(row, 0, sequence_item)
                    visible_row_index += 1  # 增加可见行索引
                    updated_count += 1
                else:
                    # 总计行，显示特殊标记
                    total_item = QTableWidgetItem("-")
                    total_item.setTextAlignment(Qt.AlignCenter)
                    total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
                    table_widget.setItem(row, 0, total_item)

            print(f"数据统计表格可见行序号更新完成: 更新了 {updated_count} 行")

            # 更新表格显示
            table_widget.update()
        except Exception as e:
            print(f"更新数据统计表格可见行序号时出错: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            # 操作完成后恢复信号
            table_widget.blockSignals(False)

    def show_stats_table_context_menu(self, position, table_widget):
        """显示数据统计表格的右键菜单

        Args:
            position (QPoint): 鼠标点击位置
            table_widget (QTableWidget): 表格控件
        """
        # 创建右键菜单
        context_menu = QMenu(self)

        # 设置现代化菜单样式
        context_menu.setStyleSheet("""
            QMenu {
                background-color: #FFFFFF;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                padding: 6px 0px;
                font-family: 'Microsoft YaHei';
                font-size: 13px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }
            QMenu::item {
                background-color: transparent;
                color: #374151;
                padding: 8px 16px;
                margin: 2px 6px;
                border-radius: 6px;
                min-width: 120px;
            }
            QMenu::item:selected {
                background-color: #EEF2FF;
                color: #4338CA;
                font-weight: 600;
            }
            QMenu::item:pressed {
                background-color: #E0E7FF;
                color: #3730A3;
            }
            QMenu::separator {
                height: 1px;
                background-color: #F3F4F6;
                margin: 4px 12px;
            }
        """)

        # 检查是否是上家统计表格
        is_supplier_stats = (hasattr(self, 'current_stats_type') and
                           self.current_stats_type == "上家统计")

        # 如果是上家统计表格，添加特殊菜单项
        if is_supplier_stats:
            # 获取当前选中行
            current_row = table_widget.currentRow()
            if current_row >= 0:
                # 添加查看店铺菜单项
                view_shop_action = QAction("🏪 查看店铺", self)
                view_shop_action.triggered.connect(lambda: self.open_supplier_shop_from_stats(table_widget, current_row))
                context_menu.addAction(view_shop_action)
                context_menu.addSeparator()

        # 添加复制菜单项
        copy_cell_action = QAction("📋 复制", self)
        copy_cell_action.triggered.connect(lambda: self.copy_stats_cell_content(table_widget))

        # 添加全选和取消全选菜单项
        select_all_action = QAction("✅ 全选", self)
        select_all_action.triggered.connect(lambda: table_widget.selectAll())

        deselect_all_action = QAction("❌ 取消全选", self)
        deselect_all_action.triggered.connect(lambda: table_widget.clearSelection())

        # 将菜单项添加到菜单
        context_menu.addAction(copy_cell_action)
        context_menu.addSeparator()
        context_menu.addAction(select_all_action)
        context_menu.addAction(deselect_all_action)

        # 显示右键菜单
        cursor_pos = QCursor.pos()
        context_menu.exec_(cursor_pos)

    def on_stats_table_sort_changed(self, logicalIndex, order, table_widget):
        """处理数据统计表格排序变化事件

        当点击表头进行排序时触发，执行排序并更新序号

        Args:
            logicalIndex (int): 排序列的逻辑索引
            order (Qt.SortOrder): 排序方向
            table_widget (QTableWidget): 要排序的表格控件
        """
        # 检查表格是否属于数据统计模块
        if hasattr(table_widget, '_stats_module_sort') and table_widget._stats_module_sort:
            print(f"数据统计表格排序由数据统计模块处理，主程序跳过")
            return

        # 检查表格是否在数据统计模块中
        if hasattr(self, 'data_stats_widget') and self.data_stats_widget:
            for table_type, stats_table in self.data_stats_widget.stats_tables.items():
                if stats_table == table_widget:
                    print(f"检测到数据统计表格 {table_type}，主程序跳过排序")
                    return

        # 调试输出
        print(f"主程序排序事件被触发: 列={logicalIndex}, 方向={'降序' if order == Qt.DescendingOrder else '升序'}, 表格={table_widget}")

        # 更新全局排序方向变量
        global CURRENT_SORT_ORDER
        CURRENT_SORT_ORDER = order

        # 暂时禁用表格更新，避免闪烁
        table_widget.setUpdatesEnabled(False)

        # 获取当前隐藏行
        hidden_rows = [i for i in range(table_widget.rowCount())
                       if table_widget.isRowHidden(i)]

        # 执行排序 - 先禁用排序功能，然后手动排序
        table_widget.setSortingEnabled(False)

        # 获取所有行的数据
        rows_data = []
        for row in range(table_widget.rowCount()):
            row_data = []
            for col in range(table_widget.columnCount()):
                item = table_widget.item(row, col)
                if item:
                    # 如果是NumericTableWidgetItem，保存其数值和显示文本
                    if isinstance(item, NumericTableWidgetItem):
                        row_data.append((item._numeric_value, item.text()))
                    else:
                        row_data.append(item.text())
                else:
                    row_data.append("")
            rows_data.append((row, row_data))  # 保存原始行索引

        # 根据指定列排序
        def get_sort_key(row_tuple):
            row_idx, row_data = row_tuple
            cell_data = row_data[logicalIndex]

            # 如果是元组(数值,文本)，按数值排序
            if isinstance(cell_data, tuple) and len(cell_data) == 2:
                numeric_value = cell_data[0] if cell_data[0] is not None else float('-inf')
                return (0, numeric_value)  # 数值类型优先级为0

            # 尝试转换为数值
            if isinstance(cell_data, str):
                try:
                    # 清理文本，移除百分号、¥符号和逗号
                    cleaned_text = cell_data.replace('%', '').replace('¥', '').replace(',', '').strip()
                    if cleaned_text:
                        # 尝试提取数字部分
                        import re
                        match = re.search(r'-?\d+\.?\d*', cleaned_text)
                        if match:
                            return (0, float(match.group(0)))  # 数值类型优先级为0
                except (ValueError, TypeError):
                    pass
                # 如果无法转换为数值，按文本排序
                return (1, str(cell_data))  # 文本类型优先级为1

            # 处理其他数值类型
            if isinstance(cell_data, (int, float)):
                return (0, float(cell_data))  # 数值类型优先级为0

            # 默认按文本排序
            return (1, str(cell_data))  # 文本类型优先级为1

        # 排序数据
        rows_data.sort(key=get_sort_key, reverse=(order == Qt.DescendingOrder))

        # 重新填充表格
        table_widget.blockSignals(True)  # 阻塞信号避免循环触发
        try:
            # 重新填充表格
            for new_row, (old_row, row_data) in enumerate(rows_data):
                for col, cell_data in enumerate(row_data):
                    if isinstance(cell_data, tuple) and len(cell_data) == 2:
                        # 如果是(数值,文本)元组，创建NumericTableWidgetItem
                        numeric_value, display_text = cell_data
                        item = NumericTableWidgetItem(numeric_value, display_text)
                    else:
                        # 否则创建普通的QTableWidgetItem
                        item = QTableWidgetItem(str(cell_data))

                    table_widget.setItem(new_row, col, item)

            # 更新序号列
            self.update_sequence_numbers(table_widget)
        finally:
            table_widget.blockSignals(False)  # 恢复信号

        # 恢复隐藏行状态
        for row in hidden_rows:
            if row < table_widget.rowCount():
                table_widget.setRowHidden(row, True)

        # 重新启用排序功能和表格更新
        table_widget.setSortingEnabled(True)
        table_widget.setUpdatesEnabled(True)

        # 强制重绘表格
        table_widget.update()

    def copy_stats_cell_content(self, table_widget):
        """复制数据统计表格中选中单元格的内容"""
        # 获取当前选中的单元格
        selected_items = table_widget.selectedItems()

        if not selected_items:
            return

        # 如果只选中了一个单元格，直接复制其内容
        if len(selected_items) == 1:
            cell_content = selected_items[0].text()
            clipboard = QApplication.clipboard()
            clipboard.setText(cell_content)
            self.set_window_title_status(f"已复制: {cell_content}")
        else:
            # 对于多个选中的单元格，直接复制选中的单元格内容
            # 创建一个字典来存储选中的单元格，以行列坐标为键
            selected_cells = {}
            for item in selected_items:
                row, col = item.row(), item.column()
                if row not in selected_cells:
                    selected_cells[row] = {}
                selected_cells[row][col] = item.text()

            # 生成文本内容，按行排序
            rows = sorted(selected_cells.keys())
            text = ""
            for row in rows:
                # 按列排序
                cols = sorted(selected_cells[row].keys())
                row_text = ""
                for col in cols:
                    row_text += selected_cells[row][col] + "\t"
                text += row_text.rstrip("\t") + "\n"

            clipboard = QApplication.clipboard()
            clipboard.setText(text.rstrip("\n"))
            self.set_window_title_status(f"已复制: {len(selected_items)} 个单元格")

class CustomListWidget(QListWidget):
    """自定义列表控件，用于处理拖放事件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragEnabled(True)
        self.setAcceptDrops(True)
        self.setDropIndicatorShown(True)
        self.setDragDropMode(QAbstractItemView.InternalMove)  # 改为InternalMove，允许在列表内部移动
        self.setDefaultDropAction(Qt.MoveAction)  # 设置默认拖放动作为移动

    def dropEvent(self, event):
        """重写拖放事件，防止重复项"""
        # 先调用父类的dropEvent，确保基本的拖放功能正常工作
        super().dropEvent(event)

        # 在拖放完成后，检查并移除重复项
        self.removeDuplicates()

    def removeDuplicates(self):
        """移除列表中的重复项"""
        # 创建一个集合，用于跟踪已经处理过的项
        processed_items = set()

        # 从后向前遍历列表，移除重复项
        for i in range(self.count() - 1, -1, -1):
            item_text = self.item(i).text()

            # 如果该项已经在集合中，则移除
            if item_text in processed_items:
                self.takeItem(i)
            else:
                # 否则，将其添加到集合中
                processed_items.add(item_text)

class FieldSettingsDialog(QDialog):
    """字段设置对话框，用于选择要显示的字段和顺序"""

    def __init__(self, available_fields, selected_fields, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog) # 设置为无边框对话框
        self.setAttribute(Qt.WA_TranslucentBackground) # 允许异形窗口和阴影效果
        self.available_fields = available_fields
        self.selected_fields = selected_fields.copy() if selected_fields else []

        # 设置窗口属性
        self.setWindowTitle("表格字段设置")
        self.setMinimumSize(750, 550) # 调整最小尺寸
        self.setModal(True)

        # 创建主容器用于圆角效果
        self.main_container = QWidget(self)
        self.main_container.setObjectName("mainContainer")

        # 设置窗口阴影效果
        self.shadow = QGraphicsDropShadowEffect(self)
        self.shadow.setBlurRadius(30)  # 增加模糊半径，使阴影更柔和
        self.shadow.setColor(QColor(0, 0, 0, 80))  # 增加阴影不透明度
        self.shadow.setOffset(0, 6)  # 稍微增加垂直偏移，使阴影更明显
        self.main_container.setGraphicsEffect(self.shadow)

        # 用于拖拽功能的变量
        self.drag_position = None

        # 设置窗口样式 - 现代化圆角版本
        self.setStyleSheet("""
            QDialog {
                background-color: transparent;
                font-family: 'Microsoft YaHei';
            }
            QWidget#mainContainer {
                background-color: #FFFFFF;
                border-radius: 16px;
                border: 1px solid #E5E7EB;
            }
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                margin-bottom: 6px;
                background-color: transparent;
            }
            QLabel#titleLabel {
                color: #111827;
                font-size: 18px;
                font-weight: 700;
                margin-bottom: 8px;
                padding: 0px 4px;
            }
            QListWidget {
                border: 2px solid #E5E7EB;
                border-radius: 10px;
                background-color: #FAFAFA;
                padding: 8px;
                font-size: 13px;
                color: #374151;
                selection-background-color: transparent;
            }
            QListWidget::item {
                border: 1px solid transparent;
                padding: 10px 12px;
                border-radius: 8px;
                margin: 2px 0px;
                background-color: #FFFFFF;
            }
            QListWidget::item:selected {
                background-color: #EEF2FF;
                color: #4338CA;
                font-weight: 600;
                border: 2px solid #A5B4FC;
            }
            QListWidget::item:hover {
                background-color: #F8FAFC;
                border: 1px solid #CBD5E1;
            }
            /* 通用按钮样式 */
            QPushButton {
                border-radius: 5px;
                padding: 8px 12px;
                font-size: 14px;
                font-weight: 500;
                min-height: 30px; /* 调整按钮高度 */
            }
            /* 主操作按钮（例如：确定） */
            QPushButton#primaryButton {
                background-color: #4A55A2; /* 主按钮颜色 - 深蓝紫 */
                color: white;
                border: none;
                padding: 8px 18px; /* 主按钮稍大一些的内边距 */
            }
            QPushButton#primaryButton:hover {
                background-color: #40498C;
            }
            QPushButton#primaryButton:pressed {
                background-color: #353D73;
            }
            /* 次要/默认按钮（例如：取消） */
            QPushButton#secondaryButton {
                background-color: #F3F4F6; /* 次要按钮背景 */
                color: #374151;
                border: 1px solid #D1D5DB;
            }
            QPushButton#secondaryButton:hover {
                background-color: #E5E7EB;
                border-color: #9CA3AF;
            }
            QPushButton#secondaryButton:pressed {
                background-color: #D1D5DB;
            }
            /* 中间的图标操作按钮特定样式 */
            QPushButton#actionButton {
                background-color: white;
                color: #4B5563; /* 图标按钮颜色 */
                border: 1px solid #D1D5DB;
                min-width: 38px; /* 图标按钮宽度 */
                max-width: 38px;
                min-height: 30px; /* 图标按钮高度 */
                max-height: 30px;
                padding: 5px;    /* 图标按钮内边距 */
                font-size: 16px; /* 图标字号 */
            }
            QPushButton#actionButton:hover {
                background-color: #F9FAFB;
                border-color: #9CA3AF;
            }
            QFrame#contentFrame { /* 内容框架特定样式 */
                background-color: white;
                border-radius: 8px;
            }
            QScrollBar:vertical {
                border: none;
                background: #F3F4F6;
                width: 8px;
                margin: 0px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: #D1D5DB;
                min-height: 25px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical:hover {
                background: #9CA3AF;
            }
        """)

        # 设置窗口图标
        icon_path = get_resource_path(os.path.join("config", "imges", "logo.ico"))
        self.setWindowIcon(QIcon(icon_path))

        # 初始化界面
        self.init_ui() # init_ui 将在下一步被完全替换

        # 填充字段列表
        self.populate_fields()

        # 居中显示
        if parent:
            x = parent.geometry().center().x() - self.width() // 2
            y = parent.geometry().center().y() - self.height() // 2
            self.move(x, y)

    def init_ui(self):
        """初始化界面"""
        # 创建主布局（用于整个对话框）
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)  # 为阴影留出空间
        main_layout.setSpacing(0)

        # 将主容器添加到主布局
        main_layout.addWidget(self.main_container)

        # 创建主容器的内部布局
        container_layout = QVBoxLayout(self.main_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)

        # --- 自定义标题栏 ---
        self.title_bar = QFrame()
        self.title_bar.setFixedHeight(50)
        self.title_bar.setObjectName("customTitleBar")
        self.title_bar.setStyleSheet("""
            QFrame#customTitleBar {
                background-color: #FAFBFC;
                border-bottom: 1px solid #E5E7EB;
                border-top-left-radius: 16px;
                border-top-right-radius: 16px;
            }
        """)
        title_bar_layout = QHBoxLayout(self.title_bar)
        title_bar_layout.setContentsMargins(15, 0, 15, 0) # 增加左右内边距
        title_bar_layout.setSpacing(5)

        # 添加图标
        icon_label = QLabel()
        icon_path = get_resource_path(os.path.join("config", "imges", "logo.ico"))
        if os.path.exists(icon_path):
            pixmap = QIcon(icon_path).pixmap(QSize(20, 20))
            icon_label.setPixmap(pixmap)
        title_bar_layout.addWidget(icon_label)
        title_bar_layout.addSpacing(5)

        self.window_title_label = QLabel("表格字段设置") # 从 self.windowTitle() 获取或许更好
        self.window_title_label.setStyleSheet("color: #1e293b; font-size: 15px; font-weight: bold;")
        title_bar_layout.addWidget(self.window_title_label)
        title_bar_layout.addStretch()

        # 添加关闭按钮
        close_button = QPushButton("×")
        close_button.setFixedSize(30, 30)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #64748b;
                font-size: 18px;
                font-weight: bold;
                border: none;
                border-radius: 15px;
            }
            QPushButton:hover {
                background-color: #fee2e2;
                color: #ef4444;
            }
            QPushButton:pressed {
                background-color: #fecaca;
            }
        """)
        close_button.clicked.connect(self.reject)
        title_bar_layout.addWidget(close_button)

        container_layout.addWidget(self.title_bar) # 将自定义标题栏添加到容器布局顶部

        # --- 内容框架 ---
        # 创建更现代化的内容框架
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        content_frame.setStyleSheet("""
            QFrame#contentFrame {
                background-color: #FFFFFF;
                border: none;
                padding: 0px;
            }
        """)

        content_outer_layout = QVBoxLayout(content_frame) # 在content_frame内部再加一层QVBoxLayout控制边距
        content_outer_layout.setContentsMargins(15, 15, 15, 15)
        content_outer_layout.setSpacing(10)

        # # 顶部标题标签 (用户要求删除)
        # title_label = QLabel("自定义表格字段显示与排序")
        # title_label.setStyleSheet("font-size: 16px; font-weight: 600; color: #1F2937; margin-bottom: 10px;")
        # title_label.setAlignment(Qt.AlignCenter)
        # content_outer_layout.addWidget(title_label)

        fields_interaction_layout = QHBoxLayout() # 水平布局：可用字段、按钮、已选字段
        fields_interaction_layout.setSpacing(12)
        # # content_outer_layout.addLayout(fields_interaction_layout) # 这句先注释掉，后面完整构建后再加回来

        # 左侧：可用字段区域
        available_fields_group = QWidget() # 使用QWidget作为容器以便更好地控制拉伸
        available_fields_layout = QVBoxLayout(available_fields_group)
        available_fields_layout.setContentsMargins(0,0,0,0)
        available_fields_layout.setSpacing(5)

        available_label = QLabel("可用字段:")
        self.available_list = CustomListWidget()
        self.available_list.setMinimumHeight(420) # 再次增加列表最小高度
        self.available_list.setAlternatingRowColors(True) # 增加交替行颜色

        available_fields_layout.addWidget(available_label)
        available_fields_layout.addWidget(self.available_list)
        fields_interaction_layout.addWidget(available_fields_group, 2) # 左侧可用字段列表，拉伸因子为2

        # 中间：操作按钮区域 (添加/移除)
        add_remove_buttons_layout = QVBoxLayout()
        add_remove_buttons_layout.setSpacing(10)
        add_remove_buttons_layout.setAlignment(Qt.AlignCenter)
        add_remove_buttons_layout.addStretch(1) # 顶部伸缩

        self.add_button = QPushButton("→") # 使用箭头图标
        self.add_button.setToolTip("添加选中字段")
        self.add_button.setObjectName("actionButton") # 应用QSS中的样式
        self.add_button.clicked.connect(self.add_field)

        self.remove_button = QPushButton("←") # 使用箭头图标
        self.remove_button.setToolTip("移除选中字段")
        self.remove_button.setObjectName("actionButton") # 应用QSS中的样式
        self.remove_button.clicked.connect(self.remove_field)

        add_remove_buttons_layout.addWidget(self.add_button)
        add_remove_buttons_layout.addWidget(self.remove_button)
        add_remove_buttons_layout.addStretch(1) # 底部伸缩
        fields_interaction_layout.addLayout(add_remove_buttons_layout, 0) # 中间按钮组，不拉伸

        # 右侧：已选字段区域
        selected_fields_group = QWidget() # 使用QWidget作为容器
        selected_fields_layout = QVBoxLayout(selected_fields_group)
        selected_fields_layout.setContentsMargins(0,0,0,0)
        selected_fields_layout.setSpacing(5)

        selected_label = QLabel("已选字段 (可拖拽排序):")
        self.selected_list = CustomListWidget()
        self.selected_list.setMinimumHeight(420) # 再次增加列表最小高度
        self.selected_list.setAlternatingRowColors(True) # 增加交替行颜色

        selected_fields_layout.addWidget(selected_label)
        selected_fields_layout.addWidget(self.selected_list)

        # 已选字段列表的操作按钮（上移、下移） - 放在右侧列表下方
        selected_list_actions_layout = QHBoxLayout()
        selected_list_actions_layout.setSpacing(8)
        selected_list_actions_layout.addStretch() # 使按钮靠右

        self.move_up_button = QPushButton("↑")
        self.move_up_button.setToolTip("上移选中字段")
        self.move_up_button.setObjectName("actionButton") # 应用QSS中的样式
        self.move_up_button.clicked.connect(self.move_up)

        self.move_down_button = QPushButton("↓")
        self.move_down_button.setToolTip("下移选中字段")
        self.move_down_button.setObjectName("actionButton") # 应用QSS中的样式
        self.move_down_button.clicked.connect(self.move_down)

        selected_list_actions_layout.addWidget(self.move_up_button)
        selected_list_actions_layout.addWidget(self.move_down_button)
        selected_fields_layout.addLayout(selected_list_actions_layout) # 将上移下移按钮添加到已选字段布局
        fields_interaction_layout.addWidget(selected_fields_group, 2) # 右侧已选字段列表，拉伸因子为2

        # 将整合好的字段交互布局添加到内容区的外部布局中
        content_outer_layout.addLayout(fields_interaction_layout)

        # 以下旧的布局代码已被删除 (相关的 lists_layout 和 content_layout.addLayout(lists_layout) 等)
        # (确保相关的旧代码如 lists_layout 定义和使用已被移除或注释)


        # 添加内容框架到容器布局
        container_layout.addWidget(content_frame)

        # 底部按钮区域 - 现代化设计
        button_frame = QFrame()
        button_frame.setStyleSheet("""
            QFrame {
                background-color: #F8FAFC;
                border-top: 1px solid #E5E7EB;
                border-bottom-left-radius: 16px;
                border-bottom-right-radius: 16px;
            }
        """)
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(20, 10, 20, 10) # 调整上下边距
        button_layout.addStretch(1) # 左侧伸缩，实现居中

        # 取消按钮 - 优化的现代化样式
        cancel_button = QPushButton("取消")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #F1F5F9;
                color: #475569;
                border: 1px solid #CBD5E1;
                border-radius: 8px;
                padding: 8px 20px;
                font-size: 13px;
                font-weight: 600;
                min-width: 100px;
                min-height: 36px;
            }
            QPushButton:hover {
                background-color: #E2E8F0;
                color: #334155;
                border-color: #94A3B8;
            }
            QPushButton:pressed {
                background-color: #CBD5E1;
                color: #1E293B;
            }
        """)
        cancel_button.clicked.connect(self.reject)

        # 确定按钮 - 优化现代化样式
        ok_button = QPushButton("确定")
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #4F46E5;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 20px;
                font-size: 13px;
                font-weight: 600;
                min-width: 100px;
                min-height: 36px;
            }
            QPushButton:hover {
                background-color: #4338CA;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #3730A3;
                transform: translateY(0px);
            }
        """)
        ok_button.clicked.connect(self.accept)

        # 添加按钮到布局
        button_layout.addWidget(cancel_button)
        button_layout.addSpacing(10)
        button_layout.addWidget(ok_button)
        button_layout.addStretch(1) # 右侧伸缩，实现居中

        # 添加按钮框架到容器布局
        container_layout.addWidget(button_frame)

    def populate_fields(self):
        """填充字段列表"""
        # 清空列表
        self.available_list.clear()
        self.selected_list.clear()

        # 创建已选字段集合，用于快速查找
        selected_set = set(self.selected_fields)

        # 添加可用字段（确保不在已选字段中）
        for field in self.available_fields:
            if field not in selected_set:
                self.available_list.addItem(field)

        # 添加已选字段（确保不重复）
        added_fields = set()
        for field in self.selected_fields:
            if field not in added_fields:
                self.selected_list.addItem(field)
                added_fields.add(field)

    def add_field(self):
        """添加字段到已选列表"""
        # 获取所有选中的项目
        selected_items = self.available_list.selectedItems()
        if not selected_items:
            return

        # 创建选中项目的副本，因为在处理过程中会修改选择
        items_to_process = [(item.text(), self.available_list.row(item)) for item in selected_items]
        # 按行号从大到小排序，这样从后向前移除不会影响索引
        items_to_process.sort(key=lambda x: x[1], reverse=True)

        # 获取已选列表中的所有项目文本
        existing_items = set()
        for i in range(self.selected_list.count()):
            existing_items.add(self.selected_list.item(i).text())

        # 处理每个选中的项目
        for field, row in items_to_process:
            # 如果项目不在已选列表中，则添加
            if field not in existing_items:
                self.selected_list.addItem(field)
                existing_items.add(field)
            # 从可用列表中移除
            self.available_list.takeItem(row)

    def remove_field(self):
        """从已选列表移除字段"""
        # 获取所有选中的项目
        selected_items = self.selected_list.selectedItems()
        if not selected_items:
            return

        # 创建选中项目的副本，因为在处理过程中会修改选择
        items_to_process = [(item.text(), self.selected_list.row(item)) for item in selected_items]
        # 按行号从大到小排序，这样从后向前移除不会影响索引
        items_to_process.sort(key=lambda x: x[1], reverse=True)

        # 获取可用列表中的所有项目文本
        existing_items = set()
        for i in range(self.available_list.count()):
            existing_items.add(self.available_list.item(i).text())

        # 处理每个选中的项目
        for field, row in items_to_process:
            # 如果项目不在可用列表中，则添加
            if field not in existing_items:
                self.available_list.addItem(field)
                existing_items.add(field)
            # 从已选列表中移除
            self.selected_list.takeItem(row)

    def move_up(self):
        """上移选中字段"""
        current_row = self.selected_list.currentRow()
        if current_row > 0:
            current_item = self.selected_list.takeItem(current_row)
            self.selected_list.insertItem(current_row - 1, current_item)
            self.selected_list.setCurrentRow(current_row - 1)

    def move_down(self):
        """下移选中字段"""
        current_row = self.selected_list.currentRow()
        if current_row < self.selected_list.count() - 1:
            current_item = self.selected_list.takeItem(current_row)
            self.selected_list.insertItem(current_row + 1, current_item)
            self.selected_list.setCurrentRow(current_row + 1)

    def get_selected_fields(self):
        """获取已选字段列表（确保不重复）"""
        fields = []
        added_fields = set()  # 用于跟踪已添加的字段

        for i in range(self.selected_list.count()):
            field = self.selected_list.item(i).text()
            if field not in added_fields:
                fields.append(field)
                added_fields.add(field)

        return fields

    def mousePressEvent(self, event):
        """鼠标按下事件 - 用于拖拽窗口"""
        if event.button() == Qt.LeftButton:
            # 检查是否点击在标题栏区域
            if hasattr(self, 'title_bar') and self.title_bar.geometry().contains(event.pos()):
                self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
                event.accept()
            else:
                super().mousePressEvent(event)
        else:
            super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 用于拖拽窗口"""
        if event.buttons() == Qt.LeftButton and self.drag_position is not None:
            self.move(event.globalPos() - self.drag_position)
            event.accept()
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件 - 结束拖拽"""
        if event.button() == Qt.LeftButton:
            self.drag_position = None
            event.accept()

    def resizeEvent(self, event):
        """窗口大小改变事件 - 确保内容容器始终正确定位"""
        super().resizeEvent(event)
        if hasattr(self, 'content_container'):
            # 确保内容容器大小和位置与对话框一致
            self.content_container.resize(self.size())
            self.content_container.move(0, 0)

class ServerSettingsDialog(QDialog):
    """服务器设置对话框"""

    def __init__(self, current_server_url, parent=None):
        super().__init__(parent)
        # self.server_config = server_config # 不再需要旧的 server_config 字典
        self.setWindowTitle("服务器设置")
        self.setMinimumSize(400, 200)
        self.setModal(True)

        # 设置窗口图标
        icon_path = get_resource_path(os.path.join("config", "imges", "logo.ico"))
        self.setWindowIcon(QIcon(icon_path))

        self.init_ui()

        # 设置初始值
        if current_server_url:
            self.set_server_url(current_server_url)

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 创建表单布局
        form_layout = QFormLayout()

        # 服务器地址输入框
        self.server_url_edit = QLineEdit()
        self.server_url_edit.setPlaceholderText("请输入服务器地址")
        form_layout.addRow("服务器地址:", self.server_url_edit)

        # 添加表单布局到主布局
        layout.addLayout(form_layout)

        # 添加按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout)

    def get_server_url(self):
        """获取服务器地址"""
        return self.server_url_edit.text().strip()

    def set_server_url(self, url):
        """设置服务器地址"""
        self.server_url_edit.setText(url)

    def get_server_config(self):
        """获取服务器配置"""
        return {
            'server_url': self.get_server_url()
        }

# 从这里开始是主程序部分，不应该在类内部
if __name__ == "__main__":
    # 创建QApplication实例
    app = QApplication(sys.argv)
    # 创建第一个实例并保存在全局变量中，防止被垃圾回收
    main_window = KuaishouShopManager()
    # 使用全局变量存储所有实例，确保不会被垃圾回收
    instances = [main_window]

    sys.exit(app.exec_())
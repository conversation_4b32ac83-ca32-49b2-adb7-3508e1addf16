#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import time
import json
import os
import sys
import hmac
import hashlib
import base64
import traceback
from datetime import datetime
from typing import Dict, Any, Optional
import logging

# 配置日志 - 设置为WARNING级别，减少调试信息
logging.basicConfig(level=logging.WARNING, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("KuaishouAPI")

def get_config_file_path(relative_path):
    """
    获取配置文件的绝对路径，确保使用正确的config目录

    参数:
        relative_path (str): 相对于config目录的文件路径

    返回:
        str: 配置文件的绝对路径
    """
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe文件
        app_dir = os.path.dirname(sys.executable)
    else:
        # 开发环境 - 从tool目录回到上级目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        app_dir = os.path.dirname(current_dir)  # 回到ksxiaodian目录

    config_path = os.path.join(app_dir, 'config', relative_path)
    return config_path

class KuaishouAPI:
    """快手电商开放平台API处理类"""

    def __init__(self, app_key: str = None, app_secret: str = None, access_token: str = None, sign_secret: str = None):
        """
        初始化快手API处理类

        Parameters:
        -----------
        app_key: str, optional
            应用的AppKey，如果不提供则从配置文件读取
        app_secret: str, optional
            应用的AppSecret，如果不提供则从配置文件读取
        access_token: str, optional
            用户授权令牌，如果不提供则从配置文件读取
        sign_secret: str, optional
            签名密钥，如果不提供则从配置文件读取
        """
        # 从配置文件加载配置
        config = self._load_config_from_file()
        
        # 设置API配置，优先使用传入的参数
        self.app_key = app_key or config.get('app_key') or 'default_app_key'
        self.app_secret = app_secret or config.get('app_secret') or 'default_app_secret'
        self.sign_secret = sign_secret or config.get('signSecret') or config.get('sign_secret') or self.app_secret
        self.access_token = access_token or config.get('access_token')

        # 快手API基础配置
        self.base_url = "https://openapi.kwaixiaodian.com"
        self.backup_url = "https://open.kwaixiaodian.com"
        self.version = 1
        self.timeout = 30

        # 创建Session对象
        self.session = requests.Session()
        self.session.timeout = self.timeout

        # logger.info("快手API客户端初始化完成")  # 注释掉调试信息

    @staticmethod
    def _load_config_from_file():
        """
        从配置文件加载快手API配置

        Returns:
        --------
        Dict[str, str]:
            API配置信息
        """
        config_path = get_config_file_path("config.json")
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    kuaishou_config = config_data.get('kuaishou_api_config', {})
                    # logger.info("成功从配置文件加载快手API配置")  # 注释掉调试信息
                    return kuaishou_config
            else:
                logger.warning(f"配置文件 {config_path} 不存在")
                return {}
        except Exception as e:
            logger.error(f"读取配置文件失败: {str(e)}")
            return {}

    @classmethod
    def from_config(cls):
        """
        从配置文件创建KuaishouAPI实例

        Returns:
        --------
        KuaishouAPI:
            从配置文件初始化的API实例
        """
        return cls()

    def _generate_sign(self, request_data: Dict[str, Any]) -> str:
        """
        生成API请求签名 - 按照快手官方文档实现

        Parameters:
        -----------
        request_data: Dict[str, Any]
            请求数据

        Returns:
        --------
        str:
            签名字符串
        """
        try:
            # 1. 排除sign字段，按字典序排序
            params_to_sign = {k: str(v) for k, v in request_data.items() if k != 'sign'}
            
            # 2. 按key排序
            sorted_params = sorted(params_to_sign.items())
            
            # 3. 构建签名字符串: key=value&key=value格式
            sign_parts = []
            for key, value in sorted_params:
                sign_parts.append(f"{key}={value}")
            
            # 拼接参数
            params_str = "&".join(sign_parts)
            
            # 4. 在末尾加入signSecret
            sign_str = f"{params_str}&signSecret={self.sign_secret}"
            
            logger.debug(f"签名前字符串: {sign_str}")
            
            # 5. 根据signMethod生成签名
            sign_method = request_data.get('signMethod', 'HMAC_SHA256')
            
            if sign_method == 'HMAC_SHA256':
                # HMAC_SHA256算法 - 使用Base64编码
                signature = hmac.new(
                    self.sign_secret.encode('utf-8'),
                    sign_str.encode('utf-8'),
                    hashlib.sha256
                ).digest()
                signature = base64.b64encode(signature).decode('utf-8')
            else:
                # MD5算法
                signature = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
            
            logger.debug(f"生成签名: {signature}")
            
            return signature
            
        except Exception as e:
            logger.error(f"生成签名失败: {str(e)}")
            traceback.print_exc()
            return ""

    def _make_request(self, method: str, params: Dict[str, Any], use_backup: bool = False) -> Dict[str, Any]:
        """
        发送API请求

        Parameters:
        -----------
        method: str
            API方法名
        params: Dict[str, Any]
            请求参数
        use_backup: bool
            是否使用备用域名

        Returns:
        --------
        Dict[str, Any]:
            API响应结果
        """
        base_url = self.backup_url if use_backup else self.base_url
        
        # 构建API路径 - 将 . 替换为 /
        api_path = method.replace('.', '/')
        url = f"{base_url}/{api_path}"
        
        # 构建请求数据
        request_data = {
            'appkey': self.app_key,
            'timestamp': int(time.time() * 1000),  # 毫秒级时间戳
            'access_token': self.access_token,
            'version': self.version,
            'method': method,
            'param': json.dumps(params, separators=(',', ':')),  # 压缩JSON格式
            'signMethod': 'HMAC_SHA256'
        }

        # 生成签名
        request_data['sign'] = self._generate_sign(request_data)
        
        # 设置请求头
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'KuaishouAPI/1.0'
        }

        try:
            logger.debug(f"请求URL: {url}")
            logger.debug(f"请求数据: {request_data}")

            response = self.session.post(url, data=request_data, headers=headers, timeout=self.timeout)

            logger.debug(f"响应状态码: {response.status_code}")
            logger.debug(f"响应内容: {response.text}")

            response.raise_for_status()

            result = response.json()
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {method}, 错误: {str(e)}")
            logger.error(f"响应内容: {getattr(e.response, 'text', 'No response')}")
            
            if not use_backup:
                logger.info("尝试使用备用域名重新请求")
                return self._make_request(method, params, use_backup=True)
            raise Exception(f"API请求失败: {str(e)}")

    def update_item_shelf_status(self, kwai_item_id: int, shelf_status: int) -> Dict[str, Any]:
        """
        商品上下架管理

        Parameters:
        -----------
        kwai_item_id: int
            快手商品ID
        shelf_status: int
            商品上架状态。0:下架； 1:上架

        Returns:
        --------
        Dict[str, Any]:
            API响应结果
            
        Raises:
        -------
        ValueError:
            当参数无效时抛出异常
        """
        # 参数验证
        if not isinstance(kwai_item_id, int) or kwai_item_id <= 0:
            raise ValueError("kwai_item_id必须是正整数")
        
        if shelf_status not in [0, 1]:
            raise ValueError("shelf_status必须是0(下架)或1(上架)")

        # 构建请求参数
        params = {
            'kwaiItemId': kwai_item_id,
            'shelfStatus': shelf_status
        }

        logger.info(f"更新商品上下架状态: 商品ID={kwai_item_id}, 状态={'上架' if shelf_status == 1 else '下架'}")
        
        try:
            result = self._make_request('open.item.shelf.status.update', params)
            
            # 检查返回结果
            if result.get('result') == 1:
                logger.info(f"商品上下架操作成功: 商品ID={kwai_item_id}")
                return {
                    'success': True,
                    'message': '操作成功',
                    'data': result
                }
            else:
                error_msg = result.get('error_msg', '未知错误')
                logger.error(f"商品上下架操作失败: {error_msg}")
                return {
                    'success': False,
                    'message': error_msg,
                    'data': result
                }
                
        except Exception as e:
            logger.error(f"商品上下架操作异常: {str(e)}")
            return {
                'success': False,
                'message': f'操作异常: {str(e)}',
                'data': None
            }

    def shelf_item(self, kwai_item_id: int) -> Dict[str, Any]:
        """
        商品上架

        Parameters:
        -----------
        kwai_item_id: int
            快手商品ID

        Returns:
        --------
        Dict[str, Any]:
            API响应结果
        """
        return self.update_item_shelf_status(kwai_item_id, 1)

    def unshelf_item(self, kwai_item_id: int) -> Dict[str, Any]:
        """
        商品下架

        Parameters:
        -----------
        kwai_item_id: int
            快手商品ID

        Returns:
        --------
        Dict[str, Any]:
            API响应结果
        """
        return self.update_item_shelf_status(kwai_item_id, 0)

    def delete_item(self, kwai_item_id: int) -> Dict[str, Any]:
        """
        删除商品

        Parameters:
        -----------
        kwai_item_id: int
            快手商品ID

        Returns:
        --------
        Dict[str, Any]:
            API响应结果

        Raises:
        -------
        ValueError:
            当参数无效时抛出异常
        """
        # 参数验证
        if not isinstance(kwai_item_id, int) or kwai_item_id <= 0:
            raise ValueError("kwai_item_id必须是正整数")

        # 构建请求参数
        params = {
            'kwaiItemId': kwai_item_id
        }

        logger.info(f"删除商品: 商品ID={kwai_item_id}")

        try:
            # 调用API
            response = self._make_request('open.item.delete', params)

            if response.get('result') == 1:
                logger.info(f"商品删除操作成功: 商品ID={kwai_item_id}")
                return {
                    'success': True,
                    'message': '商品删除成功',
                    'data': response.get('data')
                }
            else:
                error_msg = response.get('error_msg', '删除失败')
                logger.error(f"商品删除操作失败: {error_msg}")
                return {
                    'success': False,
                    'message': f'删除失败: {error_msg}',
                    'data': response.get('data')
                }
        except Exception as e:
            logger.error(f"删除商品异常: {str(e)}")
            return {
                'success': False,
                'message': f'删除异常: {str(e)}',
                'data': None
            }

    def batch_shelf_operation(self, item_operations: list) -> Dict[str, Any]:
        """
        批量商品上下架操作

        Parameters:
        -----------
        item_operations: list
            操作列表，每个元素为字典，包含 'item_id' 和 'operation' 键
            operation: 'shelf' 为上架, 'unshelf' 为下架

        Returns:
        --------
        Dict[str, Any]:
            批量操作结果
        """
        results = []
        success_count = 0
        fail_count = 0

        for operation in item_operations:
            try:
                item_id = operation['item_id']
                op_type = operation['operation']
                
                if op_type == 'shelf':
                    result = self.shelf_item(item_id)
                elif op_type == 'unshelf':
                    result = self.unshelf_item(item_id)
                else:
                    result = {
                        'success': False,
                        'message': f'不支持的操作类型: {op_type}',
                        'data': None
                    }
                
                results.append({
                    'item_id': item_id,
                    'operation': op_type,
                    'result': result
                })
                
                if result['success']:
                    success_count += 1
                else:
                    fail_count += 1
                    
            except Exception as e:
                results.append({
                    'item_id': operation.get('item_id', 'unknown'),
                    'operation': operation.get('operation', 'unknown'),
                    'result': {
                        'success': False,
                        'message': f'操作异常: {str(e)}',
                        'data': None
                    }
                })
                fail_count += 1

        return {
            'total': len(item_operations),
            'success_count': success_count,
            'fail_count': fail_count,
            'results': results
        }

    def get_item_list(self, kwai_item_id: int = None, rel_item_id: int = None,
                      item_status: int = None, item_type: int = None,
                      page_number: int = 1, page_size: int = 20,
                      on_offline_status: int = None) -> Dict[str, Any]:
        """
        查询商品列表

        Parameters:
        -----------
        kwai_item_id: int, optional
            快手商品ID
        rel_item_id: int, optional
            外部商品ID，如kwai_item_id未传，但rel_item_id传了，将会按照appkey+rel_item_id查询
        item_status: int, optional
            商品状态，1-正常（商品状态1是指未删除的商品）
        item_type: int, optional
            商品类型，1-自建商品
        page_number: int, optional
            页码，0 < 数值 < totalPage，默认为1
        page_size: int, optional
            每页数量，推荐值20，范围为10～100，默认为20
        on_offline_status: int, optional
            上下架条件，目前仅支持查询1（上架）商品

        Returns:
        --------
        Dict[str, Any]:
            API响应结果，包含商品列表数据
            
        Raises:
        -------
        ValueError:
            当参数无效时抛出异常
        """
        # 参数验证
        if kwai_item_id is not None and (not isinstance(kwai_item_id, int) or kwai_item_id <= 0):
            raise ValueError("kwai_item_id必须是正整数")
        
        if rel_item_id is not None and (not isinstance(rel_item_id, int) or rel_item_id <= 0):
            raise ValueError("rel_item_id必须是正整数")
        
        if item_status is not None and item_status not in [1]:
            raise ValueError("item_status目前只支持1（正常）")
        
        if item_type is not None and item_type not in [1]:
            raise ValueError("item_type目前只支持1（自建商品）")
            
        if not isinstance(page_number, int) or page_number <= 0:
            raise ValueError("page_number必须是正整数")
        
        if not isinstance(page_size, int) or page_size < 10 or page_size > 100:
            raise ValueError("page_size必须在10-100范围内")
        
        if on_offline_status is not None and on_offline_status not in [1]:
            raise ValueError("on_offline_status目前只支持1（上架）")

        # 构建请求参数
        params = {
            'pageNumber': page_number,
            'pageSize': page_size
        }
        
        # 添加可选参数
        if kwai_item_id is not None:
            params['kwaiItemId'] = kwai_item_id
        
        if rel_item_id is not None:
            params['relItemId'] = rel_item_id
        
        if item_status is not None:
            params['itemStatus'] = item_status
        
        if item_type is not None:
            params['itemType'] = item_type
        
        if on_offline_status is not None:
            params['onOfflineStatus'] = on_offline_status

        logger.info(f"查询商品列表: 页码={page_number}, 每页数量={page_size}")
        if kwai_item_id is not None:
            logger.info(f"查询指定商品ID: {kwai_item_id}")
        if rel_item_id is not None:
            logger.info(f"查询指定外部商品ID: {rel_item_id}")
        
        try:
            result = self._make_request('open.item.list.get', params)
            
            # 检查返回结果
            if result.get('result') == 1:
                logger.info(f"商品列表查询成功")
                return {
                    'success': True,
                    'message': '查询成功',
                    'data': result.get('data', {})
                }
            else:
                error_msg = result.get('error_msg', '未知错误')
                logger.error(f"商品列表查询失败: {error_msg}")
                return {
                    'success': False,
                    'message': error_msg,
                    'data': result
                }
                
        except Exception as e:
            logger.error(f"商品列表查询异常: {str(e)}")
            return {
                'success': False,
                'message': f'查询异常: {str(e)}',
                'data': None
            }

    def get_item_detail(self, kwai_item_id: int) -> Dict[str, Any]:
        """
        获取商品详情

        Parameters:
        -----------
        kwai_item_id: int
            快手商品ID（必须）

        Returns:
        --------
        Dict[str, Any]:
            API响应结果，包含商品详情数据
            
        Raises:
        -------
        ValueError:
            当参数无效时抛出异常
        """
        # 参数验证
        if not isinstance(kwai_item_id, int) or kwai_item_id <= 0:
            raise ValueError("kwai_item_id必须是正整数")

        # 构建请求参数
        params = {
            'kwaiItemId': kwai_item_id
        }

        # logger.info(f"查询商品详情: 商品ID={kwai_item_id}")  # 注释掉调试信息
        
        try:
            result = self._make_request('open.item.get', params)
            
            # 检查返回结果
            if result.get('result') == 1:
                return {
                    'success': True,
                    'message': '查询成功',
                    'data': result.get('data', {})
                }
            else:
                error_msg = result.get('error_msg', '未知错误')
                logger.error(f"商品详情查询失败: {error_msg}")
                return {
                    'success': False,
                    'message': error_msg,
                    'data': result
                }
                
        except Exception as e:
            logger.error(f"商品详情查询异常: {str(e)}")
            return {
                'success': False,
                'message': f'查询异常: {str(e)}',
                'data': None
            }

    def get_order_list(self, cursor: str = None, page_size: int = 20,
                       order_status: int = None, start_time: str = None,
                       end_time: str = None) -> Dict[str, Any]:
        """
        获取订单列表（游标分页）

        Parameters:
        -----------
        cursor: str, optional
            游标，用于分页查询，首次查询可不传
        page_size: int, optional
            每页数量，范围1-50，默认20（API文档限制最多50条）
        order_status: int, optional
            订单状态筛选：
            1-全部, 2-待付款, 3-待发货, 4-待收货（已发货）, 5-已收货, 6-交易成功, 7-已关闭
        start_time: str, optional
            开始时间，格式：yyyy-MM-dd HH:mm:ss（将转换为毫秒时间戳）
        end_time: str, optional
            结束时间，格式：yyyy-MM-dd HH:mm:ss（将转换为毫秒时间戳）

        Returns:
        --------
        Dict[str, Any]:
            API响应结果，包含订单列表数据

        Raises:
        -------
        ValueError:
            当参数无效时抛出异常
        """
        # 参数验证
        if page_size < 1 or page_size > 50:  # 修正：API文档限制最多50条
            raise ValueError("page_size必须在1-50范围内")

        # 修正：根据API文档，订单状态值范围调整
        if order_status is not None and order_status not in [1, 2, 3, 4, 5, 6, 7]:
            raise ValueError("order_status必须是1-7之间的整数")

        # 构建请求参数
        params = {
            'pageSize': page_size,
            'orderViewStatus': order_status if order_status is not None else 1,  # 默认查询全部订单
            'queryType': 1,  # 按创建时间查找
            'sort': 1  # 时间降序
        }

        # 添加可选参数
        if cursor is not None:
            params['cursor'] = cursor
        else:
            params['cursor'] = ""  # 首次查询传空字符串

        # 处理时间参数：转换为毫秒时间戳
        if start_time is not None and end_time is not None:
            try:
                from datetime import datetime

                # 解析时间字符串
                start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")

                # 转换为毫秒时间戳
                begin_time = int(start_dt.timestamp() * 1000)
                end_time_ms = int(end_dt.timestamp() * 1000)

                # 验证时间范围（不能超过7天）
                time_diff_days = (end_dt - start_dt).days
                if time_diff_days > 7:
                    raise ValueError("时间范围不能超过7天")

                # 验证不能查询90天前的数据
                from datetime import datetime, timedelta
                ninety_days_ago = datetime.now() - timedelta(days=90)
                if start_dt < ninety_days_ago:
                    raise ValueError("不能查询90天前的订单数据")

                params['beginTime'] = begin_time
                params['endTime'] = end_time_ms

                logger.info(f"时间参数转换: {start_time} -> {begin_time}, {end_time} -> {end_time_ms}")

            except ValueError as e:
                if "时间范围" in str(e) or "90天前" in str(e):
                    raise e
                else:
                    raise ValueError(f"时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式: {str(e)}")
            except Exception as e:
                raise ValueError(f"时间参数处理失败: {str(e)}")

        logger.info(f"查询订单列表: 页大小={page_size}, 订单状态={order_status}")
        if start_time and end_time:
            logger.info(f"时间范围: {start_time} 至 {end_time}")

        logger.info(f"API请求参数: {params}")

        try:
            result = self._make_request('open.order.cursor.list', params)

            # 检查返回结果 - 修正：根据API文档调整成功判断逻辑
            if result.get('result') == 1:
                logger.info(f"订单列表查询成功")
                return {
                    'success': True,
                    'message': '查询成功',
                    'data': result.get('data', {}),
                    'cursor': result.get('data', {}).get('cursor'),  # 下一页游标
                    'hasMore': result.get('data', {}).get('cursor') not in [None, '', 'nomore']  # 根据cursor判断是否有更多数据
                }
            else:
                error_msg = result.get('error_msg', '未知错误')
                logger.error(f"订单列表查询失败: {error_msg}")
                return {
                    'success': False,
                    'message': error_msg,
                    'data': result
                }

        except Exception as e:
            logger.error(f"订单列表查询异常: {str(e)}")
            return {
                'success': False,
                'message': f'查询异常: {str(e)}',
                'data': None
            }

    def get_all_orders(self, order_status: int = None, start_time: str = None,
                       end_time: str = None, max_pages: int = 50) -> Dict[str, Any]:
        """
        获取所有订单（自动分页）

        Parameters:
        -----------
        order_status: int, optional
            订单状态筛选：1-全部, 2-待付款, 3-待发货, 4-待收货, 5-已收货, 6-交易成功, 7-已关闭
        start_time: str, optional
            开始时间，格式：yyyy-MM-dd HH:mm:ss
        end_time: str, optional
            结束时间，格式：yyyy-MM-dd HH:mm:ss
        max_pages: int, optional
            最大页数限制，防止无限循环，默认50页

        Returns:
        --------
        Dict[str, Any]:
            包含所有订单数据的响应结果
        """
        all_orders = []
        cursor = ""  # 首次查询使用空字符串
        page_count = 0

        logger.info(f"开始获取所有订单，状态={order_status}, 时间范围={start_time} 至 {end_time}")

        try:
            while page_count < max_pages:
                # 获取当前页数据
                result = self.get_order_list(
                    cursor=cursor,
                    page_size=50,  # 修正：使用API允许的最大页大小50
                    order_status=order_status,
                    start_time=start_time,
                    end_time=end_time
                )

                if not result['success']:
                    return result

                # 获取订单数据 - 修正：根据API文档调整数据结构
                data = result['data']
                orders = data.get('orderList', [])  # 修正：API返回的是orderList而不是orders
                all_orders.extend(orders)

                page_count += 1
                logger.info(f"已获取第{page_count}页，本页{len(orders)}条订单，累计{len(all_orders)}条")

                # 检查是否有更多数据 - 修正：根据cursor判断
                cursor = data.get('cursor', '')
                if not cursor or cursor == "nomore":  # API文档说明：返回"nomore"表示到底
                    logger.info("已获取所有订单数据")
                    break

            logger.info(f"订单获取完成，共{len(all_orders)}条订单，{page_count}页")

            return {
                'success': True,
                'message': f'获取成功，共{len(all_orders)}条订单',
                'data': {
                    'orders': all_orders,
                    'total_count': len(all_orders),
                    'page_count': page_count
                }
            }

        except Exception as e:
            logger.error(f"获取所有订单异常: {str(e)}")
            return {
                'success': False,
                'message': f'获取异常: {str(e)}',
                'data': None
            }

    def get_error_code_description(self, error_code: str) -> str:
        """
        获取错误码描述

        Parameters:
        -----------
        error_code: str
            错误码

        Returns:
        --------
        str:
            错误码描述
        """
        error_codes = {
            '11': '服务端错误，请稍后重试',
            '14': '参数不能为空，请检查必填参数',
            '21': '定向人不能为空，请补充定向人信息',
            '300': '商品准入未通过，请重新编辑商品信息',
            '400': '请求参数非法，请检查',
            '2007': '商品当前状态不允许进行操作，请等待商品审核通过后再操作',
            '100002': '商品不存在，请确认参数是否正确',
            '100003': '商品id不合法，请确认参数是否正确',
            '100022': '上下架状态参数不合法，请检查参数信息',
            '100023': '商品尚未通过审核，请等待审核通过后再操作',
            '100038': '无法更新闪电购商品状态，请确认更新数据参数',
            '100065': '系统繁忙，请稍后重试',
            '100069': '店铺已关闭，无法上架商品'
        }

        return error_codes.get(error_code, f'未知错误码: {error_code}')

    def save_sample_rule(self, item_rules: list) -> Dict[str, Any]:
        """
        保存申样规则

        Parameters:
        -----------
        item_rules: list
            申样规则列表，每个元素包含：
            - item_id: int, 商品ID
            - sample_count: int, 申样数量

        Returns:
        --------
        Dict[str, Any]:
            API响应结果

        Raises:
        -------
        ValueError:
            当参数无效时抛出异常
        """
        # 参数验证
        if not isinstance(item_rules, list) or not item_rules:
            raise ValueError("item_rules必须是非空列表")

        # 构建申样规则数据
        rule_list = []
        for rule in item_rules:
            if not isinstance(rule, dict):
                raise ValueError("每个规则必须是字典类型")

            item_id = rule.get('item_id')
            sample_count = rule.get('sample_count', 5)  # 默认5个申样

            if not isinstance(item_id, (int, str)) or not item_id:
                raise ValueError("item_id不能为空")

            if not isinstance(sample_count, int) or sample_count <= 0:
                raise ValueError("sample_count必须是正整数")

            # 构建单个商品的申样规则
            item_rule = {
                "itemId": int(item_id),
                "itemRule": [
                    {
                        "quotaCondition": [
                            {
                                "quotaValue": str(sample_count),
                                "quotaName": "successOrderCount"  # 总结算订单量
                            }
                        ],
                        "applyType": 2  # 买样后返
                    }
                ]
            }
            rule_list.append(item_rule)

        # 构建请求参数
        params = {
            "rule": rule_list
        }

        logger.info(f"保存申样规则: 商品数量={len(rule_list)}")

        try:
            result = self._make_request('open.distribution.seller.sample.rule.save', params)

            # 检查返回结果
            if result.get('result') == 1:
                success_items = result.get('data', [])
                logger.info(f"申样规则保存成功: 成功设置{len(success_items)}个商品")
                return {
                    'success': True,
                    'message': '申样规则保存成功',
                    'data': {
                        'success_items': success_items,
                        'total_count': len(rule_list),
                        'success_count': len(success_items)
                    }
                }
            else:
                error_msg = result.get('error_msg', '未知错误')
                sub_msg = result.get('sub_msg', '')
                full_error = f"{error_msg} {sub_msg}".strip()
                logger.error(f"申样规则保存失败: {full_error}")
                return {
                    'success': False,
                    'message': full_error,
                    'data': result
                }

        except Exception as e:
            logger.error(f"申样规则保存异常: {str(e)}")
            return {
                'success': False,
                'message': f'保存异常: {str(e)}',
                'data': None
            }

    def create_distribution_plan(self, item_plans: list, plan_type: str = "ITEM_NORMAL") -> Dict[str, Any]:
        """
        创建分销计划

        Parameters:
        -----------
        item_plans: list
            商品计划列表，每个元素包含：
            - item_id: int, 商品ID
            - commission_rate: int, 佣金比例（百分比，0-90）
        plan_type: str
            计划类型，默认为 "ITEM_NORMAL"（商品普通计划）

        Returns:
        --------
        Dict[str, Any]:
            API响应结果

        Raises:
        -------
        ValueError:
            当参数无效时抛出异常
        """
        # 参数验证
        if not isinstance(item_plans, list) or not item_plans:
            raise ValueError("item_plans必须是非空列表")

        if plan_type not in ["ITEM_NORMAL", "ITEM_EXCLUSIVE", "ITEM_ORIENTATION"]:
            raise ValueError("plan_type必须是ITEM_NORMAL、ITEM_EXCLUSIVE或ITEM_ORIENTATION")

        # 按佣金比例分组商品（相同佣金比例的商品可以一起创建计划）
        commission_groups = {}
        for plan in item_plans:
            if not isinstance(plan, dict):
                raise ValueError("每个计划必须是字典类型")

            item_id = plan.get('item_id')
            commission_rate = plan.get('commission_rate', 20)  # 默认20%

            if not isinstance(item_id, (int, str)) or not item_id:
                raise ValueError("item_id不能为空")

            if not isinstance(commission_rate, int) or commission_rate < 0 or commission_rate > 90:
                raise ValueError("commission_rate必须是0-90之间的整数")

            # 按佣金比例分组
            if commission_rate not in commission_groups:
                commission_groups[commission_rate] = []
            commission_groups[commission_rate].append(int(item_id))

        logger.info(f"创建分销计划: 计划类型={plan_type}, 佣金分组={len(commission_groups)}个")

        all_results = []
        total_success = 0
        total_failed = 0

        try:
            # 为每个佣金比例分组创建计划
            for commission_rate, item_ids in commission_groups.items():
                # 分批处理（每批最多20个商品）
                batch_size = 20
                for i in range(0, len(item_ids), batch_size):
                    batch_items = item_ids[i:i + batch_size]

                    # 构建请求参数
                    params = {
                        "planCreateType": plan_type
                    }

                    if plan_type == "ITEM_NORMAL":
                        params["normalPlanParam"] = {
                            "itemIds": batch_items,
                            "commissionRate": commission_rate
                        }

                    logger.info(f"创建分销计划批次: 佣金{commission_rate}%, 商品{len(batch_items)}个")

                    result = self._make_request('open.distribution.plan.create', params)

                    # 处理结果
                    if result.get('result') == 1:
                        total_success += len(batch_items)
                        logger.info(f"分销计划创建成功: 佣金{commission_rate}%, 商品{len(batch_items)}个")
                    else:
                        total_failed += len(batch_items)
                        error_msg = result.get('error_msg', '未知错误')
                        logger.error(f"分销计划创建失败: 佣金{commission_rate}%, 错误: {error_msg}")

                    all_results.append({
                        'commission_rate': commission_rate,
                        'item_count': len(batch_items),
                        'result': result
                    })

            # 汇总结果
            if total_failed == 0:
                return {
                    'success': True,
                    'message': '分销计划创建成功',
                    'data': {
                        'total_success': total_success,
                        'total_failed': total_failed,
                        'details': all_results
                    }
                }
            else:
                return {
                    'success': False,
                    'message': f'部分分销计划创建失败: 成功{total_success}个, 失败{total_failed}个',
                    'data': {
                        'total_success': total_success,
                        'total_failed': total_failed,
                        'details': all_results
                    }
                }

        except Exception as e:
            logger.error(f"创建分销计划异常: {str(e)}")
            return {
                'success': False,
                'message': f'创建异常: {str(e)}',
                'data': None
            }

    def update_distribution_plan(self, plan_updates: list) -> Dict[str, Any]:
        """
        更新分销计划佣金比例

        Parameters:
        -----------
        plan_updates: list
            分销计划更新列表，每个元素包含：
            - item_id: int, 商品ID
            - commission_rate: int, 新的佣金比例（0-90）

        Returns:
        --------
        Dict[str, Any]:
            API响应结果

        Raises:
        -------
        ValueError:
            当参数无效时抛出异常
        """
        # 参数验证
        if not isinstance(plan_updates, list) or not plan_updates:
            raise ValueError("plan_updates必须是非空列表")

        # 按佣金比例分组商品（相同佣金比例的商品可以一起更新）
        commission_groups = {}
        for update in plan_updates:
            if not isinstance(update, dict):
                raise ValueError("每个更新必须是字典类型")

            item_id = update.get('item_id')
            commission_rate = update.get('commission_rate', 20)  # 默认20%

            if not isinstance(item_id, (int, str)) or not item_id:
                raise ValueError("item_id不能为空")

            if not isinstance(commission_rate, int) or commission_rate < 0 or commission_rate > 90:
                raise ValueError("commission_rate必须是0-90之间的整数")

            # 按佣金比例分组
            if commission_rate not in commission_groups:
                commission_groups[commission_rate] = []
            commission_groups[commission_rate].append(int(item_id))

        logger.info(f"更新分销计划: 佣金分组={len(commission_groups)}个")

        all_results = []
        total_success = 0
        total_failed = 0

        try:
            # 为每个佣金比例分组更新计划
            for commission_rate, item_ids in commission_groups.items():
                # 分批处理（每批最多20个商品）
                batch_size = 20
                for i in range(0, len(item_ids), batch_size):
                    batch_items = item_ids[i:i + batch_size]

                    # 构建请求参数
                    params = {
                        "itemIds": batch_items,
                        "commissionRate": commission_rate
                    }

                    logger.info(f"更新分销计划批次: 佣金{commission_rate}%, 商品{len(batch_items)}个")

                    result = self._make_request('open.distribution.plan.update', params)

                    # 处理结果
                    if result.get('result') == 1:
                        total_success += len(batch_items)
                        logger.info(f"分销计划更新成功: 佣金{commission_rate}%, 商品{len(batch_items)}个")
                    else:
                        total_failed += len(batch_items)
                        error_msg = result.get('error_msg', '未知错误')
                        logger.error(f"分销计划更新失败: 佣金{commission_rate}%, 错误: {error_msg}")

                    all_results.append({
                        'commission_rate': commission_rate,
                        'item_count': len(batch_items),
                        'result': result
                    })

            # 汇总结果
            if total_failed == 0:
                return {
                    'success': True,
                    'message': '分销计划更新成功',
                    'data': {
                        'total_success': total_success,
                        'total_failed': total_failed,
                        'details': all_results
                    }
                }
            else:
                return {
                    'success': False,
                    'message': f'部分分销计划更新失败: 成功{total_success}个, 失败{total_failed}个',
                    'data': {
                        'total_success': total_success,
                        'total_failed': total_failed,
                        'details': all_results
                    }
                }

        except Exception as e:
            logger.error(f"更新分销计划异常: {str(e)}")
            return {
                'success': False,
                'message': f'更新异常: {str(e)}',
                'data': None
            }

    def update_distribution_plan(self, plan_id: int, update_type: str, **kwargs) -> Dict[str, Any]:
        """
        更新分销计划

        Parameters:
        -----------
        plan_id: int
            计划ID
        update_type: str
            更新的信息类型：
            - UPDATE_PLAN_STATUS: 更新计划状态
            - UPDATE_NORMAL_COMMISSION: 更新普通计划佣金
            - UPDATE_ORIENTATION_COMMISSION: 更新计划定向/专属佣金
        **kwargs:
            根据update_type的不同，需要传入不同的参数：
            - status: int, 更新后的计划状态（1-开启；3-关闭）
            - commission_rate: int, 更新后的佣金比例（0-90）
            - commission_id: int, 佣金ID（定向计划使用）

        Returns:
        --------
        Dict[str, Any]:
            API响应结果

        Raises:
        -------
        ValueError:
            当参数无效时抛出异常
        """
        # 参数验证
        if not isinstance(plan_id, int) or plan_id <= 0:
            raise ValueError("plan_id必须是正整数")

        valid_update_types = ["UPDATE_PLAN_STATUS", "UPDATE_NORMAL_COMMISSION", "UPDATE_ORIENTATION_COMMISSION"]
        if update_type not in valid_update_types:
            raise ValueError(f"update_type必须是{valid_update_types}中的一个")

        # 构建请求参数
        params = {
            "planId": plan_id,
            "updateType": update_type
        }

        # 根据更新类型添加相应参数
        if update_type == "UPDATE_PLAN_STATUS":
            status = kwargs.get('status')
            if status not in [1, 3]:
                raise ValueError("status必须是1（开启）或3（关闭）")

            params["updatePlanStatusParam"] = {
                "planId": plan_id,
                "status": status
            }

        elif update_type == "UPDATE_NORMAL_COMMISSION":
            commission_rate = kwargs.get('commission_rate')
            if not isinstance(commission_rate, int) or commission_rate < 0 or commission_rate > 90:
                raise ValueError("commission_rate必须是0-90之间的整数")

            params["updateNormalCommissionParam"] = {
                "planId": plan_id,
                "commissionRate": commission_rate
            }

        elif update_type == "UPDATE_ORIENTATION_COMMISSION":
            commission_id = kwargs.get('commission_id')
            commission_rate = kwargs.get('commission_rate')

            if not isinstance(commission_id, int) or commission_id <= 0:
                raise ValueError("commission_id必须是正整数")
            if not isinstance(commission_rate, int) or commission_rate < 0 or commission_rate > 90:
                raise ValueError("commission_rate必须是0-90之间的整数")

            params["updateOrientationCommissionParam"] = {
                "commissionId": commission_id,
                "commissionRate": commission_rate
            }

        logger.info(f"更新分销计划: 计划ID={plan_id}, 更新类型={update_type}")

        try:
            result = self._make_request('open.distribution.plan.update', params)

            # 处理结果
            if result.get('result') == 1:
                logger.info(f"分销计划更新成功: 计划ID={plan_id}")
                return {
                    'success': True,
                    'message': '分销计划更新成功',
                    'data': result
                }
            else:
                error_msg = result.get('error_msg', '未知错误')
                logger.error(f"分销计划更新失败: 计划ID={plan_id}, 错误: {error_msg}")
                return {
                    'success': False,
                    'message': f'分销计划更新失败: {error_msg}',
                    'data': result
                }

        except Exception as e:
            logger.error(f"更新分销计划异常: {str(e)}")
            return {
                'success': False,
                'message': f'更新异常: {str(e)}',
                'data': None
            }

    def batch_update_commission(self, plan_updates: list) -> Dict[str, Any]:
        """
        批量更新分销计划佣金比例

        Parameters:
        -----------
        plan_updates: list
            分销计划更新列表，每个元素包含：
            - plan_id: int, 计划ID
            - commission_rate: int, 新的佣金比例（0-90）

        Returns:
        --------
        Dict[str, Any]:
            批量更新结果

        Raises:
        -------
        ValueError:
            当参数无效时抛出异常
        """
        # 参数验证
        if not isinstance(plan_updates, list) or not plan_updates:
            raise ValueError("plan_updates必须是非空列表")

        logger.info(f"批量更新分销计划佣金: 计划数量={len(plan_updates)}")

        all_results = []
        total_success = 0
        total_failed = 0

        try:
            for update in plan_updates:
                if not isinstance(update, dict):
                    raise ValueError("每个更新必须是字典类型")

                plan_id = update.get('plan_id')
                commission_rate = update.get('commission_rate')

                if not isinstance(plan_id, int) or plan_id <= 0:
                    raise ValueError("plan_id必须是正整数")

                if not isinstance(commission_rate, int) or commission_rate < 0 or commission_rate > 90:
                    raise ValueError("commission_rate必须是0-90之间的整数")

                # 调用单个更新接口
                result = self.update_distribution_plan(
                    plan_id=plan_id,
                    update_type="UPDATE_NORMAL_COMMISSION",
                    commission_rate=commission_rate
                )

                if result.get('success'):
                    total_success += 1
                else:
                    total_failed += 1

                all_results.append({
                    'plan_id': plan_id,
                    'commission_rate': commission_rate,
                    'result': result
                })

                # 避免请求过于频繁
                time.sleep(0.1)

            # 汇总结果
            if total_failed == 0:
                return {
                    'success': True,
                    'message': '批量更新分销计划佣金成功',
                    'data': {
                        'total_success': total_success,
                        'total_failed': total_failed,
                        'details': all_results
                    }
                }
            else:
                return {
                    'success': False,
                    'message': f'部分分销计划更新失败: 成功{total_success}个, 失败{total_failed}个',
                    'data': {
                        'total_success': total_success,
                        'total_failed': total_failed,
                        'details': all_results
                    }
                }

        except Exception as e:
            logger.error(f"批量更新分销计划异常: {str(e)}")
            return {
                'success': False,
                'message': f'批量更新异常: {str(e)}',
                'data': None
            }

    def update_distribution_plan_commission(self, product_id: int, commission_rate: int) -> Dict[str, Any]:
        """
        更新单个商品的分销计划佣金比例

        Parameters:
        -----------
        product_id: int
            商品ID
        commission_rate: int
            新的佣金比例（API格式，已乘以10，如20%传入200）

        Returns:
        --------
        Dict[str, Any]:
            API响应结果，包含result字段（1表示成功）
        """
        try:
            # 参数验证
            if not isinstance(product_id, (int, str)) or not product_id:
                raise ValueError("product_id不能为空")

            if not isinstance(commission_rate, int) or commission_rate < 0 or commission_rate > 900:
                raise ValueError("commission_rate必须是0-900之间的整数（API格式）")

            # 构建请求参数
            params = {
                "itemIds": [int(product_id)],
                "commissionRate": commission_rate
            }

            logger.info(f"更新商品佣金: 商品ID={product_id}, 佣金={commission_rate/10}%")

            # 调用API
            result = self._make_request('open.distribution.plan.update', params)

            # 处理结果
            if result.get('result') == 1:
                logger.info(f"商品佣金更新成功: 商品ID={product_id}")
                return {
                    'result': 1,
                    'success': True,
                    'message': '佣金更新成功'
                }
            else:
                error_msg = result.get('error_msg', '未知错误')
                logger.error(f"商品佣金更新失败: 商品ID={product_id}, 错误: {error_msg}")
                return {
                    'result': 0,
                    'success': False,
                    'error_msg': error_msg
                }

        except Exception as e:
            logger.error(f"更新商品佣金异常: 商品ID={product_id}, 异常: {str(e)}")
            return {
                'result': 0,
                'success': False,
                'error_msg': f'更新异常: {str(e)}'
            }

    def get_category_list(self, parent_id: int = 0) -> Dict[str, Any]:
        """
        获取类目列表 - 快手分销公共类目接口

        根据快手开放平台文档：open.distribution.public.category.list

        Parameters:
        -----------
        parent_id: int, optional
            父类目ID，默认为0获取一级类目

        Returns:
        --------
        Dict[str, Any]:
            API响应结果，包含类目列表数据

        Raises:
        -------
        ValueError:
            当参数无效时抛出异常
        """
        # 参数验证
        if not isinstance(parent_id, int) or parent_id < 0:
            raise ValueError("parent_id必须是非负整数")

        # 构建请求参数
        params = {
            'parentId': parent_id
        }

        logger.info(f"获取类目列表: 父类目ID={parent_id}")

        try:
            result = self._make_request('open.distribution.public.category.list', params)

            # 检查返回结果
            if result.get('result') == 1:
                logger.info(f"类目列表获取成功: 父类目ID={parent_id}")
                return {
                    'success': True,
                    'message': '获取成功',
                    'data': result.get('data', [])
                }
            else:
                error_msg = result.get('error_msg', '未知错误')
                logger.error(f"类目列表获取失败: {error_msg}")
                return {
                    'success': False,
                    'message': error_msg,
                    'data': result
                }

        except Exception as e:
            logger.error(f"类目列表获取异常: {str(e)}")
            return {
                'success': False,
                'message': f'获取异常: {str(e)}',
                'data': None
            }

    def get_all_categories(self, access_token: str = None) -> Dict[str, Any]:
        """
        获取所有类目数据（递归获取完整类目树）

        这个方法会递归调用get_category_list来构建完整的类目树结构

        Parameters:
        -----------
        access_token: str, optional
            访问令牌，如果提供则会更新实例的access_token

        Returns:
        --------
        Dict[str, Any]:
            包含完整类目树的响应结果
        """
        try:
            # 如果提供了access_token，更新实例的token
            if access_token:
                self.access_token = access_token

            logger.info("开始获取完整类目树...")

            # 获取一级类目
            first_level_result = self.get_category_list(parent_id=0)

            if not first_level_result['success']:
                return first_level_result

            first_level_categories = first_level_result['data']
            logger.info(f"获取到{len(first_level_categories)}个一级类目")

            # 递归获取所有子类目
            all_categories = []

            # 防止无限递归的已处理类目集合
            processed_categories = set()

            def fetch_children_recursive(categories, level=1):
                """递归获取子类目（防止无限递归）"""
                for category in categories:
                    category_id = category.get('categoryId')
                    category_name = category.get('categoryName', '')

                    # 防止无限递归：检查是否已经处理过这个类目
                    if category_id in processed_categories:
                        category['children'] = []
                        continue

                    # 标记为已处理
                    processed_categories.add(category_id)

                    # 添加层级信息
                    category['level'] = level
                    all_categories.append(category)

                    logger.debug(f"处理{level}级类目: {category_name} (ID: {category_id})")

                    # 限制递归深度，防止过深的递归
                    if level >= 5:
                        category['children'] = []
                        continue

                    # 获取子类目
                    if category_id:
                        children_result = self.get_category_list(parent_id=category_id)
                        if children_result['success'] and children_result['data']:
                            children = children_result['data']
                            logger.debug(f"类目 {category_name} 有{len(children)}个子类目")

                            # 将子类目添加到当前类目
                            category['children'] = children

                            # 递归处理子类目
                            fetch_children_recursive(children, level + 1)
                        else:
                            category['children'] = []
                    else:
                        category['children'] = []

            # 开始递归获取
            fetch_children_recursive(first_level_categories)

            logger.info(f"完整类目树构建完成，共{len(all_categories)}个类目")

            # 构建返回结果，兼容原有格式
            result = {
                'success': True,
                'cats_v2': first_level_categories,  # 新版类目树（带层级结构）
                'cats': all_categories,  # 扁平化的所有类目
                'total_count': len(all_categories)
            }

            return result

        except Exception as e:
            logger.error(f"获取完整类目树异常: {str(e)}")
            return {
                'success': False,
                'errmsg': f'获取异常: {str(e)}',
                'cats_v2': [],
                'cats': []
            }

    def get_freight_template_list(self, access_token: str = None) -> Dict[str, Any]:
        """
        获取运费模板列表

        Parameters:
        -----------
        access_token: str, optional
            访问令牌，如果提供则会更新实例的access_token

        Returns:
        --------
        Dict[str, Any]:
            包含运费模板列表的响应结果
        """
        try:
            # 如果提供了access_token，更新实例的token
            if access_token:
                self.access_token = access_token

            logger.info("获取运费模板列表...")

            # 构建请求参数（运费模板接口通常不需要额外参数）
            params = {}

            # 调用API（这里使用一个通用的运费模板接口名，可能需要根据实际文档调整）
            result = self._make_request('open.freight.template.list', params)

            # 检查返回结果
            if result.get('result') == 1:
                logger.info("运费模板列表获取成功")
                return {
                    'success': True,
                    'message': '获取成功',
                    'data': result.get('data', [])
                }
            else:
                error_msg = result.get('error_msg', '未知错误')
                # 如果是服务器内部错误，降级为debug级别，避免用户看到错误信息
                if '[1000]' in error_msg or 'internal server error' in error_msg.lower():
                    logger.debug(f"运费模板列表获取失败（服务器内部错误）: {error_msg}")
                else:
                    logger.error(f"运费模板列表获取失败: {error_msg}")
                return {
                    'success': False,
                    'message': error_msg,
                    'data': []
                }

        except Exception as e:
            logger.error(f"运费模板列表获取异常: {str(e)}")
            return {
                'success': False,
                'message': f'获取异常: {str(e)}',
                'data': []
            }

# 使用示例
if __name__ == "__main__":
    # 初始化API客户端
    api = KuaishouAPI()
    
    # 示例：商品上架
    # result = api.shelf_item(2342352324)
    # print(f"上架结果: {result}")
    
    # 示例：商品下架
    # result = api.unshelf_item(2342352324)
    # print(f"下架结果: {result}")
    
    # 示例：批量操作
    # operations = [
    #     {'item_id': 2342352324, 'operation': 'shelf'},
    #     {'item_id': 2342352325, 'operation': 'unshelf'}
    # ]
    # result = api.batch_shelf_operation(operations)
    # print(f"批量操作结果: {result}")
    
    # 示例：查询商品列表
    # result = api.get_item_list(page_number=1, page_size=20)
    # print(f"商品列表查询结果: {result}")
    
    # 示例：查询指定商品
    # result = api.get_item_list(kwai_item_id=401936319911)
    # print(f"指定商品查询结果: {result}")
    
    # 示例：查询上架商品列表
    # result = api.get_item_list(item_status=1, on_offline_status=1, page_size=10)
    # print(f"上架商品列表查询结果: {result}")
    
    # 示例：查询商品详情
    # result = api.get_item_detail(kwai_item_id=401936318906)
    # print(f"商品详情查询结果: {result}")
    
    print("快手API模块加载完成") 
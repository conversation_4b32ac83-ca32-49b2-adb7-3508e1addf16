#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手小店管理系统 - 专用打包工具
带图形界面的PyInstaller打包脚本
"""

import sys
import os
import subprocess
import threading
import time
import requests
import zipfile
import shutil
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QPushButton, QLabel, QTextEdit, QProgressBar,
                             QComboBox, QCheckBox, QGroupBox, QGridLayout, QFileDialog,
                             QMessageBox, QFrame, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon, QPixmap

class UPXDownloadThread(QThread):
    """UPX下载线程"""
    progress_signal = pyqtSignal(str)  # 下载进度信息
    finished_signal = pyqtSignal(bool, str)  # 完成信号(成功, 消息)

    def __init__(self):
        super().__init__()
        self.upx_url = "https://github.com/upx/upx/releases/download/v4.2.2/upx-4.2.2-win64.zip"
        self.tool_dir = os.path.join("ksxiaodian", "tool")

    def run(self):
        """执行UPX下载"""
        try:
            self.progress_signal.emit("🌐 开始下载UPX工具...")

            # 确保tool目录存在
            os.makedirs(self.tool_dir, exist_ok=True)

            # 下载UPX
            self.progress_signal.emit("📥 正在从GitHub下载UPX...")
            response = requests.get(self.upx_url, stream=True, timeout=30)
            response.raise_for_status()

            # 保存zip文件
            zip_path = os.path.join(self.tool_dir, "upx.zip")
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0

            with open(zip_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        if total_size > 0:
                            percent = (downloaded / total_size) * 100
                            self.progress_signal.emit(f"📥 下载进度: {percent:.1f}%")

            self.progress_signal.emit("📦 正在解压UPX工具...")

            # 解压zip文件
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # 查找upx.exe文件
                for file_info in zip_ref.filelist:
                    if file_info.filename.endswith('upx.exe'):
                        # 提取upx.exe到tool目录
                        with zip_ref.open(file_info) as source:
                            upx_exe_path = os.path.join(self.tool_dir, "upx.exe")
                            with open(upx_exe_path, 'wb') as target:
                                shutil.copyfileobj(source, target)
                        break

            # 删除zip文件
            os.remove(zip_path)

            # 验证下载
            upx_exe_path = os.path.join(self.tool_dir, "upx.exe")
            if os.path.exists(upx_exe_path):
                self.progress_signal.emit("✅ UPX工具下载完成！")
                self.finished_signal.emit(True, "UPX工具下载成功")
            else:
                self.finished_signal.emit(False, "UPX工具解压失败")

        except requests.RequestException as e:
            self.progress_signal.emit(f"❌ 网络错误: {str(e)}")
            self.finished_signal.emit(False, f"下载失败: {str(e)}")
        except Exception as e:
            self.progress_signal.emit(f"❌ 下载出错: {str(e)}")
            self.finished_signal.emit(False, f"下载出错: {str(e)}")

class PackageThread(QThread):
    """打包线程"""
    progress_signal = pyqtSignal(str)  # 进度信息
    finished_signal = pyqtSignal(bool, str)  # 完成信号(成功, 消息)
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        
    def run(self):
        """执行打包"""
        try:
            self.progress_signal.emit("🚀 开始打包快手小店管理系统...")
            
            # 构建PyInstaller命令
            cmd = self.build_command()
            self.progress_signal.emit(f"📝 执行命令: {' '.join(cmd[:5])}...")
            
            # 执行打包命令 - 使用更安全的编码处理
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=False,  # 使用字节模式
                cwd=os.getcwd()
            )

            # 实时输出进度 - 手动处理编码
            while True:
                output_bytes = process.stdout.readline()
                if output_bytes == b'' and process.poll() is not None:
                    break
                if output_bytes:
                    try:
                        # 尝试UTF-8解码
                        output = output_bytes.decode('utf-8').strip()
                    except UnicodeDecodeError:
                        try:
                            # 尝试GBK解码（Windows中文）
                            output = output_bytes.decode('gbk').strip()
                        except UnicodeDecodeError:
                            # 最后使用latin-1（不会失败）
                            output = output_bytes.decode('latin-1').strip()

                    if output:
                        self.progress_signal.emit(output)
            
            # 检查结果
            return_code = process.poll()
            if return_code == 0:
                self.progress_signal.emit("✅ 打包成功完成！")
                self.finished_signal.emit(True, "打包成功！")
            else:
                self.progress_signal.emit("❌ 打包失败！")
                self.finished_signal.emit(False, f"打包失败，返回码: {return_code}")
                
        except Exception as e:
            self.progress_signal.emit(f"❌ 打包过程出错: {str(e)}")
            self.finished_signal.emit(False, f"打包出错: {str(e)}")
    
    def build_command(self):
        """构建PyInstaller命令"""
        cmd = ["pyinstaller"]
        
        # 基本参数
        cmd.extend([
            f"--name={self.config['name']}",
            f"--distpath={self.config['output_dir']}",
            "--clean"
        ])
        
        # 打包模式
        if self.config['mode'] == 'onefile':
            cmd.append("--onefile")
        else:
            cmd.append("--onedir")
        
        # 控制台选项
        if not self.config['show_console']:
            cmd.append("--noconsole")

        # UPX压缩选项 - 优化配置
        if self.config['enable_upx'] and self.config.get('upx_path'):
            # 排除不适合压缩的文件，避免体积增大
            excluded_files = [
                "vcruntime140.dll",
                "msvcp140.dll",
                "api-ms-win-*.dll",
                "ucrtbase.dll",
                "kernel32.dll",
                "user32.dll",
                "advapi32.dll",
                "shell32.dll",
                "ole32.dll",
                "oleaut32.dll",
                "comctl32.dll",
                "comdlg32.dll",
                "gdi32.dll",
                "winmm.dll",
                "wsock32.dll",
                "ws2_32.dll",
                "version.dll",
                "imm32.dll",
                "msvcr*.dll",
                "mfc*.dll",
                "Qt5Core.dll",
                "Qt5Gui.dll",
                "Qt5Widgets.dll",
                "Qt5Network.dll"
            ]

            # 添加排除参数
            for exclude_file in excluded_files:
                cmd.append(f"--upx-exclude={exclude_file}")

            # 设置UPX路径
            if os.path.exists(self.config['upx_path']):
                upx_dir = os.path.dirname(os.path.abspath(self.config['upx_path']))
                cmd.append(f"--upx-dir={upx_dir}")
            else:
                cmd.append("--upx-dir=.")
        else:
            cmd.append("--noupx")  # 禁用UPX压缩

        # 图标
        if self.config['icon_path'] and os.path.exists(self.config['icon_path']):
            cmd.append(f"--icon={self.config['icon_path']}")
        
        # 数据文件
        for data_file in self.config['data_files']:
            cmd.append(f"--add-data={data_file}")
        
        # 隐藏导入
        for hidden_import in self.config['hidden_imports']:
            cmd.append(f"--hidden-import={hidden_import}")
        
        # 主文件
        cmd.append(self.config['main_file'])
        
        return cmd

class KuaishouPackageTool(QMainWindow):
    """快手打包工具主窗口"""
    
    def __init__(self):
        super().__init__()
        self.package_thread = None
        self.init_ui()
        self.load_default_config()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("快手小店管理系统 - 专用打包工具")
        self.setFixedSize(800, 700)
        self.setWindowIcon(self.create_icon())
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f7fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #d1d5db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #374151;
            }
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
            QPushButton:pressed {
                background-color: #1d4ed8;
            }
            QPushButton:disabled {
                background-color: #9ca3af;
            }
            QComboBox, QCheckBox {
                padding: 4px;
                font-size: 12px;
            }
            QTextEdit {
                border: 1px solid #d1d5db;
                border-radius: 6px;
                background-color: #1f2937;
                color: #f9fafb;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
            }
            QProgressBar {
                border: 1px solid #d1d5db;
                border-radius: 6px;
                text-align: center;
                background-color: #f3f4f6;
            }
            QProgressBar::chunk {
                background-color: #10b981;
                border-radius: 5px;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("🚀 快手小店管理系统打包工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #1f2937;
            padding: 10px;
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        """)
        layout.addWidget(title_label)
        
        # 配置区域
        config_group = QGroupBox("📋 打包配置")
        config_layout = QGridLayout(config_group)
        
        # 打包模式
        config_layout.addWidget(QLabel("打包模式:"), 0, 0)
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["单文件模式 (推荐)", "文件夹模式"])
        config_layout.addWidget(self.mode_combo, 0, 1)
        
        # 显示控制台
        self.console_check = QCheckBox("显示控制台窗口")
        config_layout.addWidget(self.console_check, 0, 2)

        # 压缩选项
        self.compress_check = QCheckBox("启用UPX压缩 (智能压缩)")
        self.compress_check.setToolTip("智能UPX压缩：自动排除不适合压缩的系统DLL，避免体积增大")
        config_layout.addWidget(self.compress_check, 0, 3)

        # UPX状态检测（延迟到日志组件创建后）
        
        # 输出目录
        config_layout.addWidget(QLabel("输出目录:"), 1, 0)
        self.output_dir_btn = QPushButton("选择输出目录")
        self.output_dir_btn.clicked.connect(self.select_output_dir)
        config_layout.addWidget(self.output_dir_btn, 1, 1, 1, 2)
        
        layout.addWidget(config_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.package_btn = QPushButton("🎯 开始打包")
        self.package_btn.setFixedSize(120, 40)
        self.package_btn.clicked.connect(self.start_package)
        button_layout.addWidget(self.package_btn)

        self.stop_btn = QPushButton("⏹️ 停止打包")
        self.stop_btn.setFixedSize(120, 40)
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.stop_package)
        button_layout.addWidget(self.stop_btn)

        # 添加压缩建议按钮
        self.advice_btn = QPushButton("💡 压缩建议")
        self.advice_btn.setFixedSize(120, 40)
        self.advice_btn.clicked.connect(self.show_compression_advice)
        button_layout.addWidget(self.advice_btn)

        # 添加下载UPX按钮
        self.download_upx_btn = QPushButton("📥 下载UPX")
        self.download_upx_btn.setFixedSize(120, 40)
        self.download_upx_btn.clicked.connect(self.download_upx)
        button_layout.addWidget(self.download_upx_btn)

        # 添加打开文件夹按钮
        self.open_folder_btn = QPushButton("📂 打开文件夹")
        self.open_folder_btn.setFixedSize(120, 40)
        self.open_folder_btn.clicked.connect(self.open_output_folder)
        button_layout.addWidget(self.open_folder_btn)

        # 添加体积优化按钮
        self.optimize_btn = QPushButton("🔧 体积优化")
        self.optimize_btn.setFixedSize(120, 40)
        self.optimize_btn.clicked.connect(self.show_size_optimization)
        button_layout.addWidget(self.optimize_btn)

        # 添加清理临时文件按钮
        self.cleanup_btn = QPushButton("🗑️ 清理文件")
        self.cleanup_btn.setFixedSize(120, 40)
        self.cleanup_btn.clicked.connect(self.manual_cleanup)
        button_layout.addWidget(self.cleanup_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 日志区域
        log_group = QGroupBox("📝 打包日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMinimumHeight(300)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)

        # 在所有UI组件创建完成后检测UPX
        self.check_upx_availability()
        
    def create_icon(self):
        """创建窗口图标"""
        try:
            # 使用正确的图标路径
            icon_path = os.path.join("ksxiaodian", "config", "imges", "logo.ico")
            if os.path.exists(icon_path):
                return QIcon(icon_path)
        except:
            pass
        return QIcon()
    
    def load_default_config(self):
        """加载默认配置"""
        # 输出目录默认在脚本运行目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.output_dir = os.path.join(script_dir, "dist")
        self.log_text.append("✅ 默认配置已加载")
        self.log_text.append(f"📁 脚本运行目录: {script_dir}")
        self.log_text.append(f"📁 输出目录: {self.output_dir}")

        # 显示检测到的文件（只显示找到的文件，不显示错误）
        self.log_text.append("🔍 检测项目文件...")

        # 检测主文件
        if os.path.exists(os.path.join("ksxiaodian", "快手小店管理系统.py")):
            self.log_text.append("✅ 找到主文件: ksxiaodian/快手小店管理系统.py")

        # 检测配置目录
        if os.path.exists(os.path.join("ksxiaodian", "config")):
            self.log_text.append("✅ 找到配置目录: ksxiaodian/config")

        # 检测工具目录
        if os.path.exists(os.path.join("ksxiaodian", "tool")):
            self.log_text.append("✅ 找到工具目录: ksxiaodian/tool")

        # 检测图标文件
        if os.path.exists(os.path.join("ksxiaodian", "config", "imges", "logo.ico")):
            self.log_text.append("✅ 找到图标文件: ksxiaodian/config/imges/logo.ico")
        
    def select_output_dir(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录", self.output_dir)
        if dir_path:
            self.output_dir = dir_path
            self.log_text.append(f"📁 输出目录已更改: {self.output_dir}")

    def start_package(self):
        """开始打包"""
        if self.package_thread and self.package_thread.isRunning():
            QMessageBox.warning(self, "警告", "打包正在进行中，请等待完成！")
            return

        # 检查主文件（使用正确的路径）
        main_file = os.path.join("ksxiaodian", "快手小店管理系统.py")
        if not os.path.exists(main_file):
            QMessageBox.critical(self, "错误",
                f"找不到主文件: {main_file}\n请确保文件存在")
            return

        # 准备配置
        config = self.prepare_config(main_file)

        # UPX压缩警告检查
        if config['enable_upx'] and not hasattr(self, 'upx_available'):
            self.check_upx_availability()

        if config['enable_upx'] and not getattr(self, 'upx_available', False):
            reply = QMessageBox.question(
                self, "UPX未安装",
                "检测到您启用了UPX压缩，但系统中未安装UPX工具。\n\n"
                "是否继续打包（不使用压缩）？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                self.package_btn.setEnabled(True)
                self.stop_btn.setEnabled(False)
                return
            else:
                config['enable_upx'] = False  # 禁用压缩

        # 清空日志
        self.log_text.clear()
        self.log_text.append("🎯 准备开始打包...")
        self.log_text.append(f"📦 打包模式: {config['mode']}")
        self.log_text.append(f"🖥️ 显示控制台: {'是' if config['show_console'] else '否'}")
        self.log_text.append(f"🗜️ UPX压缩: {'启用' if config['enable_upx'] else '禁用'}")
        if config['icon_path']:
            self.log_text.append(f"🎨 图标文件: {config['icon_path']}")
        else:
            self.log_text.append("🎨 图标文件: 未找到")

        # 显示数据文件详情，排查体积增大原因
        self.log_text.append("📋 数据文件分析:")
        data_files = config['data_files']
        if not data_files:
            self.log_text.append("  ⚠️ 未找到任何数据文件")
        else:
            total_size = 0
            for data_file in data_files:
                source_path = data_file.split(';')[0] if ';' in data_file else data_file.split(':')[0]
                if os.path.exists(source_path):
                    if os.path.isfile(source_path):
                        size = os.path.getsize(source_path) / (1024 * 1024)  # MB
                        total_size += size
                        self.log_text.append(f"  📄 {source_path}: {size:.2f} MB")
                    elif os.path.isdir(source_path):
                        dir_size = self.get_directory_size(source_path)
                        total_size += dir_size
                        self.log_text.append(f"  📁 {source_path}: {dir_size:.2f} MB")
                else:
                    self.log_text.append(f"  ❌ {source_path}: 文件不存在")
            self.log_text.append(f"  📊 数据文件总大小: {total_size:.2f} MB")

        # 显示隐藏导入分析
        self.log_text.append("📋 隐藏导入分析:")
        hidden_imports = config['hidden_imports']
        self.log_text.append(f"  📦 隐藏导入数量: {len(hidden_imports)}")
        for imp in hidden_imports[:5]:  # 只显示前5个
            self.log_text.append(f"  📦 {imp}")
        if len(hidden_imports) > 5:
            self.log_text.append(f"  📦 ... 还有 {len(hidden_imports) - 5} 个模块")

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条

        # 更新按钮状态
        self.package_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)

        # 启动打包线程
        self.package_thread = PackageThread(config)
        self.package_thread.progress_signal.connect(self.update_log)
        self.package_thread.finished_signal.connect(self.package_finished)
        self.package_thread.start()

    def stop_package(self):
        """停止打包"""
        if self.package_thread and self.package_thread.isRunning():
            self.package_thread.terminate()
            self.package_thread.wait()
            self.log_text.append("⏹️ 打包已被用户停止")
            self.package_finished(False, "用户停止打包")

    def prepare_config(self, main_file):
        """准备打包配置"""
        config = {
            'name': '快手小店管理系统',
            'main_file': main_file,
            'output_dir': self.output_dir,
            'mode': 'onefile' if self.mode_combo.currentIndex() == 0 else 'onedir',
            'show_console': self.console_check.isChecked(),
            'enable_upx': self.compress_check.isChecked(),
            'upx_path': getattr(self, 'upx_path', None),
            'icon_path': self.find_icon_file(),
            'data_files': self.get_data_files(),
            'hidden_imports': self.get_hidden_imports()
        }
        return config

    def find_icon_file(self):
        """查找图标文件"""
        # 使用正确的图标路径
        icon_path = os.path.join("ksxiaodian", "config", "imges", "logo.ico")
        if os.path.exists(icon_path):
            return os.path.abspath(icon_path)
        return None

    def get_data_files(self):
        """获取数据文件列表（优化版本，避免包含过大文件）"""
        data_files = []
        separator = ";" if sys.platform == "win32" else ":"

        # 配置文件目录 - 只包含必要的配置文件
        config_path = os.path.join("ksxiaodian", "config")
        if os.path.exists(config_path):
            # 检查config目录大小，如果过大则选择性包含
            config_size = self.get_directory_size(config_path)
            if config_size > 50:  # 如果config目录超过50MB
                self.log_text.append(f"⚠️ config目录过大({config_size:.1f}MB)，建议检查是否包含不必要文件")
                # 只包含必要的配置文件
                essential_config_files = [
                    "config.json",
                    "账号管理.json",
                    "账号管理字段设置.json",
                    "邀约日志.json"
                ]
                for config_file in essential_config_files:
                    config_file_path = os.path.join(config_path, config_file)
                    if os.path.exists(config_file_path):
                        data_files.append(f"{config_file_path}{separator}config")

                # 包含imges目录（图标等）
                imges_path = os.path.join(config_path, "imges")
                if os.path.exists(imges_path):
                    data_files.append(f"{imges_path}{separator}config/imges")
            else:
                # config目录不大，全部包含
                data_files.append(f"{config_path}{separator}config")

        # 工具目录 - 排除大文件
        tool_path = os.path.join("ksxiaodian", "tool")
        if os.path.exists(tool_path):
            tool_size = self.get_directory_size(tool_path)
            if tool_size > 100:  # 如果tool目录超过100MB
                self.log_text.append(f"⚠️ tool目录过大({tool_size:.1f}MB)，只包含必要文件")
                # 只包含必要的Python文件
                essential_tool_files = [
                    "__init__.py",
                    "快手api.py",
                    "快手cokie_api.py",
                    "内嵌浏览器.py",
                    "阿里巴巴接口.py",
                    "详情统计.py",
                    "状态管理器.py",
                    "分类树.py",
                    "店铺编辑器.py",
                    "待下单底部.py"
                ]
                for tool_file in essential_tool_files:
                    tool_file_path = os.path.join(tool_path, tool_file)
                    if os.path.exists(tool_file_path):
                        data_files.append(f"{tool_file_path}{separator}tool")
            else:
                # tool目录不大，全部包含
                data_files.append(f"{tool_path}{separator}tool")

        # 其他必要文件
        essential_files = [
            "达人邀约.py",
            "商品复制.py",
            "一键下单.py",
            "商品管理.py",
            "快手订单.py",
            "数据统计.py",
            "登录窗口.py",
            "计划管理.py",
            "店铺后台登录.py"
        ]

        # 查找必要文件
        for file in essential_files:
            file_path = os.path.join("ksxiaodian", file)
            if os.path.exists(file_path):
                data_files.append(f"{file_path}{separator}.")

        return data_files

    def check_upx_availability(self):
        """检测UPX是否可用"""
        self.upx_path = None
        self.upx_available = False

        # 检查本地tool目录中的UPX
        local_upx = os.path.join("ksxiaodian", "tool", "upx.exe")
        if os.path.exists(local_upx):
            try:
                result = subprocess.run([local_upx, '--version'],
                                      capture_output=True,
                                      text=True,
                                      timeout=5)
                if result.returncode == 0:
                    self.upx_available = True
                    self.upx_path = local_upx
                    self.log_text.append("✅ 检测到本地UPX工具，压缩功能可用")
                    return
            except:
                pass

        # 检查系统PATH中的UPX
        try:
            result = subprocess.run(['upx', '--version'],
                                  capture_output=True,
                                  text=True,
                                  timeout=5)
            if result.returncode == 0:
                self.upx_available = True
                self.upx_path = 'upx'
                self.log_text.append("✅ 检测到系统UPX工具，压缩功能可用")
                return
        except:
            pass

        # 未找到UPX
        self.log_text.append("⚠️ 未检测到UPX工具")
        self.log_text.append("💡 可以点击'下载UPX'按钮自动下载到tool目录")

    def get_file_size_mb(self, file_path):
        """获取文件大小（MB）"""
        try:
            size_bytes = os.path.getsize(file_path)
            size_mb = size_bytes / (1024 * 1024)
            return round(size_mb, 2)
        except:
            return 0

    def get_directory_size(self, directory):
        """获取目录大小（MB）"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except (OSError, FileNotFoundError):
                        continue
            return total_size / (1024 * 1024)  # 转换为MB
        except:
            return 0

    def cleanup_temp_files(self):
        """清理打包过程中生成的临时文件"""
        try:
            # 清理spec文件
            spec_files = [
                "快手小店管理系统.spec",
                "ksxiaodian/快手小店管理系统.spec"
            ]

            for spec_file in spec_files:
                if os.path.exists(spec_file):
                    os.remove(spec_file)
                    self.log_text.append(f"🗑️ 已删除临时文件: {spec_file}")

            # 清理build目录
            build_dirs = [
                "build",
                "ksxiaodian/build"
            ]

            for build_dir in build_dirs:
                if os.path.exists(build_dir):
                    try:
                        shutil.rmtree(build_dir)
                        self.log_text.append(f"🗑️ 已删除临时目录: {build_dir}")
                    except Exception as e:
                        self.log_text.append(f"⚠️ 无法删除目录 {build_dir}: {str(e)}")

            # 清理__pycache__目录
            pycache_dirs = []
            for root, dirs, files in os.walk("."):
                for dir_name in dirs:
                    if dir_name == "__pycache__":
                        pycache_dirs.append(os.path.join(root, dir_name))

            for pycache_dir in pycache_dirs:
                try:
                    shutil.rmtree(pycache_dir)
                    self.log_text.append(f"🗑️ 已删除缓存目录: {pycache_dir}")
                except Exception as e:
                    self.log_text.append(f"⚠️ 无法删除缓存目录 {pycache_dir}: {str(e)}")

            self.log_text.append("✅ 临时文件清理完成")

        except Exception as e:
            self.log_text.append(f"❌ 清理临时文件时出错: {str(e)}")

    def open_output_folder(self):
        """打开输出文件夹"""
        try:
            if os.path.exists(self.output_dir):
                if sys.platform == "win32":
                    # Windows系统
                    os.startfile(self.output_dir)
                elif sys.platform == "darwin":
                    # macOS系统
                    subprocess.run(["open", self.output_dir])
                else:
                    # Linux系统
                    subprocess.run(["xdg-open", self.output_dir])

                self.log_text.append(f"📂 已打开输出文件夹: {self.output_dir}")
            else:
                self.log_text.append(f"❌ 输出文件夹不存在: {self.output_dir}")
        except Exception as e:
            self.log_text.append(f"❌ 无法打开文件夹: {str(e)}")

    def show_compression_advice(self):
        """显示压缩建议"""
        advice_text = """
🔍 UPX压缩优化建议

❌ 为什么压缩后体积反而变大？

1. 系统DLL文件已经高度优化，压缩后可能增大
2. Qt库文件通常已经压缩过，再次压缩效果不佳
3. 某些文件格式不适合UPX压缩算法

✅ 本工具的智能压缩方案：

• 自动排除系统DLL（vcruntime140.dll等）
• 排除Qt库文件（Qt5Core.dll等）
• 排除Windows API库文件
• 只压缩适合的应用程序代码

💡 使用建议：

• 首次打包：建议不启用压缩，查看基础大小
• 文件>100MB：可尝试启用智能压缩
• 文件<50MB：通常不需要压缩
• 对比测试：分别打包对比实际效果

⚠️ 注意事项：

• 需要安装UPX工具（upx.exe）
• 压缩会增加启动时间1-3秒
• 某些杀毒软件可能误报压缩文件
        """

        QMessageBox.information(self, "压缩建议", advice_text)

    def show_size_optimization(self):
        """显示体积优化建议"""
        optimization_text = """
🔧 打包体积优化指南

❌ 常见体积增大原因：

1. 包含了整个config和tool目录
   • config目录可能包含大量日志、缓存文件
   • tool目录可能包含chromedriver等大文件

2. 过多的隐藏导入模块
   • 自动导入了不必要的第三方库
   • 包含了未使用的PyQt5模块

3. 数据文件配置不当
   • 包含了临时文件、日志文件
   • 包含了开发时的测试文件

✅ 优化建议：

📁 目录优化：
• 清理config目录中的日志文件(.log, .pkl)
• 删除tool目录中的chromedriver.exe等大文件
• 只保留必要的配置文件和Python模块

📦 模块优化：
• 使用精简的隐藏导入列表
• 只导入实际使用的模块
• 避免导入整个库

🗜️ 压缩优化：
• 启用智能UPX压缩
• 排除系统DLL文件
• 只压缩应用程序代码

📊 预期效果：
• 优化前：600MB
• 优化后：80-120MB
• 压缩后：50-80MB

🔧 立即优化：
本工具已自动应用以上优化策略！
        """

        QMessageBox.information(self, "体积优化", optimization_text)

    def manual_cleanup(self):
        """手动清理临时文件"""
        reply = QMessageBox.question(
            self, "清理确认",
            "将清理以下临时文件：\n\n"
            "• .spec文件（PyInstaller规格文件）\n"
            "• build目录（编译临时文件）\n"
            "• __pycache__目录（Python缓存）\n\n"
            "是否继续清理？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            self.cleanup_temp_files()
            QMessageBox.information(self, "清理完成", "临时文件清理完成！")

    def download_upx(self):
        """下载UPX工具"""
        # 检查是否已经存在
        local_upx = os.path.join("ksxiaodian", "tool", "upx.exe")
        if os.path.exists(local_upx):
            reply = QMessageBox.question(
                self, "UPX已存在",
                "检测到UPX工具已存在，是否重新下载？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return

        # 确认下载
        reply = QMessageBox.question(
            self, "下载UPX工具",
            "将从GitHub下载UPX工具到ksxiaodian/tool目录\n\n"
            "下载大小约2MB，是否继续？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            # 禁用下载按钮
            self.download_upx_btn.setEnabled(False)
            self.download_upx_btn.setText("下载中...")

            # 启动下载线程
            self.upx_download_thread = UPXDownloadThread()
            self.upx_download_thread.progress_signal.connect(self.update_log)
            self.upx_download_thread.finished_signal.connect(self.upx_download_finished)
            self.upx_download_thread.start()

    def upx_download_finished(self, success, message):
        """UPX下载完成"""
        # 恢复下载按钮
        self.download_upx_btn.setEnabled(True)
        self.download_upx_btn.setText("📥 下载UPX")

        if success:
            self.log_text.append("🎉 UPX工具下载成功！")
            # 重新检测UPX可用性
            self.check_upx_availability()
            QMessageBox.information(self, "成功", "UPX工具下载成功！\n现在可以使用压缩功能了。")
        else:
            self.log_text.append(f"❌ UPX下载失败: {message}")
            QMessageBox.critical(self, "下载失败", f"UPX下载失败: {message}\n\n请检查网络连接或手动下载。")

    def get_hidden_imports(self):
        """获取隐藏导入列表（精简版本，只包含必要模块）"""
        # 基础必要模块
        basic_imports = [
            "PyQt5.QtCore",
            "PyQt5.QtWidgets",
            "PyQt5.QtGui",
            "requests",
            "json",
            "sqlite3"
        ]

        # 条件性导入 - 只在文件存在时添加
        conditional_imports = []

        # 检查tool目录中的模块
        tool_modules = [
            ("tool.快手cokie_api", "ksxiaodian/tool/快手cokie_api.py"),
            ("tool.内嵌浏览器", "ksxiaodian/tool/内嵌浏览器.py"),
            ("tool.阿里巴巴接口", "ksxiaodian/tool/阿里巴巴接口.py"),
            ("tool.详情统计", "ksxiaodian/tool/详情统计.py"),
            ("tool.状态管理器", "ksxiaodian/tool/状态管理器.py")
        ]

        for module_name, file_path in tool_modules:
            if os.path.exists(file_path):
                conditional_imports.append(module_name)

        # 网络相关模块 - 检查是否真的需要
        network_modules = []
        if os.path.exists("ksxiaodian/tool/快手cokie_api.py"):
            network_modules.extend(["urllib3", "brotli"])
        if os.path.exists("ksxiaodian/tool/内嵌浏览器.py"):
            network_modules.append("PyQt5.QtNetwork")

        # 并发模块 - 检查是否使用
        concurrent_modules = []
        main_file = os.path.join("ksxiaodian", "快手小店管理系统.py")
        if os.path.exists(main_file):
            try:
                with open(main_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'threading' in content or 'Thread' in content:
                        concurrent_modules.append("threading")
                    if 'concurrent.futures' in content:
                        concurrent_modules.append("concurrent.futures")
            except:
                pass

        all_imports = basic_imports + conditional_imports + network_modules + concurrent_modules

        # 去重并返回
        return list(set(all_imports))

    def update_log(self, message):
        """更新日志"""
        self.log_text.append(message)
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def package_finished(self, success, message):
        """打包完成"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 恢复按钮状态
        self.package_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

        # 显示结果
        if success:
            self.log_text.append("🎉 打包成功完成！")
            self.log_text.append(f"📦 输出位置: {self.output_dir}")

            # 检查生成的文件大小
            exe_path = os.path.join(self.output_dir, "快手小店管理系统.exe")
            file_size = 0
            if os.path.exists(exe_path):
                file_size = self.get_file_size_mb(exe_path)
                self.log_text.append(f"📊 生成文件大小: {file_size} MB")

                # 根据文件大小给出建议
                if file_size > 150:
                    self.log_text.append("💡 建议: 文件较大，可尝试启用UPX压缩减小体积")
                elif file_size < 50:
                    self.log_text.append("✅ 文件大小合适")
                else:
                    self.log_text.append("✅ 文件大小正常")

            # 清理临时文件
            self.cleanup_temp_files()

            # 显示成功对话框，包含打开文件夹选项
            size_text = f"{file_size} MB" if file_size > 0 else "未知"
            reply = QMessageBox.question(
                self, "打包成功",
                f"🎉 打包成功！\n\n"
                f"📦 输出位置: {self.output_dir}\n"
                f"📊 文件大小: {size_text}\n\n"
                f"是否打开输出文件夹？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                self.open_output_folder()
        else:
            self.log_text.append(f"❌ 打包失败: {message}")
            QMessageBox.critical(self, "失败", f"打包失败: {message}")

    def closeEvent(self, event):
        """关闭事件"""
        if self.package_thread and self.package_thread.isRunning():
            reply = QMessageBox.question(
                self, "确认", "打包正在进行中，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.package_thread.terminate()
                self.package_thread.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("快手打包工具")
    app.setApplicationVersion("1.0")

    # 设置应用图标（使用正确的图标路径）
    try:
        icon_path = os.path.join("ksxiaodian", "config", "imges", "logo.ico")
        if os.path.exists(icon_path):
            app.setWindowIcon(QIcon(icon_path))
    except:
        pass

    # 创建主窗口
    window = KuaishouPackageTool()
    window.show()

    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

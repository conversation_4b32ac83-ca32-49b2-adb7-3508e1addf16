{"template_info": {"name": "微信小店商品上传模板", "version": "1.0", "description": "用于将1688商品数据转换为微信小店格式的配置模板", "last_updated": "2025-06-30"}, "default_config": {"deliver_method": 0, "brand_id": "2100000000", "listing": 1, "extra_service": {"seven_day_return": 1, "freight_insurance": 0}, "after_sale_info": {"after_sale_address_id": 1}}, "field_mapping": {"title": {"source_field": "title", "max_length": 60, "required": true, "filters": ["remove_emoji", "trim_spaces"]}, "short_title": {"source_field": "title", "max_length": 20, "required": false, "filters": ["remove_emoji", "trim_spaces"]}, "spu_code": {"source_field": "product_id", "required": false, "process_type": "generate_spu_code"}, "head_imgs": {"source_field": "product_image.images", "required": true, "min_count": 1, "max_count": 9, "process_type": "upload_to_wechat"}, "desc_info": {"imgs": {"source_field": "description_images", "required": false, "max_count": 20, "process_type": "upload_to_wechat"}, "desc": {"source_field": "description", "required": false, "max_length": 20000, "process_type": "replace_images_with_wechat"}}, "cats": {"source_field": "cats", "required": false, "default": []}, "cats_v2": {"source_field": "category_id", "required": true, "process_type": "convert_to_cats_v2"}, "attrs": {"source_field": "product_attribute", "required": false, "process_type": "convert_to_attrs"}, "deliver_method": {"source_field": "deliver_method", "required": true, "default": 0}, "brand_id": {"source_field": "brand_id", "required": false, "default": "2100000000"}, "listing": {"source_field": "listing", "required": false, "data_type": "integer", "default": 1, "description": "添加完成后是否立即上架。1:是；0:否"}, "express_info": {"template_id": {"source_field": "freight_template_id", "required": true, "default": "1"}}, "extra_service": {"seven_day_return": {"default": 1}, "freight_insurance": {"default": 0}}, "after_sale_info": {"after_sale_address_id": {"default": 1}}, "skus": {"source_field": "product_sku_infos", "required": true, "min_count": 1, "fields": {"thumb_img": {"source_field": "sku_image", "required": false, "process_type": "upload_to_wechat"}, "sale_price": {"source_field": "price", "required": true, "data_type": "price_in_cents"}, "stock_num": {"source_field": "stock", "required": true, "data_type": "integer", "default": 999}, "sku_code": {"source_field": "sku_code", "required": false, "max_length": 128, "default": ""}, "sku_attrs": {"source_field": "attributes", "required": false, "process_type": "convert_to_sku_attrs"}, "sku_deliver_info": {"stock_type": {"default": 0}}}}, "product_qua_infos": {"source_field": "qualifications", "required": false, "fields": {"qua_id": {"source_field": "qua_id", "required": true}, "qua_url": {"source_field": "images", "required": true, "process_type": "upload_to_wechat"}}}}, "data_processors": {"remove_emoji": {"description": "移除emoji表情符号", "regex": "[\\u1F600-\\u1F64F\\u1F300-\\u1F5FF\\u1F680-\\u1F6FF\\u1F1E0-\\u1F1FF]"}, "trim_spaces": {"description": "去除首尾空格并压缩多余空格"}, "price_in_cents": {"description": "将价格转换为分（乘以100）", "formula": "int(float(value) * 100)"}, "upload_to_wechat": {"description": "通过微信图片上传接口处理图片", "api_endpoint": "/channels/ec/basics/img/upload", "params": {"upload_type": 1, "resp_type": 1}}, "convert_attributes": {"description": "转换SKU属性格式", "output_format": [{"attr_key": "颜色", "attr_value": "红色"}]}, "convert_to_cats_v2": {"description": "将类目ID转换为cats_v2格式", "format": "[{\"cat_id\": \"category_id\"}]"}, "convert_to_attrs": {"description": "将product_attribute转换为attrs格式", "format": "[{\"attr_key\": \"name\", \"attr_value\": \"value\"}]"}, "convert_to_sku_attrs": {"description": "将SKU属性转换为sku_attrs格式", "format": "[{\"attr_key\": \"name\", \"attr_value\": \"value\"}]"}, "replace_images_with_wechat": {"description": "替换HTML中的图片链接为微信链接"}}, "validation_rules": {"must_be_positive_integer": {"type": "integer", "min_value": 1}, "must_be_positive": {"type": "number", "min_value": 0.01}, "required_field": {"not_empty": true, "not_null": true}}, "error_handling": {"missing_required_field": {"action": "skip_product", "log_level": "error", "message": "缺少必填字段: {field_name}"}, "image_upload_failed": {"action": "retry", "max_retries": 3, "log_level": "warning", "message": "图片上传失败: {image_url}"}, "data_conversion_error": {"action": "use_default", "log_level": "warning", "message": "数据转换失败，使用默认值: {field_name}"}}, "category_templates": {"女装": {"category_id": 1001, "required_qualifications": [], "default_freight_template": 1, "special_fields": {"size_chart": {"required": false, "source_field": "size_info"}}}, "女鞋": {"category_id": 1002, "required_qualifications": [], "default_freight_template": 1, "special_fields": {"shoe_size": {"required": true, "source_field": "size_range"}}}, "百货": {"category_id": 1003, "required_qualifications": [], "default_freight_template": 1}}, "shop_templates": {"default": {"freight_template_id": 1, "brand_id": 0, "auto_listing": false}}}
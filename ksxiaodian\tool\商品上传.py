#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
微信小店商品上传工具

此模块提供完整的微信小店商品上传功能，包括：
1. 基于JSON模板的数据转换
2. 图片批量上传处理
3. 商品数据验证和清洗
4. 错误处理和重试机制
5. 与商品复制界面的集成

作者: AI工程师
创建日期: 2025-06-30
"""

import sys
import os
import json
from datetime import datetime
import re
import requests
import time
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
import difflib

# 添加父目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tool.微信api import WechatAPI

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class WechatStoreUploadTemplate:
    """微信小店上传模板管理器"""
    
    def __init__(self):
        """初始化模板配置"""
        self.template_config = self._get_default_template()
    
    def _get_default_template(self) -> Dict:
        """获取默认模板配置"""
        return {
            "template_info": {
                "name": "微信小店商品上传模板",
                "version": "1.0",
                "description": "用于将1688商品数据转换为微信小店格式的配置模板"
            },
            
            "default_config": {
                "freight_template_id": 0,
                "brand_id": 0,
                "product_type": 1,
                "listing": False,
                "edit_type": 1
            },
            
            "field_mapping": {
                "basic_info": {
                    "title": {
                        "source_field": "title",
                        "max_length": 60,
                        "required": True,
                        "filters": ["remove_emoji", "trim_spaces"],
                        "default": ""
                    },
                    "sub_title": {
                        "source_field": "sub_title",
                        "max_length": 18,
                        "required": False,
                        "default": ""
                    },
                    "info": {
                        "source_field": "description",
                        "max_length": 1000,
                        "required": False,
                        "default": ""
                    }
                },
                
                "image_info": {
                    "head_img": {
                        "source_field": "main_images",
                        "required": True,
                        "min_count": 1,
                        "max_count": 9,
                        "process_type": "upload_to_wechat"
                    },
                    "desc_imgs": {
                        "source_field": "detail_images", 
                        "required": False,
                        "max_count": 20,
                        "process_type": "upload_to_wechat"
                    }
                },
                
                "sku_info": {
                    "skus": {
                        "source_field": "skus",
                        "required": True,
                        "min_count": 1,
                        "fields": {
                            "out_sku_id": {
                                "source_field": "sku_id",
                                "required": True,
                                "max_length": 128
                            },
                            "thumb_img": {
                                "source_field": "sku_image",
                                "required": False,
                                "process_type": "upload_to_wechat"
                            },
                            "sale_price": {
                                "source_field": "price",
                                "required": True,
                                "data_type": "price_in_cents",
                                "validation": "must_be_positive"
                            },
                            "market_price": {
                                "source_field": "market_price",
                                "required": False,
                                "data_type": "price_in_cents",
                                "default_from": "sale_price"
                            },
                            "stock_num": {
                                "source_field": "stock",
                                "required": True,
                                "data_type": "integer",
                                "min_value": 0,
                                "default": 999
                            },
                            "sku_code": {
                                "source_field": "sku_code",
                                "required": False,
                                "max_length": 128,
                                "default": ""
                            },
                            "barcode": {
                                "source_field": "barcode", 
                                "required": False,
                                "max_length": 128,
                                "default": ""
                            },
                            "sku_attrs": {
                                "source_field": "attributes",
                                "required": False,
                                "process_type": "convert_attributes"
                            }
                        }
                    }
                }
            },
            
            "data_processors": {
                "remove_emoji": {
                    "description": "移除emoji表情符号",
                    "regex": "[\\u1F600-\\u1F64F\\u1F300-\\u1F5FF\\u1F680-\\u1F6FF\\u1F1E0-\\u1F1FF]"
                },
                "trim_spaces": {
                    "description": "去除首尾空格并压缩多余空格"
                },
                "price_in_cents": {
                    "description": "将价格转换为分（乘以100）",
                    "formula": "int(float(value) * 100)"
                },
                "upload_to_wechat": {
                    "description": "通过微信图片上传接口处理图片",
                    "api_endpoint": "/channels/ec/basics/img/upload",
                    "params": {
                        "upload_type": 1,
                        "resp_type": 1
                    }
                }
            },
            
            "category_templates": {
                "女装": {
                    "category_id": 1001,
                    "default_freight_template": 1
                },
                "女鞋": {
                    "category_id": 1002,
                    "default_freight_template": 1
                },
                "百货": {
                    "category_id": 1003,
                    "default_freight_template": 1
                }
            }
        }


class WechatStoreUploadProcessor:
    """微信小店商品上传处理器"""

    def __init__(self, template_path: str = "config/微信小店上传模板.json"):
        """初始化上传处理器"""
        self.template_path = template_path
        self.template_config = self._load_template()
        self.wechat_api = None
        self.image_cache = {}  # 图片上传缓存
        self.attr_mapping_config = self._load_attr_mapping_config()  # 属性智能匹配配置
        self.attr_mapping_updates = {}  # 记录需要更新的属性映射
        self.image_filter_settings = {  # 图片过滤设置
            'main_image': {
                'delete_before': 0,  # 主图删除前N张
                'delete_after': 0    # 主图删除后N张
            },
            'detail_image': {
                'delete_before': 0,  # 详情图删除前N张
                'delete_after': 0    # 详情图删除后N张
            }
        }
        self.skip_copy_settings = {  # 跳过复制设置
            'enabled': False,
            'keywords': []
        }

    def _load_template(self) -> Dict:
        """加载外部模板配置文件"""
        try:
            if os.path.exists(self.template_path):
                with open(self.template_path, 'r', encoding='utf-8') as f:
                    template_config = json.load(f)
                print(f"[微信小店上传] 成功加载模板配置: {self.template_path}")
                return template_config
            else:
                print(f"[微信小店上传] 模板文件不存在: {self.template_path}")
                # 如果外部模板不存在，使用内置模板
                template = WechatStoreUploadTemplate()
                return template.template_config
        except Exception as e:
            print(f"[微信小店上传] 加载模板配置失败: {e}")
            # 如果加载失败，使用内置模板
            template = WechatStoreUploadTemplate()
            return template.template_config

    def _load_attr_mapping_config(self) -> Dict:
        """加载属性智能匹配配置"""
        try:
            config_path = "config/属性智能匹配.json"
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    print(f"[微信小店上传] 成功加载属性智能匹配配置")
                    return config
            else:
                print(f"[微信小店上传] 属性智能匹配配置文件不存在: {config_path}")
                return {}
        except Exception as e:
            print(f"[微信小店上传] 加载属性智能匹配配置失败: {e}")
            return {}

    def set_wechat_api(self, access_token: str, appid: str = None):
        """设置微信API实例"""
        self.wechat_api = WechatAPI()
        self.wechat_api.access_token = access_token
        if appid:
            self.wechat_api.appid = appid
    
    def process_product_data(self, cache_data: Dict, category_id: int,
                           freight_template_id: int = None) -> Dict:
        """
        处理商品数据，转换为微信小店格式

        Args:
            cache_data: 1688商品缓存数据
            category_id: 微信小店类目ID
            freight_template_id: 运费模板ID

        Returns:
            转换后的微信小店商品数据
        """
        try:
            print(f"[微信小店上传] ===== 开始处理商品数据 =====")
            print(f"[微信小店上传] cache_data keys: {list(cache_data.keys())}")
            print(f"[微信小店上传] category_id: {category_id}")
            print(f"[微信小店上传] freight_template_id: {freight_template_id}")
            logger.info(f"开始处理商品数据: {cache_data.get('title', 'Unknown')}")

            # 设置当前商品标题，供属性智能判断使用
            self.current_product_title = cache_data.get('title', '')

            # 获取模板配置
            field_mapping = self.template_config['field_mapping']
            default_config = self.template_config['default_config']
            
            # 构建基础商品数据 - 按照微信小店API要求
            product_data = {
                # 基础信息
                'deliver_method': 0,  # 0-快递发货
                'brand_id': str(default_config.get('brand_id', 2100000000)),  # 无品牌为"2100000000"
                'listing': 1 if default_config.get('listing', False) else 0,  # 转换为数字

                # 售后服务（必填）
                'extra_service': {
                    'seven_day_return': 1,  # 支持七天无理由退货
                    'freight_insurance': 0   # 不支持运费险
                },

                # 售后地址（必填）
                'after_sale_info': {
                    'after_sale_address_id': 1  # 默认售后地址ID，需要商家先设置
                }
            }
            
            # 处理基础信息 - 直接按照新模板结构处理
            product_data.update(self._process_all_fields(cache_data, field_mapping))
            
            # 处理图片信息
            print(f"[微信小店上传] 准备调用 _process_images 方法")
            image_data = self._process_images(cache_data, field_mapping)
            print(f"[微信小店上传] _process_images 方法调用完成，返回数据: {list(image_data.keys()) if image_data else 'None'}")
            product_data.update(image_data)

            # 处理SKU信息
            product_data['skus'] = self._process_skus(cache_data, field_mapping)

            # 处理商品属性 - 从缓存的 product_attribute 获取
            product_attributes = cache_data.get('product_attribute', [])
            if product_attributes:
                print(f"[微信小店上传] 找到 {len(product_attributes)} 个商品属性")
                # 这里可以根据需要处理商品属性
                # 微信小店的商品属性格式可能需要特殊处理

            # 数据验证
            self._validate_product_data(product_data)

            # 保存属性映射更新建议
            self._save_mapping_updates()

            logger.info(f"商品数据处理完成: {product_data.get('title')}")
            return product_data
            
        except Exception as e:
            logger.error(f"处理商品数据失败: {e}")
            raise
    
    def _process_all_fields(self, cache_data: Dict, field_mapping: Dict) -> Dict:
        """处理所有字段 - 按照新模板结构"""
        result = {}

        for field_name, config in field_mapping.items():
            # 跳过特殊处理的字段
            if field_name in ['head_imgs', 'desc_info', 'skus', 'product_qua_infos']:
                continue

            try:
                if field_name == 'cats_v2':
                    # 处理类目信息 - 从缓存数据中获取实际类目ID
                    category_text = str(cache_data.get('category_id', '1001'))
                    cats_v2 = []
                    if category_text:
                        # 解析类目ID，可能是逗号分隔的多个ID：10000212,10000213,7389
                        category_ids = [id.strip() for id in category_text.split(',') if id.strip()]
                        for cat_id in category_ids:
                            cats_v2.append({"cat_id": cat_id})
                        print(f"[微信小店上传] 从缓存数据获取类目ID: {category_text}")
                        print(f"[微信小店上传] 解析后的类目列表: {cats_v2}")
                    else:
                        # 如果没有类目ID，使用默认的测试类目
                        cats_v2 = [
                            {"cat_id": "10000111"},  # 一级类目：服饰鞋包
                            {"cat_id": "10000113"},  # 二级类目：女鞋
                            {"cat_id": "6091"}       # 三级类目：运动鞋
                        ]
                        print(f"[微信小店上传] 未找到类目ID，使用默认测试类目: {cats_v2}")

                    result[field_name] = cats_v2

                elif field_name == 'attrs':
                    # 只添加类目要求的必填属性，不使用缓存中的属性
                    # 获取最后一个类目ID（叶子类目）
                    category_text = str(cache_data.get('category_id', '1001'))
                    leaf_category_id = "1001"  # 默认类目ID
                    if category_text:
                        category_ids = [id.strip() for id in category_text.split(',') if id.strip()]
                        if category_ids:
                            leaf_category_id = category_ids[-1]  # 使用最后一个类目ID（叶子类目）

                    required_attrs = self._get_required_attrs_only(leaf_category_id, cache_data)
                    result[field_name] = required_attrs
                    print(f"[微信小店上传] 使用类目ID {leaf_category_id} 获取必填属性: {len(required_attrs)} 个")

                elif field_name == 'express_info':
                    # 处理运费模板 - 从设置页面获取
                    template_id = self._get_freight_template_id()
                    result[field_name] = {"template_id": str(template_id)}
                    print(f"[微信小店上传] 使用运费模板ID: {template_id}")

                elif field_name == 'extra_service':
                    # 处理售后服务（必填）
                    result[field_name] = {
                        "seven_day_return": 1,  # 支持七天无理由退货
                        "freight_insurance": 0   # 不支持运费险
                    }

                elif field_name == 'after_sale_info':
                    # 处理售后地址（必填）
                    return_address_id = self._get_return_address_id()
                    result[field_name] = {
                        "after_sale_address_id": return_address_id
                    }
                    print(f"[微信小店上传] 使用退货地址ID: {return_address_id}")

                elif field_name == 'listing':
                    # 处理上架状态 - 微信API要求数字类型：1=立即上架，0=不立即上架
                    shelf_status = self._get_shelf_status()
                    result[field_name] = shelf_status  # 直接使用数字值（1或0）
                    print(f"[微信小店上传] 设置上架状态: {result[field_name]} ({'立即上架' if shelf_status == 1 else '不立即上架'})")

                elif field_name == 'cats':
                    # 旧版类目，设为空数组
                    result[field_name] = []

                elif field_name == 'spu_code':
                    # 处理商品编码 - 从缓存数据中获取product_id
                    product_id = self._get_product_id_from_cache(cache_data)
                    if product_id:
                        result[field_name] = str(product_id)
                        print(f"[微信小店上传] 设置商品编码(spu_code): {result[field_name]}")
                    else:
                        print(f"[微信小店上传] 未找到product_id，跳过spu_code设置")

                else:
                    # 处理普通字段
                    source_field = config.get('source_field', field_name)
                    value = cache_data.get(source_field, config.get('default', ''))

                    # 应用数据处理器
                    if 'filters' in config:
                        for filter_name in config['filters']:
                            value = self._apply_filter(value, filter_name)

                    # 长度限制
                    if 'max_length' in config and len(str(value)) > config['max_length']:
                        value = str(value)[:config['max_length']]

                    result[field_name] = value

            except Exception as e:
                print(f"[微信小店上传] 处理字段 {field_name} 失败: {e}")
                # 设置默认值
                result[field_name] = config.get('default', '')

        return result

    def _get_product_id_from_cache(self, cache_data: Dict) -> str:
        """从缓存数据中获取商品ID"""
        # 尝试从不同位置获取product_id
        product_id = None

        # 首先尝试从顶层获取
        if 'product_id' in cache_data:
            product_id = cache_data['product_id']
        # 然后尝试从product_info层级获取
        elif 'product_info' in cache_data and 'product_id' in cache_data['product_info']:
            product_id = cache_data['product_info']['product_id']

        return str(product_id) if product_id else None

    def _convert_to_attrs(self, product_attrs: List) -> List[Dict]:
        """转换商品属性为微信小店格式"""
        attrs = []
        attr_groups = {}  # 用于合并相同属性名的多个值

        for attr in product_attrs:
            # 根据实际缓存格式处理属性
            if isinstance(attr, dict):
                attr_name = attr.get('attributeName', attr.get('name', ''))
                attr_value = attr.get('value', '')

                if attr_name and attr_value:
                    # 将相同属性名的值合并
                    if attr_name not in attr_groups:
                        attr_groups[attr_name] = []

                    # 避免重复的值
                    if attr_value not in attr_groups[attr_name]:
                        attr_groups[attr_name].append(attr_value)

        # 转换为微信小店格式
        for attr_name, values in attr_groups.items():
            if len(values) == 1:
                # 单个值
                attrs.append({
                    "attr_key": str(attr_name),
                    "attr_value": str(values[0])
                })
            else:
                # 多个值用分号分隔（根据微信API文档）
                combined_value = ";".join(values)
                attrs.append({
                    "attr_key": str(attr_name),
                    "attr_value": combined_value
                })
                print(f"[微信小店上传] 合并属性 {attr_name}: {combined_value}")

        print(f"[微信小店上传] 转换商品属性: {len(attrs)} 个属性")
        return attrs

    def _get_required_attrs_only(self, category_id: str, cache_data: Dict = None) -> List[Dict]:
        """通过API查询类目要求的必填属性，并尝试从商品缓存中匹配属性值"""
        try:
            # 设置当前类目ID，用于类目特定的属性匹配
            self.current_category_id = category_id

            # 获取类目详情
            print(f"[微信小店上传] 查询类目 {category_id} 的必填属性")
            category_detail = self._get_category_detail(category_id)

            if category_detail and 'attr' in category_detail and 'product_attr_list' in category_detail['attr']:
                required_attrs = []
                product_attr_list = category_detail['attr']['product_attr_list']

                # 从商品缓存中获取现有属性
                cache_attrs = {}
                if cache_data:
                    # 尝试从不同的字段获取属性信息
                    # 首先尝试从 product_info 层级获取
                    product_attrs = []
                    if 'product_info' in cache_data and 'product_attribute' in cache_data['product_info']:
                        product_attrs = cache_data['product_info']['product_attribute']
                    else:
                        # 备用：从顶层获取
                        product_attrs = cache_data.get('product_attribute', [])

                    if isinstance(product_attrs, list):
                        for attr in product_attrs:
                            if isinstance(attr, dict) and 'value' in attr:
                                # 支持两种字段名格式：attributeName 和 name
                                attr_name = attr.get('attributeName', attr.get('name', ''))
                                if attr_name:
                                    cache_attrs[attr_name] = attr['value']

                    # 也尝试从其他可能的字段获取属性
                    if 'attributes' in cache_data:
                        attrs = cache_data['attributes']
                        if isinstance(attrs, dict):
                            cache_attrs.update(attrs)

                # 如果没有产地属性，尝试从 sendGoodsAddressText 提取
                if '产地' not in cache_attrs:
                    send_goods_address = ''
                    # 尝试从不同位置获取发货地址
                    if 'product_info' in cache_data and 'product_shipping_info' in cache_data['product_info']:
                        send_goods_address = cache_data['product_info']['product_shipping_info'].get('sendGoodsAddressText', '')
                    elif 'product_info' in cache_data:
                        send_goods_address = cache_data['product_info'].get('sendGoodsAddressText', '')

                    if send_goods_address:
                        # 提取城市名（格式：江苏省 徐州市 → 徐州市）
                        import re
                        city_match = re.search(r'(\S+市)', send_goods_address)
                        if city_match:
                            city_name = city_match.group(1)
                            cache_attrs['产地'] = city_name
                            print(f"[微信小店上传] 从发货地址提取产地: {city_name}")

                print(f"[微信小店上传] 从缓存中找到 {len(cache_attrs)} 个属性")
                if cache_attrs:
                    print(f"[微信小店上传] 缓存属性详情: {cache_attrs}")

                for attr in product_attr_list:
                    if attr.get('is_required', False):  # 只处理必填属性
                        attr_name = attr['name']
                        attr_type = attr.get('type_v2', attr.get('type', 'string'))
                        attr_options = attr.get('value', '')

                        # 智能匹配属性值
                        attr_value = None

                        # 首先检查是否有固定值配置
                        if self.attr_mapping_config and 'global_attribute_mapping' in self.attr_mapping_config:
                            mapping_config = self.attr_mapping_config['global_attribute_mapping']
                            if attr_name in mapping_config:
                                attr_config = mapping_config[attr_name]

                                # 检查固定值配置
                                if 'fixed_value' in attr_config:
                                    fixed_value = attr_config['fixed_value']
                                    if not attr_options or fixed_value in [opt.strip() for opt in attr_options.split(';') if opt.strip()]:
                                        attr_value = fixed_value
                                        print(f"[微信小店上传] 使用固定值配置: {attr_name} = {fixed_value}")
                                    else:
                                        print(f"[微信小店上传] 固定值'{fixed_value}'不在可选项中，跳过固定值配置")

                        # 如果没有固定值配置，继续正常的匹配逻辑
                        if attr_value is None:
                            # 🔧 修复：优先检查特定类目映射，再检查缓存匹配
                            smart_match_value = self._smart_match_attr_value(attr_name, attr_type, attr_options, cache_attrs)

                            if smart_match_value:
                                # 智能匹配成功（包括特定类目映射）
                                attr_value = smart_match_value
                                print(f"[微信小店上传] 智能匹配属性: {attr_name} = {attr_value}")
                                # 记录智能匹配情况
                                self._log_attribute_mismatch(attr_name, "智能匹配", attr_value, "smart_match")
                            elif attr_name in cache_attrs:
                                # 缓存匹配：作为备选方案
                                original_value = cache_attrs[attr_name]
                                attr_value = self._format_attr_value(str(original_value), attr_name, attr_type)
                                # 对于选择类型，需要验证值是否在微信允许的选项中
                                if attr_type in ['select_one', 'select_many'] and attr_options:
                                    attr_value = self._match_to_wechat_options(attr_value, attr_options, attr_name)
                                print(f"[微信小店上传] 从缓存匹配属性: {attr_name} = {attr_value} (原始值: {original_value})")
                                # 记录属性匹配情况
                                self._log_attribute_mismatch(attr_name, str(original_value), attr_value, "cache_match")
                            else:
                                # 没有任何匹配，使用默认值
                                attr_value = self._get_default_attr_value(attr_name, attr_type, attr_options)
                                print(f"[微信小店上传] 使用默认属性: {attr_name} = {attr_value}")
                                # 记录默认值使用情况
                                self._log_attribute_mismatch(attr_name, "无匹配", attr_value, "default_value")

                        if attr_value:
                            required_attrs.append({
                                "attr_key": attr_name,
                                "attr_value": attr_value
                            })
                            print(f"[微信小店上传] 添加到必填属性列表: {attr_name} = {attr_value}")

                print(f"[微信小店上传] 从API获取到 {len(required_attrs)} 个必填属性")
                return required_attrs
            else:
                print(f"[微信小店上传] 无法获取类目详情，使用默认属性")

        except Exception as e:
            print(f"[微信小店上传] 获取类目详情失败: {e}")

        # 如果API调用失败，返回空数组（不添加任何属性）
        return []

    def _get_category_detail(self, cat_id: str) -> Dict:
        """获取类目详情"""
        try:
            if not self.wechat_api:
                print(f"[微信小店上传] 微信API未初始化")
                return {}

            # 调用微信类目详情接口
            response = self.wechat_api.get_category_detail(cat_id)

            if response.get('errcode') == 0:
                print(f"[微信小店上传] 成功获取类目 {cat_id} 详情")
                return response
            else:
                print(f"[微信小店上传] 获取类目详情失败: {response.get('errmsg', '未知错误')}")
                return {}

        except Exception as e:
            print(f"[微信小店上传] 获取类目详情异常: {e}")
            return {}

    def _get_default_attr_value(self, attr_name: str, attr_type: str, attr_options: str) -> str:
        """根据属性类型和选项获取默认值，使用智能匹配配置"""
        try:
            # 解析微信API返回的选项
            available_options = []
            if attr_options:
                available_options = [opt.strip() for opt in attr_options.split(';') if opt.strip()]

            # 使用智能匹配配置
            if self.attr_mapping_config and 'global_attribute_mapping' in self.attr_mapping_config:
                mapping_config = self.attr_mapping_config['global_attribute_mapping']

                if attr_name in mapping_config:
                    attr_config = mapping_config[attr_name]

                    # 注意：fixed_value 配置已在主属性匹配逻辑中处理，这里不再重复检查

                    # 检查是否有基于标题的配置
                    if 'title_based' in attr_config and attr_config['title_based']:
                        product_title = getattr(self, 'current_product_title', '')
                        if 'title_keywords' in attr_config:
                            title_keywords = attr_config['title_keywords']
                            for keyword, value in title_keywords.items():
                                if keyword in product_title and (not available_options or value in available_options):
                                    print(f"[微信小店上传] 基于标题关键词'{keyword}'匹配: {attr_name} = {value}")
                                    # 记录基于标题的匹配
                                    self._log_attribute_mismatch(attr_name, f"标题关键词:{keyword}", value, "title_based")
                                    return value
                        # 使用配置中的fallback
                        if 'fallback' in attr_config:
                            fallback_value = attr_config['fallback']
                            if not available_options or fallback_value in available_options:
                                print(f"[微信小店上传] 使用配置fallback: {attr_name} = {fallback_value}")
                                # 记录配置fallback的使用
                                self._log_attribute_mismatch(attr_name, "配置fallback", fallback_value, "config_fallback")
                                return fallback_value

                    # 优先级匹配
                    if 'priority_mapping' in attr_config and available_options:
                        priority_mapping = attr_config['priority_mapping']

                        # 按优先级查找匹配的选项
                        for preferred_value, keywords in priority_mapping.items():
                            if preferred_value in available_options:
                                print(f"[微信小店上传] 智能匹配属性: {attr_name} = {preferred_value} (优先级匹配)")
                                return preferred_value

                        # 模糊匹配
                        for preferred_value, keywords in priority_mapping.items():
                            for keyword in keywords:
                                # 使用difflib进行模糊匹配
                                matches = difflib.get_close_matches(keyword, available_options, n=1, cutoff=0.6)
                                if matches:
                                    print(f"[微信小店上传] 智能匹配属性: {attr_name} = {matches[0]} (模糊匹配: {keyword})")
                                    return matches[0]

                    # 使用fallback值
                    if 'fallback' in attr_config and attr_config['fallback'] in available_options:
                        fallback_value = attr_config['fallback']
                        print(f"[微信小店上传] 使用fallback值: {attr_name} = {fallback_value}")
                        return fallback_value

            # 处理不同类型的属性
            if attr_type in ['select_one', 'select_many'] and available_options:
                # 获取商品标题用于智能判断
                product_title = getattr(self, 'current_product_title', '')

                # 根据属性名和标题内容进行智能判断
                if attr_name == "出水方式":
                    if "吸管" in product_title and "吸管" in available_options:
                        print(f"[微信小店上传] 根据标题'{product_title}'智能判断: {attr_name} = 吸管")
                        return "吸管"
                    elif "按压" in product_title and "按压" in available_options:
                        print(f"[微信小店上传] 根据标题'{product_title}'智能判断: {attr_name} = 按压")
                        return "按压"
                    elif "直饮" in available_options:
                        print(f"[微信小店上传] 根据标题'{product_title}'智能判断: {attr_name} = 直饮 (默认)")
                        return "直饮"

                elif attr_name == "是否带吸管":
                    if "吸管" in product_title and "是" in available_options:
                        print(f"[微信小店上传] 根据标题'{product_title}'智能判断: {attr_name} = 是")
                        return "是"
                    elif "否" in available_options:
                        print(f"[微信小店上传] 根据标题'{product_title}'智能判断: {attr_name} = 否 (默认)")
                        return "否"

                elif attr_name == "杯身材质":
                    # 杯身材质匹配不到默认使用塑料
                    if "塑料" in available_options:
                        print(f"[微信小店上传] 杯身材质默认使用: 塑料")
                        return "塑料"

                # 选择类型，从可用选项中选择第一个
                selected = available_options[0]
                print(f"[微信小店上传] 选择类型属性: {attr_name} = {selected}")
                return selected
            elif attr_type == 'integer':
                # 整数类型
                return "1"
            elif attr_type == 'decimal4':
                # 小数类型
                return "1.0"
            elif attr_type == 'integer_unit' and available_options:
                # 整数+单位类型
                units = available_options
                if units:
                    return f"1 {units[0]}"
            elif attr_type == 'decimal4_unit' and available_options:
                # 小数+单位类型
                units = available_options
                if units:
                    return f"1.0 {units[0]}"
            elif attr_type == 'string':
                # 文本类型
                return "其他"

            # 如果没有匹配的处理方式，返回空字符串
            print(f"[微信小店上传] 无法为属性 {attr_name} (类型: {attr_type}) 生成默认值")
            return ""

        except Exception as e:
            print(f"[微信小店上传] 生成默认属性值失败: {e}")
            return ""

    def _apply_attribute_mapping(self, target_attr: str, attr_config: dict, cache_attrs: dict) -> str:
        """应用特定类目映射配置"""
        try:
            # 获取配置信息
            fallback = attr_config.get('fallback', '')
            priority_mapping = attr_config.get('priority_mapping', {})

            print(f"[微信小店上传] 应用特定类目映射: {target_attr}, fallback={fallback}")

            # 查找可能的1688属性值
            possible_values = []

            # 从缓存中查找相关属性值
            attr_mapping = {
                "材质": ["材质", "主要材质", "杯身材质", "内胆材质", "外壳材质"],
                "容量": ["容量", "体积", "规格"],
                "款式": ["款式", "风格", "样式"],
                "颜色": ["颜色", "色彩", "主色调"]
            }

            possible_attrs = attr_mapping.get(target_attr, [target_attr])

            for possible_attr in possible_attrs:
                if possible_attr in cache_attrs:
                    value = str(cache_attrs[possible_attr])
                    possible_values.append(value)
                    print(f"[微信小店上传] 找到1688属性值: {possible_attr}={value}")

            # 如果没有找到1688属性值，从标题中提取关键词
            if not possible_values and hasattr(self, 'current_title') and self.current_title:
                title = self.current_title
                print(f"[微信小店上传] 从标题中查找关键词: {title}")

                # 检查标题中是否包含映射的关键词
                for wechat_value, keywords in priority_mapping.items():
                    for keyword in keywords:
                        if keyword in title:
                            print(f"[微信小店上传] 配置智能匹配: 从标题'{title}' 中的关键词'{keyword}' 匹配到'{target_attr}': {wechat_value}")
                            return wechat_value

            # 使用优先级映射匹配
            for value in possible_values:
                for wechat_value, keywords in priority_mapping.items():
                    for keyword in keywords:
                        if keyword in value:
                            print(f"[微信小店上传] 配置智能匹配: 从缓存值'{value}' 中的关键词'{keyword}' 匹配到'{target_attr}': {wechat_value}")
                            return wechat_value

            # 如果没有匹配到，使用fallback
            if fallback:
                print(f"[微信小店上传] 使用fallback值: {target_attr}={fallback}")
                return fallback

            return None

        except Exception as e:
            print(f"[微信小店上传] 应用特定类目映射失败: {str(e)}")
            return None

    def _format_attr_value(self, value: str, attr_name: str, attr_type: str) -> str:
        """格式化属性值，处理微信平台不支持的格式"""
        try:
            # 处理容量等带单位的范围值
            if attr_name in ['容量', '重量', '尺寸'] and '-' in value:
                import re

                # 🔧 优化：处理多种范围格式
                # 格式1: "201mL(含)-300mL(含)" -> "201ml"
                # 格式2: "401-500ml" -> "401ml"
                # 格式3: "401ml-500ml" -> "401ml"
                # 格式4: "1-2L" -> "1l"

                # 先尝试匹配复杂格式：数字+单位(含)-数字+单位(含)
                complex_match = re.search(r'(\d+)(\w+)\([^)]*\)-(\d+)(\w+)\([^)]*\)', value)
                if complex_match:
                    min_val, min_unit, max_val, max_unit = complex_match.groups()
                    # 使用最小值和单位，并转换为小写
                    unit = min_unit.lower()
                    formatted_value = f"{min_val}{unit}"
                    print(f"[微信小店上传] 格式化复杂范围属性: {attr_name} {value} -> {formatted_value}")
                    return formatted_value

                # 尝试匹配单位+数字-单位+数字格式：401ml-500ml
                unit_range_match = re.search(r'(\d+)(\w+)-(\d+)(\w+)', value)
                if unit_range_match:
                    min_val, min_unit, max_val, max_unit = unit_range_match.groups()
                    # 使用最小值和单位，并转换为小写
                    unit = min_unit.lower()
                    formatted_value = f"{min_val}{unit}"
                    print(f"[微信小店上传] 格式化单位范围属性: {attr_name} {value} -> {formatted_value}")
                    return formatted_value

                # 最后尝试匹配简单格式：数字-数字+单位
                simple_match = re.search(r'(\d+)-(\d+)(\w+)', value)
                if simple_match:
                    min_val, max_val, unit = simple_match.groups()
                    # 使用最小值和单位，并转换为小写
                    unit = unit.lower()
                    formatted_value = f"{min_val}{unit}"
                    print(f"[微信小店上传] 格式化简单范围属性: {attr_name} {value} -> {formatted_value}")
                    return formatted_value

            # 其他属性直接返回原值
            return value

        except Exception as e:
            print(f"[微信小店上传] 格式化属性值失败: {e}")
            return value

    def _match_to_wechat_options(self, value: str, options_str: str, attr_name: str) -> str:
        """将属性值匹配到微信允许的选项中"""
        try:
            available_options = [opt.strip() for opt in options_str.split(';') if opt.strip()]

            # 直接匹配
            if value in available_options:
                return value

            # 模糊匹配
            import difflib
            matches = difflib.get_close_matches(value, available_options, n=1, cutoff=0.6)
            if matches:
                print(f"[微信小店上传] 模糊匹配 {attr_name}: {value} -> {matches[0]}")
                return matches[0]

            # 关键词匹配
            value_lower = value.lower()
            for option in available_options:
                option_lower = option.lower()
                if option_lower in value_lower or value_lower in option_lower:
                    print(f"[微信小店上传] 关键词匹配 {attr_name}: {value} -> {option}")
                    return option

            # 特殊规则匹配 - 根据微信实际允许的选项进行匹配
            if attr_name == "材质":
                # 塑料相关材质的智能匹配
                if "塑料" in value or "塑胶" in value:
                    if "塑料" in available_options:
                        return "塑料"
                    elif "复合材料" in available_options:
                        print(f"[微信小店上传] 材质匹配: {value} -> 复合材料 (塑料类材质)")
                        return "复合材料"
                    elif "其他" in available_options:
                        print(f"[微信小店上传] 材质匹配: {value} -> 其他 (塑料类材质)")
                        return "其他"

                # 玻璃相关材质
                elif "玻璃" in value:
                    if "玻璃" in available_options:
                        return "玻璃"
                    elif "玻璃钢" in available_options:
                        print(f"[微信小店上传] 材质匹配: {value} -> 玻璃钢 (玻璃类材质)")
                        return "玻璃钢"
                    elif "其他" in available_options:
                        return "其他"

                # 金属相关材质
                elif "不锈钢" in value or "金属" in value or "铝" in value or "钛" in value:
                    if "不锈钢" in available_options:
                        return "不锈钢"
                    elif "铝合金" in available_options:
                        print(f"[微信小店上传] 材质匹配: {value} -> 铝合金 (金属类材质)")
                        return "铝合金"
                    elif "钛合金" in available_options:
                        print(f"[微信小店上传] 材质匹配: {value} -> 钛合金 (金属类材质)")
                        return "钛合金"
                    elif "其他" in available_options:
                        return "其他"

                # 碳纤维相关材质
                elif "碳纤维" in value or "碳素" in value:
                    if "碳纤维" in available_options:
                        return "碳纤维"
                    elif "高碳纤维" in available_options:
                        print(f"[微信小店上传] 材质匹配: {value} -> 高碳纤维 (碳纤维类材质)")
                        return "高碳纤维"
                    elif "复合材料" in available_options:
                        return "复合材料"
                    elif "其他" in available_options:
                        return "其他"

            # 特殊处理：杯身材质匹配不上时使用塑料
            if attr_name == "杯身材质":
                if "塑料" in available_options:
                    print(f"[微信小店上传] 杯身材质无法匹配 {value}，使用默认值: 塑料")
                    return "塑料"

            # 如果都匹配不上，返回"其他"（如果存在）
            if "其他" in available_options:
                print(f"[微信小店上传] 无法匹配 {attr_name}: {value}，使用'其他'")
                return "其他"

            # 最后返回第一个选项
            if available_options:
                print(f"[微信小店上传] 无法匹配 {attr_name}: {value}，使用第一个选项: {available_options[0]}")
                return available_options[0]

            return value

        except Exception as e:
            print(f"[微信小店上传] 匹配微信选项失败: {e}")
            return value

    def _smart_match_attr_value(self, target_attr: str, attr_type: str, attr_options: str, cache_attrs: dict) -> str:
        """智能匹配：从1688缓存属性中找到最合适的值"""
        try:
            # 🔧 修复：优先检查特定类目映射，再进行常规匹配
            if self.attr_mapping_config:
                # 首先尝试类目特定的配置
                if hasattr(self, 'current_category_id') and self.current_category_id:
                    category_mapping = self.attr_mapping_config.get('category_specific_mapping', {})
                    print(f"[微信小店上传] 🔍 查找特定类目映射: current_category_id={self.current_category_id}, 可用配置={list(category_mapping.keys())}")

                    # 查找匹配的类目配置
                    matched_config = None
                    matched_name = None

                    # 方式1：直接匹配类目ID
                    if str(self.current_category_id) in category_mapping:
                        matched_config = category_mapping[str(self.current_category_id)]
                        matched_name = str(self.current_category_id)
                        print(f"[微信小店上传] ✅ 方式1匹配成功: 直接匹配类目ID {self.current_category_id}")
                    else:
                        print(f"[微信小店上传] ❌ 方式1失败: 类目ID {self.current_category_id} 不在配置键中")

                        # 方式2：通过category_ids数组匹配
                        for config_name, config_data in category_mapping.items():
                            category_ids = config_data.get('category_ids', [])
                            print(f"[微信小店上传] 🔍 检查配置'{config_name}': category_ids={category_ids}")
                            if str(self.current_category_id) in category_ids:
                                matched_config = config_data
                                matched_name = config_name
                                print(f"[微信小店上传] ✅ 方式2匹配成功: 在'{config_name}'的category_ids中找到{self.current_category_id}")
                                break

                        if not matched_config:
                            print(f"[微信小店上传] ❌ 方式2失败: 类目ID {self.current_category_id} 不在任何category_ids中")

                    if matched_config:
                        # 支持两种格式：attributes 和 override_attributes
                        override_attrs = matched_config.get('override_attributes', matched_config.get('attributes', {}))
                        print(f"[微信小店上传] 🔍 检查'{matched_name}'配置中的属性: {list(override_attrs.keys())}")
                        if target_attr in override_attrs:
                            attr_config = override_attrs[target_attr]
                            print(f"[微信小店上传] ✅ 使用类目{matched_name}特定配置: {target_attr}")

                            # 使用特定类目映射进行匹配
                            matched_value = self._apply_attribute_mapping(target_attr, attr_config, cache_attrs)
                            if matched_value:
                                return matched_value
                        else:
                            print(f"[微信小店上传] ❌ 属性'{target_attr}'不在'{matched_name}'的特定配置中")

            # 如果特定类目映射没有匹配，使用常规匹配逻辑
            print(f"[微信小店上传] 使用常规匹配逻辑: {target_attr}")

            # 属性名映射规则
            attr_mapping = {
                "材质": ["材质", "主要材质", "杯身材质", "内胆材质", "外壳材质"],
                "内胆材质": ["内胆材质", "材质", "主要材质"],
                "杯身材质": ["杯身材质", "材质", "主要材质"],
                "颜色": ["颜色", "色彩", "主色调"],
                "容量": ["容量", "体积", "规格"],
                "重量": ["重量", "净重", "毛重"],
                "尺寸": ["尺寸", "规格", "大小", "尺寸(高度、口径)"],
                "产地": ["产地", "生产地", "制造地"],
                "适用人群": ["适用人群", "适用对象", "目标人群"],
                "功能": ["功能", "特点", "用途"],
                "风格": ["风格", "款式", "样式"],
                "形状": ["形状", "外形", "造型"]
            }

            # 查找匹配的1688属性
            possible_attrs = attr_mapping.get(target_attr, [target_attr])

            for possible_attr in possible_attrs:
                if possible_attr in cache_attrs:
                    original_value = str(cache_attrs[possible_attr])

                    # 格式化值
                    formatted_value = self._format_attr_value(original_value, target_attr, attr_type)

                    # 如果是选择类型，匹配到微信选项
                    if attr_type in ['select_one', 'select_many'] and attr_options:
                        matched_value = self._match_to_wechat_options(formatted_value, attr_options, target_attr)
                        print(f"[微信小店上传] 从1688属性'{possible_attr}': {original_value} 匹配到微信'{target_attr}': {matched_value}")
                        return matched_value
                    else:
                        print(f"[微信小店上传] 从1688属性'{possible_attr}': {original_value} 映射到微信'{target_attr}': {formatted_value}")
                        return formatted_value

            # 如果没有直接匹配，尝试使用全局配置文件的智能匹配
            if self.attr_mapping_config:
                attr_config = None

                # 首先尝试类目特定的配置
                if hasattr(self, 'current_category_id') and self.current_category_id:
                    category_mapping = self.attr_mapping_config.get('category_specific_mapping', {})
                    print(f"[微信小店上传] 🔍 查找特定类目映射: current_category_id={self.current_category_id}, 可用配置={list(category_mapping.keys())}")

                    # 查找匹配的类目配置
                    matched_config = None
                    matched_name = None

                    # 方式1：直接匹配类目ID
                    if str(self.current_category_id) in category_mapping:
                        matched_config = category_mapping[str(self.current_category_id)]
                        matched_name = str(self.current_category_id)
                        print(f"[微信小店上传] ✅ 方式1匹配成功: 直接匹配类目ID {self.current_category_id}")
                    else:
                        print(f"[微信小店上传] ❌ 方式1失败: 类目ID {self.current_category_id} 不在配置键中")

                        # 方式2：通过category_ids数组匹配
                        for config_name, config_data in category_mapping.items():
                            category_ids = config_data.get('category_ids', [])
                            print(f"[微信小店上传] 🔍 检查配置'{config_name}': category_ids={category_ids}")
                            if str(self.current_category_id) in category_ids:
                                matched_config = config_data
                                matched_name = config_name
                                print(f"[微信小店上传] ✅ 方式2匹配成功: 在'{config_name}'的category_ids中找到{self.current_category_id}")
                                break

                        if not matched_config:
                            print(f"[微信小店上传] ❌ 方式2失败: 类目ID {self.current_category_id} 不在任何category_ids中")

                    if matched_config:
                        # 支持两种格式：attributes 和 override_attributes
                        override_attrs = matched_config.get('override_attributes', matched_config.get('attributes', {}))
                        print(f"[微信小店上传] 🔍 检查'{matched_name}'配置中的属性: {list(override_attrs.keys())}")
                        if target_attr in override_attrs:
                            attr_config = override_attrs[target_attr]
                            print(f"[微信小店上传] ✅ 使用类目{matched_name}特定配置: {target_attr}")
                        else:
                            print(f"[微信小店上传] ❌ 属性'{target_attr}'不在'{matched_name}'的特定配置中")
                    else:
                        print(f"[微信小店上传] ❌ 未找到匹配的特定类目配置")

                # 如果没有类目特定配置，使用全局配置
                if not attr_config and 'global_attribute_mapping' in self.attr_mapping_config:
                    mapping_config = self.attr_mapping_config['global_attribute_mapping']
                    if target_attr in mapping_config:
                        attr_config = mapping_config[target_attr]
                        print(f"[微信小店上传] 使用全局配置: {target_attr}")

                # 如果找到了配置，进行智能匹配
                if attr_config and 'priority_mapping' in attr_config:
                    priority_mapping = attr_config['priority_mapping']

                    # 首先检查商品标题（优先级最高）
                    product_title = getattr(self, 'current_product_title', '')
                    if product_title:
                        title_str = product_title.lower()
                        for wechat_value, keywords in priority_mapping.items():
                            for keyword in keywords:
                                if keyword.lower() in title_str:
                                    print(f"[微信小店上传] 配置智能匹配: 从标题'{product_title}' 中的关键词'{keyword}' 匹配到'{target_attr}': {wechat_value}")
                                    return wechat_value

                    # 然后检查缓存属性
                    for cache_attr_name, cache_attr_value in cache_attrs.items():
                        cache_value_str = str(cache_attr_value).lower()

                        # 检查每个优先级选项的关键词
                        for wechat_value, keywords in priority_mapping.items():
                            for keyword in keywords:
                                if keyword.lower() in cache_value_str:
                                    print(f"[微信小店上传] 配置智能匹配: 从'{cache_attr_name}': {cache_attr_value} 中的关键词'{keyword}' 匹配到'{target_attr}': {wechat_value}")
                                    return wechat_value

                    # 如果没有匹配到，使用fallback
                    if 'fallback' in attr_config:
                        fallback_value = attr_config['fallback']
                        print(f"[微信小店上传] 配置智能匹配: 使用fallback值 '{target_attr}': {fallback_value}")
                        return fallback_value

            return ""

        except Exception as e:
            print(f"[微信小店上传] 智能匹配属性失败: {e}")
            return ""

    def _log_attribute_mismatch(self, attr_name: str, original_value: str, final_value: str, match_type: str):
        """记录属性匹配情况，用于自动更新配置"""
        try:
            validation_config = self.attr_mapping_config.get('validation_config', {})

            if validation_config.get('log_mismatches', True):
                print(f"[属性匹配日志] {attr_name}: '{original_value}' -> '{final_value}' ({match_type})")

            # 如果启用自动更新配置
            if validation_config.get('auto_update_mapping', True):
                self._record_mapping_update(attr_name, original_value, final_value, match_type)

        except Exception as e:
            print(f"[微信小店上传] 记录属性匹配日志失败: {e}")

    def _record_mapping_update(self, attr_name: str, original_value: str, final_value: str, match_type: str):
        """记录需要更新的属性映射"""
        try:
            # 记录需要改进的匹配更新
            if match_type in ['fallback_used', 'first_option_used', 'manual_match', 'default_value', 'smart_match']:
                if attr_name not in self.attr_mapping_updates:
                    self.attr_mapping_updates[attr_name] = {
                        'priority_mapping': {},
                        'suggested_updates': []
                    }

                # 记录建议的映射更新
                if original_value and final_value and original_value != final_value:
                    suggestion = {
                        'original_value': original_value,
                        'final_value': final_value,
                        'match_type': match_type,
                        'frequency': 1
                    }

                    # 检查是否已存在相同的建议
                    existing = None
                    for item in self.attr_mapping_updates[attr_name]['suggested_updates']:
                        if item['original_value'] == original_value and item['final_value'] == final_value:
                            existing = item
                            break

                    if existing:
                        existing['frequency'] += 1
                    else:
                        self.attr_mapping_updates[attr_name]['suggested_updates'].append(suggestion)

        except Exception as e:
            print(f"[微信小店上传] 记录映射更新失败: {e}")

    def _save_mapping_updates(self):
        """保存属性映射更新到配置文件"""
        try:
            validation_config = self.attr_mapping_config.get('validation_config', {})

            if not validation_config.get('auto_update_mapping', True):
                return

            if not self.attr_mapping_updates:
                return

            print(f"[微信小店上传] 发现 {len(self.attr_mapping_updates)} 个属性需要更新映射")

            # 生成更新建议报告
            update_report = {
                'timestamp': datetime.now().isoformat(),
                'suggested_updates': self.attr_mapping_updates
            }

            # 保存到单独的更新建议文件
            report_path = "config/属性映射更新建议.json"
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(update_report, f, ensure_ascii=False, indent=2)

            print(f"[微信小店上传] 属性映射更新建议已保存到: {report_path}")

            # 如果频次足够高，自动更新配置文件
            self._auto_update_config_if_needed()

        except Exception as e:
            print(f"[微信小店上传] 保存映射更新失败: {e}")

    def _auto_update_config_if_needed(self):
        """如果建议频次足够高，自动更新配置文件"""
        try:
            auto_update_threshold = 3  # 出现3次以上的建议才自动更新

            for attr_name, updates in self.attr_mapping_updates.items():
                for suggestion in updates['suggested_updates']:
                    if suggestion['frequency'] >= auto_update_threshold:
                        self._apply_config_update(attr_name, suggestion)

        except Exception as e:
            print(f"[微信小店上传] 自动更新配置失败: {e}")

    def _apply_config_update(self, attr_name: str, suggestion: dict):
        """应用配置更新"""
        try:
            if 'global_attribute_mapping' not in self.attr_mapping_config:
                self.attr_mapping_config['global_attribute_mapping'] = {}

            mapping_config = self.attr_mapping_config['global_attribute_mapping']

            if attr_name not in mapping_config:
                mapping_config[attr_name] = {'priority_mapping': {}}

            if 'priority_mapping' not in mapping_config[attr_name]:
                mapping_config[attr_name]['priority_mapping'] = {}

            # 添加新的映射规则
            final_value = suggestion['final_value']
            original_value = suggestion['original_value']

            if final_value not in mapping_config[attr_name]['priority_mapping']:
                mapping_config[attr_name]['priority_mapping'][final_value] = []

            if original_value not in mapping_config[attr_name]['priority_mapping'][final_value]:
                mapping_config[attr_name]['priority_mapping'][final_value].append(original_value)
                print(f"[微信小店上传] 自动更新配置: {attr_name}.{final_value} 添加关键词 '{original_value}'")

                # 保存更新后的配置
                self._save_updated_config()

        except Exception as e:
            print(f"[微信小店上传] 应用配置更新失败: {e}")

    def _save_updated_config(self):
        """保存更新后的配置文件"""
        try:
            config_path = "config/属性智能匹配.json"
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.attr_mapping_config, f, ensure_ascii=False, indent=2)
            print(f"[微信小店上传] 配置文件已更新: {config_path}")

        except Exception as e:
            print(f"[微信小店上传] 保存配置文件失败: {e}")

    def _add_required_attrs(self, attrs: List[Dict]) -> List[Dict]:
        """添加必填属性（如果缺少）并修正属性值格式"""
        # 检查已有的属性名
        existing_attr_keys = {attr['attr_key'] for attr in attrs}

        # 定义必填属性及其默认值
        required_attrs = {
            "面料材质": "棉",
            "面料材质成分含量（%）": "100 %"  # 格式：数字 单位
        }

        # 添加缺少的必填属性
        for attr_key, default_value in required_attrs.items():
            if attr_key not in existing_attr_keys:
                attrs.append({
                    "attr_key": attr_key,
                    "attr_value": default_value
                })
                print(f"[微信小店上传] 添加必填属性: {attr_key} = {default_value}")

        # 修正已有属性的值格式
        attrs = self._fix_attr_values(attrs)

        return attrs

    def _fix_attr_values(self, attrs: List[Dict]) -> List[Dict]:
        """修正属性值格式以符合微信API要求"""
        # 需要移除的有问题属性
        problematic_attrs = ["风格"]  # 暂时移除风格属性，因为不知道正确的选项

        # 过滤掉有问题的属性
        filtered_attrs = []
        for attr in attrs:
            attr_key = attr['attr_key']
            attr_value = attr['attr_value']

            # 跳过有问题的属性
            if attr_key in problematic_attrs:
                print(f"[微信小店上传] 跳过有问题的属性: {attr_key} = {attr_value}")
                continue

            # 特殊处理百分比格式
            if "%" in attr_key and "%" in attr_value:
                # 将 "其他100%" 格式转换为 "100 %" 格式
                import re
                match = re.search(r'(\d+(?:\.\d+)?)%?', attr_value)
                if match:
                    number = match.group(1)
                    attr['attr_value'] = f"{number} %"
                    print(f"[微信小店上传] 修正百分比格式: {attr_key} {attr_value} -> {attr['attr_value']}")

            # 修正面料材质的值
            if attr_key == "面料材质" and attr_value == "其他":
                attr['attr_value'] = "棉"
                print(f"[微信小店上传] 修正面料材质: 其他 -> 棉")

            filtered_attrs.append(attr)

        return filtered_attrs
    
    def _process_images(self, cache_data: Dict, field_mapping: Dict) -> Dict:
        """处理图片信息 - 按照模板配置处理"""
        print(f"[微信小店上传] ===== 开始处理图片信息 =====")
        print(f"[微信小店上传] cache_data keys: {list(cache_data.keys())}")
        print(f"[微信小店上传] field_mapping keys: {list(field_mapping.keys())}")
        result = {}

        # 处理主图 - 按照模板配置的 head_imgs 字段
        if 'head_imgs' in field_mapping:
            head_img_config = field_mapping['head_imgs']
            source_field = head_img_config.get('source_field', 'main_images')

            # 根据source_field获取图片数据
            if source_field == 'main_images':
                # 从 product_image.images 获取
                product_image_data = cache_data.get('product_image', {})
                if isinstance(product_image_data, dict):
                    main_images = product_image_data.get('images', [])
                else:
                    main_images = product_image_data if isinstance(product_image_data, list) else []
            elif source_field == 'product_image.images':
                # 从 product_info.product_image.images 获取
                print(f"[微信小店上传] 调试 - 缓存数据顶层键: {list(cache_data.keys())}")

                # 检查是否有 product_info 层级
                if 'product_info' in cache_data:
                    product_info = cache_data['product_info']
                    product_image_data = product_info.get('product_image', {})
                    print(f"[微信小店上传] 调试 - 从 product_info.product_image 获取")
                else:
                    # 直接从顶层获取
                    product_image_data = cache_data.get('product_image', {})
                    print(f"[微信小店上传] 调试 - 从顶层 product_image 获取")

                print(f"[微信小店上传] 调试 - product_image 数据类型: {type(product_image_data)}")
                print(f"[微信小店上传] 调试 - product_image 内容: {product_image_data}")

                if isinstance(product_image_data, dict):
                    main_images = product_image_data.get('images', [])
                    print(f"[微信小店上传] 调试 - images 字段内容: {main_images}")
                else:
                    main_images = []
                    print(f"[微信小店上传] 调试 - product_image 不是字典类型")
                print(f"[微信小店上传] 从 product_image.images 获取到 {len(main_images)} 张主图")
            else:
                # 直接从指定字段获取
                main_images = cache_data.get(source_field, [])

            if isinstance(main_images, str):
                main_images = [main_images]

            # 处理主图上传
            if main_images:
                # 应用搬家设置中的主图过滤
                filtered_main_images = self._filter_images(main_images, 'main_image')

                if not filtered_main_images:
                    # 检查是否设置了主图过滤参数
                    main_filter_settings = self.image_filter_settings.get('main_image', {})
                    delete_before = main_filter_settings.get('delete_before', 0)
                    delete_after = main_filter_settings.get('delete_after', 0)

                    if delete_before > 0 or delete_after > 0:
                        print(f"[微信小店上传] 主图过滤后为空，按用户设置跳过主图上传")
                        uploaded_main_imgs = []
                    else:
                        print(f"[微信小店上传] 主图过滤后为空且未设置过滤，使用原始图片")
                        filtered_main_images = main_images
                        max_count = head_img_config.get('max_count', 9)
                        uploaded_main_imgs = self._upload_images_batch(filtered_main_images[:max_count])
                else:
                    max_count = head_img_config.get('max_count', 9)
                    print(f"[微信小店上传] 开始处理 {len(filtered_main_images)} 张主图（过滤后）")
                    try:
                        uploaded_main_imgs = self._upload_images_batch(filtered_main_images[:max_count])
                    except Exception as e:
                        # 检查是否是access_token过期异常
                        if "ACCESS_TOKEN_EXPIRED" in str(e):
                            print(f"[微信小店上传] 主图上传时检测到access_token过期: {e}")
                            # 重新抛出异常，让上层处理
                            raise Exception(f"ACCESS_TOKEN_EXPIRED: {str(e)}")
                        else:
                            # 其他异常正常处理
                            raise e

                # 微信小店要求最少3张主图
                min_count = 3
                if uploaded_main_imgs and len(uploaded_main_imgs) >= min_count:
                    result['head_imgs'] = uploaded_main_imgs  # 使用 head_imgs 复数形式
                    print(f"[微信小店上传] 主图上传完成，成功 {len(uploaded_main_imgs)} 张")
                    print(f"[微信小店上传] head_imgs: {uploaded_main_imgs}")
                else:
                    # 如果图片上传失败，抛出异常停止处理
                    raise Exception(f"主图上传失败，微信小店要求至少 {min_count} 张主图，实际成功 {len(uploaded_main_imgs) if uploaded_main_imgs else 0} 张")
            else:
                # 如果没有主图，抛出异常
                raise Exception("商品缺少主图，无法提交到微信小店")
        else:
            # 如果模板中没有配置head_imgs，使用默认处理
            result['head_imgs'] = []
            print("[微信小店上传] 模板中未配置head_imgs字段")

        # 处理详情图 - 从缓存的 description HTML 中提取图片链接
        print(f"[微信小店上传] 开始处理详情图")

        # 从正确的路径获取详情HTML
        description_html = ''
        if 'product_info' in cache_data:
            description_html = cache_data['product_info'].get('description', '')
        else:
            description_html = cache_data.get('description', '')

        print(f"[微信小店上传] 详情HTML长度: {len(description_html)}")
        print(f"[微信小店上传] 详情HTML前200字符: {description_html[:200]}")
        detail_images = []

        if description_html:
            # 先过滤掉广告图片
            filtered_description_html = self._filter_description_images(description_html)
            print(f"[微信小店上传] 过滤广告图片后HTML长度: {len(filtered_description_html)}")

            # 从过滤后的HTML中提取所有图片链接 - 使用更简单的正则表达式
            import re
            img_pattern = r'src=["\']([^"\']+)["\']'
            img_matches = re.findall(img_pattern, filtered_description_html)
            print(f"[微信小店上传] 正则匹配到 {len(img_matches)} 个src属性")

            # 过滤出有效的图片链接
            for img_url in img_matches:
                img_url = img_url.strip()
                print(f"[微信小店上传] 检查图片URL: {img_url[:100]}...")
                if img_url and ('alicdn.com' in img_url or 'alibaba.com' in img_url):
                    # 清理URL参数
                    if '?' in img_url:
                        img_url = img_url.split('?')[0]
                    detail_images.append(img_url)
                    print(f"[微信小店上传] 添加有效图片: {img_url}")

            print(f"[微信小店上传] 从详情描述中提取到 {len(detail_images)} 张有效图片")

        if detail_images:
            # 应用搬家设置中的详情图过滤
            filtered_detail_images = self._filter_images(detail_images, 'detail_image')

            if not filtered_detail_images:
                # 检查是否设置了详情图过滤参数
                detail_filter_settings = self.image_filter_settings.get('detail_image', {})
                delete_before = detail_filter_settings.get('delete_before', 0)
                delete_after = detail_filter_settings.get('delete_after', 0)

                if delete_before > 0 or delete_after > 0:
                    print(f"[微信小店上传] 详情图过滤后为空，按用户设置跳过详情图上传")
                    filtered_detail_images = []
                else:
                    print(f"[微信小店上传] 详情图过滤后为空且未设置过滤，使用原始图片")
                    filtered_detail_images = detail_images

            if filtered_detail_images:
                print(f"[微信小店上传] 开始处理 {len(filtered_detail_images)} 张详情图（过滤后）")
                try:
                    uploaded_detail_imgs = self._upload_images_batch(filtered_detail_images[:20])  # 最多20张详情图
                    print(f"[微信小店上传] 上传结果: {uploaded_detail_imgs}")
                except Exception as e:
                    # 检查是否是access_token过期异常
                    if "ACCESS_TOKEN_EXPIRED" in str(e):
                        print(f"[微信小店上传] 详情图上传时检测到access_token过期: {e}")
                        # 重新抛出异常，让上层处理
                        raise Exception(f"ACCESS_TOKEN_EXPIRED: {str(e)}")
                    else:
                        # 其他异常正常处理
                        raise e
            else:
                print(f"[微信小店上传] 详情图过滤后为空，跳过上传")
                uploaded_detail_imgs = []

            if uploaded_detail_imgs:
                # 替换HTML中的图片链接为微信链接
                processed_description = description_html
                successful_uploads = 0

                for original_url, wechat_url in zip(filtered_detail_images[:20], uploaded_detail_imgs):
                    if wechat_url:
                        processed_description = processed_description.replace(original_url, wechat_url)
                        successful_uploads += 1
                        print(f"[微信小店上传] 详情图上传成功: {wechat_url}")
                    else:
                        print(f"[微信小店上传] 详情图上传失败: {original_url}")

                # 只保留上传成功的图片
                successful_imgs = [url for url in uploaded_detail_imgs if url]

                result['desc_info'] = {
                    'desc': '',  # 空描述，避免过长
                    'imgs': successful_imgs
                }
                print(f"[微信小店上传] 详情图处理完成，成功 {successful_uploads}/{len(filtered_detail_images)} 张")
                print(f"[微信小店上传] 最终imgs数组: {successful_imgs}")
            else:
                # 如果没有上传成功的详情图，使用空的desc_info
                result['desc_info'] = {
                    'desc': '',
                    'imgs': []
                }
                print(f"[微信小店上传] 详情图上传为空，使用空的desc_info")
        else:
            print("[微信小店上传] 没有找到有效的详情图片，尝试使用主图作为详情图")
            # 如果没有详情图，使用已经上传的主图作为详情图
            if 'head_imgs' in result and result['head_imgs']:
                # 直接使用已经上传的主图
                successful_imgs = result['head_imgs'][:3]  # 使用前3张主图作为详情图

                result['desc_info'] = {
                    'desc': '',  # 空描述，避免过长
                    'imgs': successful_imgs
                }
                print(f"[微信小店上传] 使用已上传的主图作为详情图: {len(successful_imgs)} 张")
                print(f"[微信小店上传] 详情图URLs: {successful_imgs}")
            else:
                # 如果连主图都没有，这是异常情况
                result['desc_info'] = {
                    'desc': '',  # 空描述，避免过长
                    'imgs': []
                }
                print(f"[微信小店上传] 警告：没有找到任何图片作为详情图！")

        return result

    def _process_desc_info(self, cache_data: Dict) -> Dict:
        """处理详情信息 - 从HTML中提取图片并上传"""
        try:
            # 获取详情HTML
            description_html = cache_data.get('description', '')
            print(f"[微信小店上传] 详情HTML长度: {len(description_html)}")

            if not description_html:
                print("[微信小店上传] 没有找到详情描述")
                return {
                    'desc': '',
                    'imgs': []
                }

            # 先过滤掉广告图片
            filtered_description_html = self._filter_description_images(description_html)
            print(f"[微信小店上传] 过滤广告图片后HTML长度: {len(filtered_description_html)}")

            # 从过滤后的HTML中提取所有图片链接
            import re
            img_pattern = r'src=["\']([^"\']+)["\']'
            img_matches = re.findall(img_pattern, filtered_description_html)
            print(f"[微信小店上传] 正则匹配到 {len(img_matches)} 个src属性")

            # 过滤出有效的图片链接
            detail_images = []
            for img_url in img_matches:
                img_url = img_url.strip()
                print(f"[微信小店上传] 检查图片URL: {img_url[:100]}...")
                if img_url and ('alicdn.com' in img_url or 'alibaba.com' in img_url):
                    # 清理URL参数
                    if '?' in img_url:
                        img_url = img_url.split('?')[0]
                    detail_images.append(img_url)
                    print(f"[微信小店上传] 添加有效图片: {img_url}")

            print(f"[微信小店上传] 从详情描述中提取到 {len(detail_images)} 张有效图片")

            if detail_images:
                # 应用搬家设置中的详情图过滤
                filtered_detail_images = self._filter_images(detail_images, 'detail_image')

                if not filtered_detail_images:
                    # 检查是否设置了详情图过滤参数
                    detail_filter_settings = self.image_filter_settings.get('detail_image', {})
                    delete_before = detail_filter_settings.get('delete_before', 0)
                    delete_after = detail_filter_settings.get('delete_after', 0)

                    if delete_before > 0 or delete_after > 0:
                        print(f"[微信小店上传] 详情图过滤后为空，按用户设置跳过详情图上传")
                        filtered_detail_images = []
                    else:
                        print(f"[微信小店上传] 详情图过滤后为空且未设置过滤，使用原始图片")
                        filtered_detail_images = detail_images

                if filtered_detail_images:
                    # 上传详情图片（最多20张）
                    print(f"[微信小店上传] 开始上传 {len(filtered_detail_images[:20])} 张详情图（过滤后）")
                    uploaded_detail_imgs = self._upload_images_batch(filtered_detail_images[:20])
                    print(f"[微信小店上传] 上传结果: {uploaded_detail_imgs}")
                else:
                    print(f"[微信小店上传] 详情图过滤后为空，跳过上传")
                    uploaded_detail_imgs = []

                # 替换HTML中的图片链接为微信链接
                processed_description = description_html
                successful_uploads = 0

                for original_url, wechat_url in zip(detail_images[:20], uploaded_detail_imgs):
                    if wechat_url:
                        processed_description = processed_description.replace(original_url, wechat_url)
                        successful_uploads += 1
                        print(f"[微信小店上传] 详情图上传成功: {wechat_url}")
                    else:
                        print(f"[微信小店上传] 详情图上传失败: {original_url}")

                # 只保留上传成功的图片
                successful_imgs = [url for url in uploaded_detail_imgs if url]

                print(f"[微信小店上传] 详情图处理完成，成功 {successful_uploads}/{len(detail_images[:20])} 张")
                print(f"[微信小店上传] 最终imgs数组: {successful_imgs}")

                return {
                    'desc': processed_description,
                    'imgs': successful_imgs
                }
            else:
                print("[微信小店上传] 没有找到有效的详情图片，尝试使用主图作为详情图")
                # 如果没有详情图，尝试使用主图
                main_images = cache_data.get('product_image', {}).get('images', [])
                if main_images:
                    # 应用主图过滤设置
                    filtered_main_images = self._filter_images(main_images, 'main_image')
                    if filtered_main_images:
                        print(f"[微信小店上传] 使用主图作为详情图: {len(filtered_main_images)} 张（过滤后）")
                        uploaded_main_imgs = self._upload_images_batch(filtered_main_images[:5])  # 最多5张主图作为详情图
                        successful_imgs = [url for url in uploaded_main_imgs if url]
                    else:
                        print(f"[微信小店上传] 主图过滤后为空，无法作为详情图")
                        successful_imgs = []

                    return {
                        'desc': description_html or "商品详情",
                        'imgs': successful_imgs
                    }
                else:
                    return {
                        'desc': description_html or "商品详情",
                        'imgs': []
                    }

        except Exception as e:
            print(f"[微信小店上传] 处理详情信息失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                'desc': cache_data.get('description', ''),
                'imgs': []
            }

    def _get_freight_template_id(self) -> str:
        """获取运费模板ID"""
        try:
            # 尝试从外部传入的运费模板ID获取
            if hasattr(self, 'freight_template_id') and self.freight_template_id:
                print(f"[微信小店上传] 使用传入的运费模板ID: {self.freight_template_id}")
                return str(self.freight_template_id)

            # 默认运费模板ID
            print(f"[微信小店上传] 使用默认运费模板ID: 1")
            return "1"

        except Exception as e:
            print(f"[微信小店上传] 获取运费模板ID失败: {e}")
            return "1"

    def set_freight_template_id(self, template_id: str):
        """设置运费模板ID"""
        self.freight_template_id = template_id
        print(f"[微信小店上传] 设置运费模板ID: {template_id}")

    def _get_return_address_id(self) -> int:
        """获取退货地址ID"""
        try:
            # 尝试从外部传入的退货地址ID获取
            if hasattr(self, 'return_address_id') and self.return_address_id:
                print(f"[微信小店上传] 使用传入的退货地址ID: {self.return_address_id}")
                return int(self.return_address_id)

            # 默认退货地址ID
            print(f"[微信小店上传] 使用默认退货地址ID: 1")
            return 1

        except Exception as e:
            print(f"[微信小店上传] 获取退货地址ID失败: {e}")
            return 1

    def set_return_address_id(self, address_id: str):
        """设置退货地址ID"""
        self.return_address_id = address_id
        print(f"[微信小店上传] 设置退货地址ID: {address_id}")

    def set_shelf_status(self, status: int):
        """设置上架状态"""
        self.shelf_status = status
        print(f"[微信小店上传] 设置上架状态: {status}")

    def _get_shelf_status(self) -> int:
        """获取上架状态"""
        try:
            if hasattr(self, 'shelf_status'):
                print(f"[微信小店上传] 使用设置的上架状态: {self.shelf_status}")
                return self.shelf_status

            # 默认立即上架
            print(f"[微信小店上传] 使用默认上架状态: 1 (立即上架)")
            return 1

        except Exception as e:
            print(f"[微信小店上传] 获取上架状态失败: {e}")
            return 1

    def set_image_filter_settings(self, settings: Dict):
        """设置图片过滤设置"""
        self.image_filter_settings = settings
        main_settings = settings.get('main_image', {})
        detail_settings = settings.get('detail_image', {})
        print(f"[微信小店上传] 设置主图过滤: 删除前{main_settings.get('delete_before', 0)}张, 删除后{main_settings.get('delete_after', 0)}张")
        print(f"[微信小店上传] 设置详情图过滤: 删除前{detail_settings.get('delete_before', 0)}张, 删除后{detail_settings.get('delete_after', 0)}张")

    def set_skip_copy_settings(self, settings: Dict):
        """设置跳过复制设置"""
        self.skip_copy_settings = settings
        print(f"[微信小店上传] 设置跳过复制: 启用={settings.get('enabled', False)}, 关键词数量={len(settings.get('keywords', []))}")

    def should_skip_product(self, title: str) -> tuple:
        """检查商品是否应该跳过上传

        Returns:
            tuple: (是否跳过, 命中的关键词)
        """
        try:
            # 如果跳过复制功能未启用，不跳过
            if not self.skip_copy_settings.get('enabled', False):
                return False, None

            # 如果没有跳过关键词，不跳过
            skip_keywords = self.skip_copy_settings.get('keywords', [])
            if not skip_keywords:
                return False, None

            # 检查标题是否包含跳过关键词
            title_lower = title.lower()
            for keyword in skip_keywords:
                if keyword and keyword.lower() in title_lower:
                    print(f"[微信小店上传] 商品标题包含跳过关键词 '{keyword}': {title}")
                    return True, keyword

            return False, None

        except Exception as e:
            print(f"[微信小店上传] 检查跳过商品异常: {e}")
            return False, None

    def _filter_images(self, images: List[str], image_type: str = 'main_image') -> List[str]:
        """根据搬家设置过滤图片"""
        if not images:
            return images

        # 根据图片类型获取对应的过滤设置
        filter_settings = self.image_filter_settings.get(image_type, {})
        delete_before = filter_settings.get('delete_before', 0)
        delete_after = filter_settings.get('delete_after', 0)

        image_type_name = "主图" if image_type == 'main_image' else "详情图"
        print(f"[微信小店上传] {image_type_name}过滤前: {len(images)} 张")
        print(f"[微信小店上传] {image_type_name}过滤设置: 删除前{delete_before}张, 删除后{delete_after}张")

        # 应用过滤规则
        filtered_images = images[:]  # 复制列表

        # 删除前N张
        if delete_before > 0:
            if delete_before >= len(filtered_images):
                # 如果要删除的数量大于等于总数量，清空列表
                filtered_images = []
                print(f"[微信小店上传] {image_type_name}删除前{delete_before}张，原有{len(images)}张，全部删除，剩余: 0 张")
            else:
                filtered_images = filtered_images[delete_before:]
                print(f"[微信小店上传] {image_type_name}删除前{delete_before}张后剩余: {len(filtered_images)} 张")

        # 删除后N张
        if delete_after > 0 and len(filtered_images) > 0:
            if delete_after >= len(filtered_images):
                # 如果要删除的数量大于等于剩余数量，清空列表
                filtered_images = []
                print(f"[微信小店上传] {image_type_name}删除后{delete_after}张，剩余全部删除，最终剩余: 0 张")
            else:
                filtered_images = filtered_images[:-delete_after]
                print(f"[微信小店上传] {image_type_name}删除后{delete_after}张后剩余: {len(filtered_images)} 张")

        print(f"[微信小店上传] {image_type_name}过滤后: {len(filtered_images)} 张")

        # 输出过滤详情（仅显示前几张图片的URL用于调试）
        if len(images) != len(filtered_images):
            print(f"[微信小店上传] 过滤详情:")
            print(f"  原始图片: {[img[:50] + '...' for img in images[:3]]}{'...' if len(images) > 3 else ''}")
            print(f"  过滤后: {[img[:50] + '...' for img in filtered_images[:3]]}{'...' if len(filtered_images) > 3 else ''}")

        return filtered_images

    def _process_skus(self, cache_data: Dict, field_mapping: Dict) -> List[Dict]:
        """处理SKU信息 - 从缓存的 product_sku_infos 获取"""
        # 从缓存获取SKU信息 - 检查 product_info 层级
        if 'product_info' in cache_data:
            original_skus = cache_data['product_info'].get('product_sku_infos', [])
            print(f"[微信小店上传] 从 product_info.product_sku_infos 获取SKU")
        else:
            original_skus = cache_data.get('product_sku_infos', [])
            print(f"[微信小店上传] 从顶层 product_sku_infos 获取SKU")
        if not original_skus:
            # 如果没有SKU，创建默认SKU
            original_skus = [self._create_default_sku(cache_data)]
            print(f"[微信小店上传] 没有找到SKU信息，创建默认SKU")
        else:
            print(f"[微信小店上传] 找到 {len(original_skus)} 个SKU")

        processed_skus = []

        # 获取商品ID用于生成sku_code
        product_id = self._get_product_id_from_cache(cache_data)

        for i, sku in enumerate(original_skus):
            try:
                # 根据实际缓存结构获取SKU数据
                sku_id = sku.get('skuId', sku.get('sku_id', f'sku_{i}'))
                cargo_number = sku.get('cargoNumber', '')
                price = sku.get('consignPrice', sku.get('price', 0))
                stock = sku.get('amountOnSale', sku.get('stock', 999))

                # 生成sku_code：B_商品ID格式
                sku_code = f"B_{product_id}" if product_id else f"B_sku_{i}"

                processed_sku = {
                    'out_sku_id': str(sku_id),
                    'sale_price': int(float(price) * 100),  # 转换为分
                    'stock_num': int(stock),
                    'sku_code': sku_code,
                    'sku_deliver_info': {'stock_type': 0}
                }

                print(f"[微信小店上传] SKU {i+1} 设置sku_code: {sku_code}")

                # 处理SKU图片 - 从attributes中获取skuImageUrl
                sku_attrs = sku.get('attributes', [])
                sku_image_url = None
                for attr in sku_attrs:
                    if 'skuImageUrl' in attr and attr['skuImageUrl']:
                        sku_image_url = attr['skuImageUrl']
                        break

                if sku_image_url:
                    uploaded_sku_img = self._upload_single_image(sku_image_url)
                    if uploaded_sku_img:
                        processed_sku['thumb_img'] = uploaded_sku_img

                # 处理SKU属性 - 转换为微信格式（保持原有，不去重）
                if sku_attrs:
                    converted_attrs = []
                    for attr in sku_attrs:
                        if 'attributeName' in attr and 'attributeValue' in attr:
                            converted_attrs.append({
                                'attr_key': attr['attributeName'],
                                'attr_value': attr['attributeValue']
                            })
                    if converted_attrs:
                        processed_sku['sku_attrs'] = converted_attrs

                processed_skus.append(processed_sku)
                print(f"[微信小店上传] 处理SKU {i+1}: {processed_sku['out_sku_id']}, 价格: {processed_sku['sale_price']/100}元, 库存: {processed_sku['stock_num']}")

            except Exception as e:
                print(f"[微信小店上传] 处理SKU {i+1} 失败: {e}")
                continue

        if not processed_skus:
            # 如果所有SKU都处理失败，创建默认SKU
            # 优先使用计算后的价格
            default_price = 0

            # 尝试从 product_info.reference_price_calculated 获取计算后的价格
            if 'product_info' in cache_data and 'reference_price_calculated' in cache_data['product_info']:
                default_price = float(cache_data['product_info']['reference_price_calculated'])
                print(f"[微信小店上传] 使用计算后的价格创建默认SKU: {default_price}")
            # 如果没有计算后的价格，尝试从 product_info.reference_price 获取
            elif 'product_info' in cache_data and 'reference_price' in cache_data['product_info']:
                default_price = float(cache_data['product_info']['reference_price'])
                print(f"[微信小店上传] 使用reference_price创建默认SKU: {default_price}")
            # 最后尝试从顶层price字段获取
            else:
                default_price = float(cache_data.get('price', 0))
                print(f"[微信小店上传] 使用顶层price创建默认SKU: {default_price}")

            default_sku = {
                'out_sku_id': 'default',
                'sale_price': int(default_price * 100),
                'market_price': int(default_price * 100),
                'stock_num': 999,
                'sku_code': '',
                'barcode': '',
            }
            processed_skus.append(default_sku)
            print(f"[微信小店上传] 创建默认SKU，价格: {default_price}元")

        return processed_skus

    def _upload_images_batch(self, image_urls: List[str], max_workers: int = 20) -> List[str]:
        """批量上传图片 - 并发上传提高效率"""
        if not image_urls:
            return []

        uploaded_urls = []
        print(f"[微信小店上传] 开始并发上传 {len(image_urls)} 张图片，并发数: {max_workers}")

        # 使用线程池并发上传，但控制并发数避免触发频率限制
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_url = {
                executor.submit(self._upload_single_image, url): url
                for url in image_urls if url
            }

            for future in as_completed(future_to_url):
                original_url = future_to_url[future]
                try:
                    uploaded_url = future.result()
                    if uploaded_url:
                        uploaded_urls.append(uploaded_url)
                        print(f"[微信小店上传] 图片上传成功: {len(uploaded_urls)}/{len(image_urls)}")
                    else:
                        logger.warning(f"图片上传失败: {original_url}")
                        print(f"[微信小店上传] 图片上传失败: {original_url}")
                except Exception as e:
                    # 检查是否是access_token过期异常，如果是则重新抛出
                    if "ACCESS_TOKEN_EXPIRED" in str(e):
                        logger.error(f"图片上传检测到access_token过期: {original_url}, 错误: {e}")
                        print(f"[微信小店上传] 图片上传检测到access_token过期: {original_url}, 错误: {e}")
                        # 重新抛出异常，让上层处理
                        raise e
                    else:
                        logger.error(f"图片上传异常: {original_url}, 错误: {e}")
                        print(f"[微信小店上传] 图片上传异常: {original_url}, 错误: {e}")

                # 减少延迟，提高上传速度
                time.sleep(0.05)

        print(f"[微信小店上传] 并发上传完成，成功: {len(uploaded_urls)}/{len(image_urls)} 张")
        return uploaded_urls

    def _upload_single_image(self, image_url: str) -> Optional[str]:
        """上传单张图片"""
        if not image_url:
            return None

        # 检查缓存
        if image_url in self.image_cache:
            return self.image_cache[image_url]

        # 检查是否已经是微信图片链接
        if 'mmecimage.cn/p/' in image_url:
            self.image_cache[image_url] = image_url
            return image_url

        try:
            if not self.wechat_api:
                raise Exception("微信API未初始化")

            # 调用微信图片上传接口
            result = self.wechat_api.upload_image(image_url)

            if result and 'pic_file' in result and 'img_url' in result['pic_file']:
                uploaded_url = result['pic_file']['img_url']
                self.image_cache[image_url] = uploaded_url
                logger.info(f"图片上传成功: {image_url} -> {uploaded_url}")
                return uploaded_url
            else:
                # 检查是否是access_token过期错误（40001、42001或4001）
                if result and (result.get('errcode') == 40001 or result.get('errcode') == 42001 or result.get('errcode') == 4001):
                    error_code = result.get('errcode')
                    error_msg = result.get('errmsg', 'access_token expired')
                    logger.error(f"图片上传失败，access_token过期: 错误码{error_code}, {error_msg}")
                    # 抛出特定的异常，让上层处理
                    raise Exception(f"ACCESS_TOKEN_EXPIRED: 错误码{error_code}, {error_msg}")
                else:
                    logger.error(f"图片上传失败，返回结果异常: {result}")
                    return None

        except Exception as e:
            # 如果是access_token过期异常，重新抛出让上层处理
            if "ACCESS_TOKEN_EXPIRED" in str(e):
                raise e
            logger.error(f"图片上传异常: {image_url}, 错误: {e}")
            return None

    def _create_default_sku(self, cache_data: Dict) -> Dict:
        """创建默认SKU"""
        # 从正确的位置获取价格信息
        price = 0
        stock = 999

        if 'product_info' in cache_data:
            product_info = cache_data['product_info']

            # 从 product_sale_info 获取价格
            if 'product_sale_info' in product_info:
                sale_info = product_info['product_sale_info']
                price = sale_info.get('consignPrice', 0)
                stock = sale_info.get('amountOnSale', 999)

                # 如果库存为0，使用默认值999
                if stock <= 0:
                    stock = 999

            # 从 processed_sale_info 获取价格（备用）
            elif 'processed_sale_info' in product_info:
                sale_info = product_info['processed_sale_info']
                price = sale_info.get('consign_price', 0)
                stock = sale_info.get('amount_on_sale', 999)

                # 如果库存为0，使用默认值999
                if stock <= 0:
                    stock = 999

        # 生成唯一的SKU ID
        product_id = self._get_product_id_from_cache(cache_data)
        sku_id = f"sku_{product_id}" if product_id else "default_sku"

        return {
            'skuId': sku_id,
            'sku_id': sku_id,
            'consignPrice': price,
            'price': price,
            'market_price': price,
            'amountOnSale': stock,
            'stock': stock,
            'sku_code': '',
            'barcode': '',
            'attributes': []
        }

    def _get_product_id_from_cache(self, cache_data: Dict) -> str:
        """从缓存数据中获取商品ID"""
        if 'product_info' in cache_data:
            product_info = cache_data['product_info']
            return str(product_info.get('product_id', ''))
        elif 'product_id' in cache_data:
            return str(cache_data['product_id'])
        return ''

    def _convert_sku_attributes(self, attributes: List) -> List[Dict]:
        """转换SKU属性格式"""
        converted_attrs = []

        for attr in attributes:
            if isinstance(attr, dict):
                converted_attr = {
                    'attr_key': attr.get('name', ''),
                    'attr_value': attr.get('value', '')
                }
                converted_attrs.append(converted_attr)

        return converted_attrs

    def _apply_filter(self, value: str, filter_name: str) -> str:
        """应用数据过滤器"""
        processors = self.template_config.get('data_processors', {})

        if filter_name == 'remove_emoji' and filter_name in processors:
            regex_pattern = processors[filter_name]['regex']
            return re.sub(regex_pattern, '', str(value))
        elif filter_name == 'trim_spaces':
            return re.sub(r'\s+', ' ', str(value)).strip()

        return str(value)

    def _validate_product_data(self, product_data: Dict):
        """验证商品数据"""
        required_fields = ['title', 'head_imgs', 'skus', 'cats_v2']  # 微信小店使用 cats_v2 而不是 category_id

        for field in required_fields:
            if field not in product_data or not product_data[field]:
                raise ValueError(f"缺少必填字段: {field}")

        # 验证主图数据
        if not isinstance(product_data['head_imgs'], list) or len(product_data['head_imgs']) == 0:
            raise ValueError("主图数据不能为空")

        # 验证SKU数据
        if not isinstance(product_data['skus'], list) or len(product_data['skus']) == 0:
            raise ValueError("SKU数据不能为空")

        for sku in product_data['skus']:
            if not sku.get('out_sku_id') or sku.get('sale_price', 0) <= 0:
                raise ValueError("SKU数据不完整")

    def upload_product_to_wechat(self, product_data: Dict) -> Dict:
        """上传商品到微信小店"""
        try:
            if not self.wechat_api:
                raise Exception("微信API未初始化")

            logger.info(f"开始上传商品到微信小店: {product_data.get('title')}")

            # 打印提交的数据用于调试
            print(f"[微信小店上传] 准备提交的商品数据:")
            print(f"  标题: {product_data.get('title', 'N/A')}")
            print(f"  短标题: {product_data.get('short_title', 'N/A')}")
            print(f"  主图数量: {len(product_data.get('head_imgs', []))}")

            # 重点检查类目信息
            cats_v2 = product_data.get('cats_v2', [])
            print(f"  类目信息 cats_v2: {cats_v2}")
            print(f"  类目数量: {len(cats_v2)}")
            for i, cat in enumerate(cats_v2):
                print(f"    类目{i+1}: {cat}")

            print(f"  SKU数量: {len(product_data.get('skus', []))}")
            print(f"  属性数量: {len(product_data.get('attrs', []))}")
            print(f"  发货方式: {product_data.get('deliver_method', 'N/A')}")
            print(f"  品牌ID: {product_data.get('brand_id', 'N/A')}")
            print(f"  售后服务: {product_data.get('extra_service', 'N/A')}")

            # 完整的JSON数据（用于调试）
            import json
            print(f"[微信小店上传] 完整提交数据:")
            print(json.dumps(product_data, ensure_ascii=False, indent=2))

            # 调用微信添加商品接口，支持token过期重试
            result = self._upload_with_token_retry(product_data)

            if result and result.get('errcode') == 0:
                logger.info(f"商品上传成功: {product_data.get('title')}")
                # 从data字段中获取product_id
                data = result.get('data', {})
                product_id = data.get('product_id', '')
                print(f"[微信小店上传] 获取到微信商品ID: {product_id}")
                return {
                    'success': True,
                    'product_id': product_id,
                    'message': '上传成功'
                }
            else:
                error_msg = result.get('errmsg', '未知错误') if result else '接口调用失败'
                logger.error(f"商品上传失败: {error_msg}")
                return {
                    'success': False,
                    'error_code': result.get('errcode') if result else -1,
                    'message': error_msg
                }

        except Exception as e:
            logger.error(f"上传商品异常: {e}")
            return {
                'success': False,
                'message': f'上传异常: {str(e)}'
            }

    def _upload_with_token_retry(self, product_data: Dict, max_retries: int = 2) -> Dict:
        """
        带token刷新重试的商品上传方法

        Args:
            product_data: 商品数据
            max_retries: 最大重试次数

        Returns:
            dict: 上传结果
        """
        for attempt in range(max_retries + 1):
            try:
                # 调用微信添加商品接口
                result = self.wechat_api.add_product(product_data)

                # 检查是否是access_token过期错误（40001、42001或4001）
                if result and result.get('errcode') in [40001, 42001, 4001]:
                    error_code = result.get('errcode')
                    error_msg = result.get('errmsg', 'access_token expired')

                    if attempt < max_retries:
                        print(f"[微信小店上传] 检测到access_token过期（错误码{error_code}），尝试刷新token并重试（第{attempt+1}次）")
                        logger.warning(f"商品上传检测到access_token过期，尝试刷新token并重试: 错误码{error_code}, {error_msg}")

                        # 刷新access_token
                        if self._refresh_access_token():
                            print(f"[微信小店上传] access_token刷新成功，继续重试上传")
                            continue
                        else:
                            print(f"[微信小店上传] access_token刷新失败，停止重试")
                            return {
                                'errcode': error_code,
                                'errmsg': f'access_token刷新失败: {error_msg}'
                            }
                    else:
                        print(f"[微信小店上传] 已达到最大重试次数，停止重试")
                        return result
                else:
                    # 非token过期错误或成功，直接返回
                    return result

            except Exception as e:
                logger.error(f"商品上传重试过程中出现异常: {e}")
                if attempt < max_retries:
                    print(f"[微信小店上传] 上传异常，重试第{attempt+1}次: {e}")
                    continue
                else:
                    return {
                        'errcode': -1,
                        'errmsg': f'上传异常: {str(e)}'
                    }

        # 理论上不会到达这里
        return {
            'errcode': -1,
            'errmsg': '未知错误：重试循环异常结束'
        }

    def _refresh_access_token(self) -> bool:
        """
        刷新access_token

        Returns:
            bool: 是否刷新成功
        """
        try:
            # 获取当前店铺信息
            current_shop = self._get_selected_shop()
            if not current_shop:
                print(f"[微信小店上传] 无法获取当前店铺信息，无法刷新token")
                return False

            appid = current_shop.get('appid')
            secret = current_shop.get('secret')

            if not appid or not secret:
                print(f"[微信小店上传] 店铺缺少appid或secret，无法刷新token")
                return False

            print(f"[微信小店上传] 开始刷新access_token，appid: {appid}")

            # 调用微信API刷新token
            refresh_result = self.wechat_api.refresh_token(appid, secret)

            if refresh_result and refresh_result.get('success'):
                new_access_token = refresh_result.get('access_token')
                if new_access_token:
                    # 更新微信API中的access_token
                    self.wechat_api.access_token = new_access_token
                    print(f"[微信小店上传] access_token刷新成功")

                    # 更新配置文件中的access_token
                    self._update_shop_access_token(current_shop.get('店铺名称'), new_access_token)

                    return True
                else:
                    print(f"[微信小店上传] 刷新结果中没有access_token")
                    return False
            else:
                error_msg = refresh_result.get('errmsg', '未知错误') if refresh_result else '刷新请求失败'
                print(f"[微信小店上传] access_token刷新失败: {error_msg}")
                return False

        except Exception as e:
            print(f"[微信小店上传] 刷新access_token异常: {e}")
            logger.error(f"刷新access_token异常: {e}")
            return False

    def _update_shop_access_token(self, shop_name: str, new_access_token: str):
        """
        更新配置文件中的access_token

        Args:
            shop_name: 店铺名称
            new_access_token: 新的access_token
        """
        try:
            config_file = "config/账号列表.json"
            if not os.path.exists(config_file):
                print(f"[微信小店上传] 配置文件不存在，无法更新token: {config_file}")
                return

            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 查找并更新对应店铺的access_token
            for shop in config_data:
                if shop.get('店铺名称') == shop_name:
                    shop['accesstoken'] = new_access_token
                    print(f"[微信小店上传] 已更新店铺 {shop_name} 的access_token")
                    break

            # 保存更新后的配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)

            print(f"[微信小店上传] 配置文件已更新")

        except Exception as e:
            print(f"[微信小店上传] 更新配置文件中的access_token失败: {e}")
            logger.error(f"更新配置文件中的access_token失败: {e}")

    def _filter_description_images(self, description: str) -> str:
        """
        过滤商品详情描述中的广告图片

        过滤规则：
        1. 跳过table标签内的所有图片（通常是广告图片）
        2. 跳过带有超链接href属性的图片（通常是广告图片）

        Parameters:
        -----------
        description: str
            原始商品详情描述HTML

        Returns:
        --------
        str:
            过滤后的商品详情描述HTML
        """
        if not description:
            return description

        try:
            import re

            # 记录过滤前的图片数量
            original_img_count = len(re.findall(r'<img[^>]*>', description, re.IGNORECASE))

            # 1. 移除table标签内的所有内容（包括图片）
            # 使用非贪婪匹配，支持嵌套table
            table_pattern = r'<table[^>]*>.*?</table>'
            description = re.sub(table_pattern, '', description, flags=re.IGNORECASE | re.DOTALL)

            # 2. 移除带有href属性的img标签（通常包裹在a标签内）
            # 匹配 <a href="..."><img ...></a> 模式
            href_img_pattern = r'<a[^>]*href[^>]*>.*?<img[^>]*>.*?</a>'
            description = re.sub(href_img_pattern, '', description, flags=re.IGNORECASE | re.DOTALL)

            # 3. 移除单独的带有onclick等链接属性的img标签
            # 匹配包含onclick、href、data-href等链接属性的img标签
            link_img_pattern = r'<img[^>]*(?:onclick|href|data-href)[^>]*>'
            description = re.sub(link_img_pattern, '', description, flags=re.IGNORECASE)

            # 记录过滤后的图片数量
            filtered_img_count = len(re.findall(r'<img[^>]*>', description, re.IGNORECASE))

            if original_img_count != filtered_img_count:
                print(f"[微信小店上传] 图片过滤: 原始{original_img_count}张, 过滤后{filtered_img_count}张, 移除{original_img_count - filtered_img_count}张广告图片")

            return description

        except Exception as e:
            print(f"[微信小店上传] 过滤description图片时出错: {e}")
            return description


class WechatStoreUploadIntegration:
    """微信小店上传功能集成类 - 用于与商品复制界面集成"""

    def __init__(self, product_table, shop_combo, copy_btn):
        """
        初始化集成类

        Args:
            product_table: 商品列表表格
            shop_combo: 店铺选择下拉框
            copy_btn: 复制按钮
        """
        self.product_table = product_table
        self.shop_combo = shop_combo
        self.copy_btn = copy_btn
        self.processor = WechatStoreUploadProcessor()

        # 连接复制按钮事件
        self.copy_btn.clicked.connect(self.on_copy_to_wechat_clicked)

    def _count_valid_products(self):
        """统计表格中有效的商品数量（检查序号列）"""
        try:
            total_rows = self.product_table.rowCount()
            valid_count = 0

            # 检查序号列（第0列），有序号就是有数据
            for row in range(total_rows):
                seq_item = self.product_table.item(row, 0)  # 序号列
                if seq_item and seq_item.text().strip():
                    valid_count += 1

            print(f"[微信小店上传] 表格总行数: {total_rows}, 有效商品数: {valid_count}")
            return valid_count

        except Exception as e:
            print(f"[微信小店上传] 统计有效商品数量异常: {e}")
            return 0

    def on_copy_to_wechat_clicked(self):
        """复制按钮点击事件 - 上传商品到微信小店"""
        try:
            from PyQt5.QtWidgets import QMessageBox, QApplication
            from PyQt5.QtGui import QColor
            from PyQt5.QtWidgets import QTableWidgetItem

            print("[微信小店上传] 开始处理商品上传...")

            # 检查是否有选中的店铺
            current_shop = self._get_selected_shop()
            if not current_shop:
                QMessageBox.warning(None, "警告", "请先选择目标店铺")
                return

            print(f"[微信小店上传] 选中的店铺: {current_shop.get('店铺名称', 'Unknown')}")
            print(f"[微信小店上传] 店铺状态: {current_shop.get('状态', 'Unknown')}")
            print(f"[微信小店上传] ACCESS_TOKEN: {current_shop.get('accesstoken', 'None')[:20]}...")

            # 检查商品表格是否有有效数据
            valid_product_count = self._count_valid_products()
            if valid_product_count == 0:
                QMessageBox.warning(None, "警告", "商品列表为空，请先采集商品数据")
                return

            # 确认对话框
            reply = QMessageBox.question(
                None, "确认上传",
                f"确定要将商品列表中的商品上传到 {current_shop.get('店铺名称', 'Unknown')} 吗？\n\n"
                f"有效商品数量: {valid_product_count} 个",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 初始化处理器
            self.processor.set_wechat_api(
                access_token=current_shop.get('accesstoken'),
                appid=current_shop.get('appid')
            )

            # 禁用复制按钮，防止重复点击
            self.copy_btn.setEnabled(False)
            self.copy_btn.setText("上传中...")

            # 开始逐行处理商品
            self._process_products_to_wechat()

        except Exception as e:
            print(f"[微信小店上传] 处理异常: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(None, "错误", f"上传处理异常: {str(e)}")
            # 恢复按钮状态
            self.copy_btn.setEnabled(True)
            self.copy_btn.setText("复制")

    def _process_products_to_wechat(self):
        """逐行处理商品并上传到微信小店"""
        try:
            from PyQt5.QtWidgets import QApplication, QMessageBox, QTableWidgetItem
            from PyQt5.QtGui import QColor

            total_rows = self.product_table.rowCount()
            valid_product_count = self._count_valid_products()
            success_count = 0
            failed_count = 0
            processed_count = 0

            print(f"[微信小店上传] 开始处理 {valid_product_count} 个有效商品（总行数: {total_rows}）...")

            for row in range(total_rows):
                try:
                    # 检查序号列是否有内容，没有序号就跳过
                    seq_item = self.product_table.item(row, 0)  # 序号列
                    if not (seq_item and seq_item.text().strip()):
                        # 跳过空行
                        continue

                    processed_count += 1

                    # 更新状态显示
                    self.copy_btn.setText(f"上传中 {processed_count}/{valid_product_count}")
                    QApplication.processEvents()  # 更新界面

                    # 获取商品数据
                    product_data = self._get_product_data_from_row(row)
                    if not product_data:
                        print(f"[微信小店上传] 第 {row+1} 行商品数据无效，跳过")
                        failed_count += 1
                        continue

                    print(f"[微信小店上传] 处理第 {row+1} 行商品: {product_data.get('title', 'Unknown')}")

                    # 转换数据格式
                    try:
                        converted_data = self.processor.process_product_data(
                            cache_data=product_data,
                            category_id=1001,  # 默认类目ID，可以根据需要调整
                            freight_template_id=1  # 默认运费模板ID
                        )
                    except Exception as convert_error:
                        # 检查是否是ACCESS_TOKEN_EXPIRED错误
                        if "ACCESS_TOKEN_EXPIRED" in str(convert_error):
                            print(f"[微信小店上传] 第 {row+1} 行商品数据转换时检测到access_token过期，已自动处理")
                            # 重新尝试转换
                            converted_data = self.processor.process_product_data(
                                cache_data=product_data,
                                category_id=1001,
                                freight_template_id=1
                            )
                        else:
                            raise convert_error

                    # 上传到微信小店
                    upload_result = self.processor.upload_product_to_wechat(converted_data)

                    if upload_result.get('success'):
                        print(f"[微信小店上传] 第 {row+1} 行商品上传成功")
                        success_count += 1
                        self._update_row_upload_status(row, "上传成功", "#4CAF50")
                    else:
                        error_msg = upload_result.get('message', '未知错误')
                        print(f"[微信小店上传] 第 {row+1} 行商品上传失败: {error_msg}")
                        failed_count += 1
                        self._update_row_upload_status(row, f"上传失败: {error_msg}", "#F44336")

                except Exception as e:
                    print(f"[微信小店上传] 处理第 {row+1} 行商品异常: {e}")
                    failed_count += 1
                    self._update_row_upload_status(row, f"处理异常: {str(e)}", "#FF9800")

            # 显示完成结果
            QMessageBox.information(
                None, "上传完成",
                f"商品上传完成！\n\n"
                f"有效商品: {valid_product_count} 个\n"
                f"成功: {success_count}\n"
                f"失败: {failed_count}"
            )

            print(f"[微信小店上传] 上传完成 - 有效商品: {valid_product_count}, 成功: {success_count}, 失败: {failed_count}")

        except Exception as e:
            print(f"[微信小店上传] 批量处理异常: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(None, "错误", f"批量处理异常: {str(e)}")

        finally:
            # 恢复按钮状态
            self.copy_btn.setEnabled(True)
            self.copy_btn.setText("复制")

    def _get_selected_shop(self):
        """获取当前选中的店铺信息"""
        try:
            # 获取当前选中的店铺名称
            current_text = self.shop_combo.currentText().strip()
            print(f"[微信小店上传] 当前选中的店铺文本: '{current_text}'")

            # 检查是否是有效的店铺选择
            if not current_text or current_text in ["全部店铺", "正在加载店铺列表...", "--无法获取店铺列表--"] or current_text.startswith("--"):
                print(f"[微信小店上传] 无效的店铺选择: '{current_text}'")
                return None

            # 从配置文件中查找匹配的店铺信息
            config_file = "config/账号列表.json"
            if not os.path.exists(config_file):
                print(f"[微信小店上传] 配置文件不存在: {config_file}")
                return None

            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    shops = json.load(f)

                print(f"[微信小店上传] 从配置文件加载了 {len(shops)} 个店铺")

                # 精确匹配店铺名称
                for shop in shops:
                    shop_name = shop.get('店铺名称', '').strip()
                    if shop_name == current_text:
                        print(f"[微信小店上传] 精确匹配到店铺: {shop_name}")
                        print(f"[微信小店上传] 店铺状态: {shop.get('状态', 'Unknown')}")
                        print(f"[微信小店上传] AppID: {shop.get('appid', 'Unknown')}")

                        # 验证必要字段
                        access_token = shop.get('accesstoken', '')
                        if access_token:
                            print(f"[微信小店上传] ACCESS_TOKEN: {access_token[:20]}...{access_token[-10:]}")
                        else:
                            print(f"[微信小店上传] ⚠️ ACCESS_TOKEN为空")

                        return shop

                # 如果精确匹配失败，尝试模糊匹配
                print(f"[微信小店上传] 精确匹配失败，尝试模糊匹配...")
                for shop in shops:
                    shop_name = shop.get('店铺名称', '').strip()
                    if shop_name and (shop_name in current_text or current_text in shop_name):
                        print(f"[微信小店上传] 模糊匹配到店铺: {shop_name}")
                        return shop

                # 如果都没有匹配到，提供可选的店铺列表
                print(f"[微信小店上传] 未找到匹配的店铺")
                print(f"[微信小店上传] 可用的店铺列表:")
                for i, shop in enumerate(shops[:5]):  # 只显示前5个
                    print(f"  {i+1}. {shop.get('店铺名称', 'Unknown')} ({shop.get('状态', 'Unknown')})")

                return None

            except Exception as e:
                print(f"[微信小店上传] 读取配置文件失败: {e}")
                return None

        except Exception as e:
            print(f"[微信小店上传] 获取选中店铺异常: {e}")
            return None

    def _get_product_data_from_row(self, row):
        """从表格行获取商品数据"""
        try:
            # 优先从原链接列（第7列）提取商品ID
            link_item = self.product_table.item(row, 7)  # 原链接在第7列
            product_id = None

            if link_item:
                link = link_item.text().strip()
                if link:
                    # 从1688链接中提取商品ID
                    import re
                    patterns = [
                        r'offer/(\d+)\.html',  # https://detail.1688.com/offer/785500552359.html
                        r'offerId=(\d+)',      # 其他格式的链接
                        r'/(\d+)\.html'        # 通用数字ID格式
                    ]

                    for pattern in patterns:
                        match = re.search(pattern, link)
                        if match:
                            product_id = match.group(1)
                            print(f"[微信小店上传] 第 {row+1} 行从原链接提取商品ID: {product_id}")
                            break

            # 如果从链接提取失败，尝试从商品ID列（第8列）获取
            if not product_id:
                product_id_item = self.product_table.item(row, 8)  # 商品ID在第8列
                if product_id_item:
                    product_id = product_id_item.text().strip()
                    if product_id:
                        print(f"[微信小店上传] 第 {row+1} 行从商品ID列获取: {product_id}")

            if not product_id:
                print(f"[微信小店上传] 第 {row+1} 行无法获取商品ID")
                return None

            # 从表格获取标题（第2列）
            title_item = self.product_table.item(row, 2)
            title = title_item.text() if title_item else "Unknown"
            print(f"[微信小店上传] 第 {row+1} 行标题: {title}")

            # 从表格获取类目ID（第4列）
            category_item = self.product_table.item(row, 4)
            category_id = 1001  # 默认类目ID
            if category_item:
                category_text = category_item.text().strip()
                if category_text:
                    try:
                        # 解析类目ID，可能是逗号分隔的多个ID：10000212,10000213,7383
                        category_ids = [id.strip() for id in category_text.split(',') if id.strip()]
                        if category_ids:
                            category_id = int(category_ids[0])  # 使用第一个类目ID
                            print(f"[微信小店上传] 第 {row+1} 行类目ID: {category_id}")
                    except Exception as e:
                        print(f"[微信小店上传] 解析类目ID失败: {e}")

            # 尝试从缓存文件读取完整数据
            cache_file_path = f"config/pdcache/{product_id}.json"
            if os.path.exists(cache_file_path):
                try:
                    with open(cache_file_path, 'r', encoding='utf-8') as f:
                        cache_data = json.load(f)
                    print(f"[微信小店上传] 成功从缓存读取商品数据: {product_id}")

                    # 使用表格中的标题和类目ID覆盖缓存数据
                    cache_data['title'] = title
                    cache_data['category_id'] = category_id

                    return cache_data
                except Exception as e:
                    print(f"[微信小店上传] 读取缓存文件失败 {cache_file_path}: {e}")

            # 如果缓存文件不存在，从表格数据构建基础商品数据
            print(f"[微信小店上传] 缓存文件不存在，从表格构建数据: {product_id}")

            # 获取价格（第5列）
            price = 0
            market_price = 0
            price_item = self.product_table.item(row, 5)
            if price_item:
                price_text = price_item.text()
                try:
                    # 解析价格范围，如 "8.6~17.5"
                    if '~' in price_text:
                        prices = price_text.split('~')
                        if len(prices) >= 2:
                            price = float(prices[0])
                            market_price = float(prices[1])
                    else:
                        price = float(price_text)
                        market_price = price
                except:
                    price = 0
                    market_price = 0

            # 构建基础商品数据
            product_data = {
                'title': title,
                'category_id': category_id,
                'description': f'商品ID: {product_id}',
                'price': price,
                'market_price': market_price,
                'stock': 999,
                'product_image': [],  # 主图
                'product_sku_infos': [{
                    'sku_id': f'{product_id}_default',
                    'price': price,
                    'market_price': market_price,
                    'stock': 999,
                    'sku_code': '',
                    'barcode': '',
                    'attributes': []
                }],
                'product_attribute': [],  # 属性
                'original_link': link_item.text() if link_item else "",
                'product_id': product_id
            }

            return product_data

        except Exception as e:
            print(f"[微信小店上传] 获取第 {row+1} 行商品数据异常: {e}")
            return None

    def _update_row_upload_status(self, row, status, color):
        """更新表格行的上传状态"""
        try:
            from PyQt5.QtWidgets import QTableWidgetItem
            from PyQt5.QtGui import QColor

            # 在最后一列显示上传状态
            col_count = self.product_table.columnCount()

            # 如果没有状态列，添加一列
            if col_count < 14:  # 假设状态列是第14列
                self.product_table.setColumnCount(col_count + 1)
                self.product_table.setHorizontalHeaderItem(col_count, QTableWidgetItem("上传状态"))

            # 设置状态文本和颜色
            status_item = QTableWidgetItem(status)
            status_item.setBackground(QColor(color))
            self.product_table.setItem(row, col_count, status_item)

        except Exception as e:
            print(f"[微信小店上传] 更新第 {row+1} 行状态异常: {e}")


def integrate_wechat_upload(product_table, shop_combo, copy_btn):
    """
    集成微信小店上传功能到现有界面

    Args:
        product_table: 商品列表表格
        shop_combo: 店铺选择下拉框
        copy_btn: 复制按钮

    Returns:
        WechatStoreUploadIntegration: 集成实例
    """
    return WechatStoreUploadIntegration(product_table, shop_combo, copy_btn)




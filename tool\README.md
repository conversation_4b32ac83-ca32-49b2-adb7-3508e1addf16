# 工具目录说明

## ChromeDriver 配置

### 下载 ChromeDriver
1. 访问 https://chromedriver.chromium.org/
2. 下载与您的Chrome版本匹配的ChromeDriver
3. 将下载的文件放在以下任一位置：
   - `tool/chromedriver.exe` (Windows)
   - `tool/chromedriver` (Linux/Mac)
   - `tool/drivers/chromedriver.exe`
   - `tool/selenium/chromedriver.exe`

### 检查Chrome版本
1. 打开Chrome浏览器
2. 访问 `chrome://version/`
3. 查看版本号，下载对应版本的ChromeDriver

### 自动下载脚本
运行 `python download_chromedriver.py` 自动下载匹配的ChromeDriver

## 其他工具
- 可以在此目录放置其他浏览器相关工具
- 所有工具都会优先使用本地版本，避免网络下载
